import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react({
    // 启用快速刷新
    fastRefresh: true,
    // 减少babel转换
    babel: {
      plugins: []
    }
  })],
  server: {
    port: 9000,
    open: true,
    // 启用HMR优化
    hmr: {
      overlay: false
    }
  },
  // 优化构建
  optimizeDeps: {
    include: ['react', 'react-dom', 'react-router-dom']
  },
  // 减少bundle大小
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          router: ['react-router-dom']
        }
      }
    }
  }
})
