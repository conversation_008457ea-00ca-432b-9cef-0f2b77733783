import React, { memo, useCallback } from 'react'

const ChapterCard = memo(({ chapter, isActive, onClick, index, themeColors }) => {
  const handleClick = useCallback(() => {
    onClick(chapter)
  }, [onClick, chapter])

  return (
    <div
      className={`
        cursor-pointer p-4 rounded-lg transition-all duration-200 fade-in hover-lift border
        ${isActive
          ? `solid-card ${themeColors.border} ${themeColors.light}`
          : `solid-card border-slate-200 ${themeColors.hover}`
        }
      `}
      onClick={handleClick}
      style={{ animationDelay: `${index * 30}ms` }}
    >
      <div className="flex items-center space-x-3">
        {/* 章节编号 */}
        <div className={`
          w-10 h-10 rounded-lg flex items-center justify-center text-sm font-bold
          ${isActive
            ? `${themeColors.primary} text-white`
            : 'bg-slate-100 text-slate-700'
          }
        `}>
          {chapter.id}
        </div>

        {/* 章节信息 */}
        <div className="flex-1 min-w-0">
          <h4 className={`
            font-semibold text-sm truncate
            ${isActive ? 'text-slate-800' : 'text-slate-800'}
          `}>
            {chapter.title}
          </h4>
          <p className={`
            text-xs mt-1 line-clamp-2
            ${isActive ? themeColors.text : 'text-slate-600'}
          `}>
            {chapter.description}
          </p>
        </div>
      </div>
    </div>
  )
})

ChapterCard.displayName = 'ChapterCard'

export default ChapterCard
