import React, { memo } from 'react'
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import HomePage from './pages/HomePage'
import ChapterPage from './pages/ChapterPage'
import './styles/index.css'

const App = memo(() => {
  return (
    <Router>
      <div className="min-h-screen textured-bg">
        <Routes>
          <Route path="/" element={<HomePage />} />
          <Route path="/course/:courseId" element={<ChapterPage />} />
        </Routes>
      </div>
    </Router>
  )
})

App.displayName = 'App'

export default App
