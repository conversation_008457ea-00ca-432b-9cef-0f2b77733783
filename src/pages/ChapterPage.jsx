import React, { useState, useEffect, useCallback, useMemo, memo } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import ChapterCard from '../components/ChapterCard'
import coursesData from '../data/courses.json'

const ChapterPage = memo(() => {
  const { courseId } = useParams()
  const navigate = useNavigate()
  const [selectedChapter, setSelectedChapter] = useState(null)

  const course = useMemo(() => {
    return coursesData.courses.find(c => c.id === courseId)
  }, [courseId])

  // 获取主题颜色配置
  const getThemeColors = (theme) => {
    const themes = {
      blue: {
        primary: 'bg-blue-600',
        light: 'bg-blue-50',
        border: 'border-blue-600',
        text: 'text-blue-600',
        hover: 'hover:border-blue-300'
      },
      purple: {
        primary: 'bg-purple-600',
        light: 'bg-purple-50',
        border: 'border-purple-600',
        text: 'text-purple-600',
        hover: 'hover:border-purple-300'
      },
      green: {
        primary: 'bg-green-600',
        light: 'bg-green-50',
        border: 'border-green-600',
        text: 'text-green-600',
        hover: 'hover:border-green-300'
      },
      orange: {
        primary: 'bg-orange-600',
        light: 'bg-orange-50',
        border: 'border-orange-600',
        text: 'text-orange-600',
        hover: 'hover:border-orange-300'
      }
    }
    return themes[theme] || themes.blue
  }

  const themeColors = course ? getThemeColors(course.theme) : getThemeColors('blue')
  const defaultThemeColors =  getThemeColors('blue')

  useEffect(() => {
    if (course && course.chapters.length > 0) {
      setSelectedChapter(course.chapters[0]) // 默认选择第一个章节
    }
  }, [course])

  const handleChapterSelect = useCallback((chapter) => {
    setSelectedChapter(chapter)
  }, [])

  const handleBackToHome = useCallback(() => {
    navigate('/')
  }, [navigate])

  if (!course) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-600">加载中...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen flex flex-col lg:flex-row bg-slate-50 p-2">
      {/* 左侧：课程卡片区域 */}
      <div className="lg:w-82 lg:min-h-screen bg-white border-r border-slate-200 fade-in solid-card-elevated border-l-4 mr-2 h-screen overflow-y-auto">
        <div className="p-6 h-full overflow-y-auto scrollbar-hide">
          {/* 返回按钮 */}
          <button
            onClick={handleBackToHome}
            className={`group relative overflow-hidden bg-blue-600 hover:opacity-90 px-6 py-4 rounded-xl mb-6 transition-all duration-300 hover-lift w-full shadow-lg hover:shadow-xl`}
          >
            {/* 背景渐变效果 */}
            <div className="absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

            {/* 按钮内容 */}
            <div className="relative flex items-center justify-center space-x-3 text-white font-semibold">
              <div className="w-8 h-8 bg-white/20 rounded-full flex items-center justify-center backdrop-blur-sm group-hover:bg-white/30 transition-colors duration-300">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
              </div>
              <span className="text-lg">返回首页</span>
            </div>

            {/* 装饰性光效 */}
            {/*<div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-white/40 via-white/60 to-white/40 transform -translate-x-full group-hover:translate-x-full transition-transform duration-700"></div>*/}
          </button>

          {/* 课程信息卡片 */}
          <div className={`solid-card p-6 mb-6 slide-up border-t-4 bg-blue-600`} style={{ animationDelay: '50ms' }}>
            <h2 className="text-lg font-bold text-slate-800 mb-2">{course.title}</h2>
            <p className="text-slate-600 text-sm leading-relaxed">{course.description}</p>
          </div>

          {/* 章节列表 */}
          <div>
            <h3 className="text-lg font-semibold text-slate-800 mb-4">章节列表</h3>
            <div className="space-y-3">
              {course.chapters.map((chapter, index) => (
                <ChapterCard
                  key={chapter.id}
                  chapter={chapter}
                  index={index}
                  isActive={selectedChapter?.id === chapter.id}
                  onClick={() => handleChapterSelect(chapter)}
                  themeColors={defaultThemeColors}
                />
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* 右侧：章节内容区域 */}
        {/* 右侧：章节内容区域 */}
        <div className="flex-1 fade-in min-h-screen mr-1 flex flex-col" style={{ animationDelay: '100ms' }}>
            {/* 全屏高，垂直排列 */}
            <div className="h-screen overflow-y-auto scrollbar-hide flex flex-col">
                {selectedChapter ? (
                    <div key={selectedChapter.id} className="fade-in flex flex-col flex-1 min-h-0">
                        {/* 章节标题 - 固定在顶部 */}
                        <div className="sticky z-10 solid-card-elevated p-4 border-l-4 border-blue-600 bg-white/95 backdrop-blur-sm border-b border-slate-200 mb-1 top-0">
                            <div className="flex items-center space-x-4">
                                <div className="w-12 h-12 bg-blue-600 text-white rounded-lg flex items-center justify-center font-bold shadow-lg">
                                    {selectedChapter.id}
                                </div>
                                <div>
                                    <h2 className="font-bold text-slate-800">{selectedChapter.title}</h2>
                                    <p className="text-slate-600 text-sm">{selectedChapter.description}</p>
                                </div>
                            </div>
                        </div>

                        {/* iframe 内容区域 - 自动填满剩余空间 */}
                        <div className="flex-1 w-full bg-white solid-card-elevated border-l-4 overflow-hidden">
                            <iframe
                                src={selectedChapter.htmlFile}
                                className="w-full h-full border-0"
                                title={selectedChapter.title}
                                loading="lazy"
                                onError={() => {
                                    console.log('HTML文件加载失败，显示默认内容')
                                }}
                            />
                        </div>
                    </div>
                ) : (
                    <div className="flex items-center justify-center flex-1">
                        <p className="text-gray-600">请选择一个章节</p>
                    </div>
                )}
            </div>
        </div>
    </div>
  )
})

ChapterPage.displayName = 'ChapterPage'

export default ChapterPage
