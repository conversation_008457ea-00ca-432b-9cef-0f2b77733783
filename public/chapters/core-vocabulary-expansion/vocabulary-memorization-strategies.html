<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>词汇记忆策略</title>
    <script src="/static/tailwindcss-3.4.17.js"></script>
    <style>
        .keyword { color: #3d74ed; font-weight: bold; }
        body { font-family: 'Microsoft YaHei', sans-serif; }
    </style>
</head>
<body class="bg-white">
    <div class="p-6">
        <!-- 联想记忆法 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">联想记忆法</h2>
            <p class="mb-4 text-gray-700">联想记忆法是通过建立新词汇与已知概念之间的联系来加强记忆。这种方法利用大脑的自然联想能力，将抽象的词汇转化为具体的图像或概念。</p>
            
            <h3 class="text-xl font-semibold mb-3 text-gray-800">形象联想</h3>
            <p class="mb-4 text-gray-700">将单词的形状、发音或含义与具体的图像联系起来。</p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">形象联想</div>
                    <div class="keyword text-lg mb-1">elephant</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈeləfənt/</div>
                    <div class="text-gray-700">大象（想象巨大的身躯）</div>
                </div>
                
                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">形象联想</div>
                    <div class="keyword text-lg mb-1">butterfly</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈbʌtərflaɪ/</div>
                    <div class="text-gray-700">蝴蝶（想象翩翩起舞）</div>
                </div>
                
                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">形象联想</div>
                    <div class="keyword text-lg mb-1">mountain</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈmaʊntən/</div>
                    <div class="text-gray-700">山（想象高耸入云）</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">谐音联想</h3>
            <p class="mb-4 text-gray-700">利用单词发音与中文谐音的相似性来记忆。</p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">谐音联想</div>
                    <div class="keyword text-lg mb-1">ambulance</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈæmbjələns/</div>
                    <div class="text-gray-700">救护车（俺不能死）</div>
                </div>
                
                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">谐音联想</div>
                    <div class="keyword text-lg mb-1">pest</div>
                    <div class="text-sm text-gray-600 mb-1">/pest/</div>
                    <div class="text-gray-700">害虫（拍死它）</div>
                </div>
                
                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">谐音联想</div>
                    <div class="keyword text-lg mb-1">economy</div>
                    <div class="text-sm text-gray-600 mb-1">/ɪˈkɑːnəmi/</div>
                    <div class="text-gray-700">经济（依靠农民）</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">词根联想</h3>
            <p class="mb-4 text-gray-700">通过词根、前缀、后缀的组合来理解和记忆单词。</p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">词根联想</div>
                    <div class="keyword text-lg mb-1">predict</div>
                    <div class="text-sm text-gray-600 mb-1">/prɪˈdɪkt/</div>
                    <div class="text-gray-700">预测（pre-提前 + dict-说）</div>
                </div>
                
                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">词根联想</div>
                    <div class="keyword text-lg mb-1">transport</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈtrænspɔːrt/</div>
                    <div class="text-gray-700">运输（trans-转移 + port-携带）</div>
                </div>
            </div>
        </section>

        <!-- 词汇卡片记忆法 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">词汇卡片记忆法</h2>
            <p class="mb-4 text-gray-700">词汇卡片是一种经典的记忆工具，通过反复练习来加强记忆。现代的词汇卡片可以包含更多信息，如例句、同义词、反义词等。</p>
            
            <h3 class="text-xl font-semibold mb-3 text-gray-800">基础词汇卡片</h3>
            <p class="mb-4 text-gray-700">包含单词、发音、释义和基本例句的标准卡片格式。</p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">基础词汇</div>
                    <div class="keyword text-lg mb-1">appreciate</div>
                    <div class="text-sm text-gray-600 mb-1">/əˈpriːʃieɪt/</div>
                    <div class="text-gray-700">欣赏，感激</div>
                </div>
                
                <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">基础词汇</div>
                    <div class="keyword text-lg mb-1">challenge</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈtʃælɪndʒ/</div>
                    <div class="text-gray-700">挑战</div>
                </div>
                
                <div class="card bg-lime-50 border border-lime-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">基础词汇</div>
                    <div class="keyword text-lg mb-1">opportunity</div>
                    <div class="text-sm text-gray-600 mb-1">/ˌɑːpərˈtuːnəti/</div>
                    <div class="text-gray-700">机会</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">情境词汇卡片</h3>
            <p class="mb-4 text-gray-700">将词汇放在具体情境中，通过完整句子来记忆单词的用法。</p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-rose-50 border border-rose-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">情境例句</div>
                    <div class="keyword text-lg mb-1">I appreciate your help.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ əˈpriːʃieɪt jʊr help/</div>
                    <div class="text-gray-700">我感激你的帮助。</div>
                </div>
                
                <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">情境例句</div>
                    <div class="keyword text-lg mb-1">This is a great opportunity.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðɪs ɪz ə greɪt ˌɑːpərˈtuːnəti/</div>
                    <div class="text-gray-700">这是一个很好的机会。</div>
                </div>
            </div>
        </section>

        <!-- 间隔重复记忆法 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">间隔重复记忆法</h2>
            <p class="mb-4 text-gray-700">间隔重复是基于遗忘曲线理论的科学记忆方法。通过在特定时间间隔内重复学习，可以显著提高长期记忆效果。</p>
            
            <h3 class="text-xl font-semibold mb-3 text-gray-800">艾宾浩斯遗忘曲线应用</h3>
            <p class="mb-4 text-gray-700">根据遗忘规律，在记忆衰退前及时复习，可以有效巩固记忆。</p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                <div class="card bg-violet-50 border border-violet-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">第一次复习</div>
                    <div class="keyword text-lg mb-1">20 minutes</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈtwenti ˈmɪnɪts/</div>
                    <div class="text-gray-700">学习后20分钟</div>
                </div>
                
                <div class="card bg-sky-50 border border-sky-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">第二次复习</div>
                    <div class="keyword text-lg mb-1">1 day</div>
                    <div class="text-sm text-gray-600 mb-1">/wʌn deɪ/</div>
                    <div class="text-gray-700">学习后1天</div>
                </div>
                
                <div class="card bg-amber-50 border border-amber-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">第三次复习</div>
                    <div class="keyword text-lg mb-1">1 week</div>
                    <div class="text-sm text-gray-600 mb-1">/wʌn wiːk/</div>
                    <div class="text-gray-700">学习后1周</div>
                </div>
                
                <div class="card bg-stone-50 border border-stone-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">第四次复习</div>
                    <div class="keyword text-lg mb-1">1 month</div>
                    <div class="text-sm text-gray-600 mb-1">/wʌn mʌnθ/</div>
                    <div class="text-gray-700">学习后1个月</div>
                </div>
            </div>
        </section>

        <!-- 分类记忆法 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">分类记忆法</h2>
            <p class="mb-4 text-gray-700">将词汇按照主题、词性、语义等进行分类，建立词汇网络，便于系统性记忆和应用。</p>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">主题分类</h3>
            <p class="mb-4 text-gray-700">按照生活场景和话题对词汇进行归类。</p>

            <h4 class="text-lg font-medium mb-3 text-gray-700">食物类词汇</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">水果</div>
                    <div class="keyword text-lg mb-1">apple</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈæpəl/</div>
                    <div class="text-gray-700">苹果</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">水果</div>
                    <div class="keyword text-lg mb-1">banana</div>
                    <div class="text-sm text-gray-600 mb-1">/bəˈnænə/</div>
                    <div class="text-gray-700">香蕉</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">水果</div>
                    <div class="keyword text-lg mb-1">grape</div>
                    <div class="text-sm text-gray-600 mb-1">/greɪp/</div>
                    <div class="text-gray-700">葡萄</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">蔬菜</div>
                    <div class="keyword text-lg mb-1">carrot</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈkærət/</div>
                    <div class="text-gray-700">胡萝卜</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">蔬菜</div>
                    <div class="keyword text-lg mb-1">broccoli</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈbrɑːkəli/</div>
                    <div class="text-gray-700">西兰花</div>
                </div>

                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">蔬菜</div>
                    <div class="keyword text-lg mb-1">spinach</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈspɪnɪtʃ/</div>
                    <div class="text-gray-700">菠菜</div>
                </div>
            </div>

            <h4 class="text-lg font-medium mb-3 text-gray-700">交通工具类词汇</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">陆地交通</div>
                    <div class="keyword text-lg mb-1">bicycle</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈbaɪsɪkəl/</div>
                    <div class="text-gray-700">自行车</div>
                </div>

                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">陆地交通</div>
                    <div class="keyword text-lg mb-1">motorcycle</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈmoʊtərsaɪkəl/</div>
                    <div class="text-gray-700">摩托车</div>
                </div>

                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">陆地交通</div>
                    <div class="keyword text-lg mb-1">subway</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈsʌbweɪ/</div>
                    <div class="text-gray-700">地铁</div>
                </div>

                <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">水上交通</div>
                    <div class="keyword text-lg mb-1">ship</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃɪp/</div>
                    <div class="text-gray-700">轮船</div>
                </div>

                <div class="card bg-sky-50 border border-sky-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">空中交通</div>
                    <div class="keyword text-lg mb-1">airplane</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈerpleɪn/</div>
                    <div class="text-gray-700">飞机</div>
                </div>

                <div class="card bg-violet-50 border border-violet-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">空中交通</div>
                    <div class="keyword text-lg mb-1">helicopter</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈhelɪkɑːptər/</div>
                    <div class="text-gray-700">直升机</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">词性分类</h3>
            <p class="mb-4 text-gray-700">按照词汇的语法功能进行分类，有助于理解词汇在句子中的作用。</p>

            <h4 class="text-lg font-medium mb-3 text-gray-700">动词分类</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">行为动词</div>
                    <div class="keyword text-lg mb-1">run</div>
                    <div class="text-sm text-gray-600 mb-1">/rʌn/</div>
                    <div class="text-gray-700">跑</div>
                </div>

                <div class="card bg-lime-50 border border-lime-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">行为动词</div>
                    <div class="keyword text-lg mb-1">jump</div>
                    <div class="text-sm text-gray-600 mb-1">/dʒʌmp/</div>
                    <div class="text-gray-700">跳</div>
                </div>

                <div class="card bg-amber-50 border border-amber-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">行为动词</div>
                    <div class="keyword text-lg mb-1">swim</div>
                    <div class="text-sm text-gray-600 mb-1">/swɪm/</div>
                    <div class="text-gray-700">游泳</div>
                </div>

                <div class="card bg-rose-50 border border-rose-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">状态动词</div>
                    <div class="keyword text-lg mb-1">exist</div>
                    <div class="text-sm text-gray-600 mb-1">/ɪɡˈzɪst/</div>
                    <div class="text-gray-700">存在</div>
                </div>

                <div class="card bg-stone-50 border border-stone-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">状态动词</div>
                    <div class="keyword text-lg mb-1">belong</div>
                    <div class="text-sm text-gray-600 mb-1">/bɪˈlɔːŋ/</div>
                    <div class="text-gray-700">属于</div>
                </div>

                <div class="card bg-neutral-50 border border-neutral-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">状态动词</div>
                    <div class="keyword text-lg mb-1">contain</div>
                    <div class="text-sm text-gray-600 mb-1">/kənˈteɪn/</div>
                    <div class="text-gray-700">包含</div>
                </div>
            </div>

            <h4 class="text-lg font-medium mb-3 text-gray-700">形容词分类</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">描述性形容词</div>
                    <div class="keyword text-lg mb-1">beautiful</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈbjuːtɪfəl/</div>
                    <div class="text-gray-700">美丽的</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">描述性形容词</div>
                    <div class="keyword text-lg mb-1">intelligent</div>
                    <div class="text-sm text-gray-600 mb-1">/ɪnˈtelɪdʒənt/</div>
                    <div class="text-gray-700">聪明的</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">描述性形容词</div>
                    <div class="keyword text-lg mb-1">generous</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈdʒenərəs/</div>
                    <div class="text-gray-700">慷慨的</div>
                </div>
            </div>
        </section>

        <!-- 语境记忆法 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">语境记忆法</h2>
            <p class="mb-4 text-gray-700">将词汇放在具体的语言环境中学习，通过上下文来理解和记忆词汇的含义和用法。</p>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">故事语境</h3>
            <p class="mb-4 text-gray-700">通过编写或阅读包含目标词汇的故事来加深记忆。</p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">故事情境</div>
                    <div class="keyword text-lg mb-1">The brave knight rescued the princess.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə breɪv naɪt ˈreskjuːd ðə ˈprɪnsəs/</div>
                    <div class="text-gray-700">勇敢的骑士救了公主。</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">故事情境</div>
                    <div class="keyword text-lg mb-1">She discovered a hidden treasure.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi dɪˈskʌvərd ə ˈhɪdən ˈtreʒər/</div>
                    <div class="text-gray-700">她发现了一个隐藏的宝藏。</div>
                </div>
            </div>
        </section>

        <!-- 对话语境 -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-3 text-gray-800">对话语境</h3>
            <p class="mb-4 text-gray-700">通过模拟真实对话场景来学习词汇的实际应用。</p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">餐厅对话</div>
                    <div class="keyword text-lg mb-1">Could I have the menu, please?</div>
                    <div class="text-sm text-gray-600 mb-1">/kʊd aɪ hæv ðə ˈmenjuː pliːz/</div>
                    <div class="text-gray-700">请给我菜单好吗？</div>
                </div>

                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">购物对话</div>
                    <div class="keyword text-lg mb-1">How much does this cost?</div>
                    <div class="text-sm text-gray-600 mb-1">/haʊ mʌtʃ dʌz ðɪs kɔːst/</div>
                    <div class="text-gray-700">这个多少钱？</div>
                </div>

                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">工作对话</div>
                    <div class="keyword text-lg mb-1">Let's schedule a meeting.</div>
                    <div class="text-sm text-gray-600 mb-1">/lets ˈskedʒuːl ə ˈmiːtɪŋ/</div>
                    <div class="text-gray-700">我们安排一个会议吧。</div>
                </div>

                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">社交对话</div>
                    <div class="keyword text-lg mb-1">Nice to meet you!</div>
                    <div class="text-sm text-gray-600 mb-1">/naɪs tuː miːt juː/</div>
                    <div class="text-gray-700">很高兴见到你！</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">新闻语境</h3>
            <p class="mb-4 text-gray-700">通过新闻报道的语境来学习正式和学术词汇。</p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">经济新闻</div>
                    <div class="keyword text-lg mb-1">The economy is recovering gradually.</div>
                    <div class="text-sm text-gray-600 mb-1">/ði ɪˈkɑːnəmi ɪz rɪˈkʌvərɪŋ ˈɡrædʒuəli/</div>
                    <div class="text-gray-700">经济正在逐渐复苏。</div>
                </div>

                <div class="card bg-sky-50 border border-sky-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">科技新闻</div>
                    <div class="keyword text-lg mb-1">Scientists made a breakthrough.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈsaɪəntɪsts meɪd ə ˈbreɪkθruː/</div>
                    <div class="text-gray-700">科学家们取得了突破。</div>
                </div>
            </div>
        </section>

        <!-- 多感官记忆法 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">多感官记忆法</h2>
            <p class="mb-4 text-gray-700">调动视觉、听觉、触觉等多种感官来增强词汇记忆效果。</p>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">视觉记忆</h3>
            <p class="mb-4 text-gray-700">通过图像、颜色、形状等视觉元素来辅助记忆。</p>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">颜色词汇</div>
                    <div class="keyword text-lg mb-1">crimson</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈkrɪmzən/</div>
                    <div class="text-gray-700">深红色</div>
                </div>

                <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">颜色词汇</div>
                    <div class="keyword text-lg mb-1">emerald</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈemərəld/</div>
                    <div class="text-gray-700">翡翠绿</div>
                </div>

                <div class="card bg-violet-50 border border-violet-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">颜色词汇</div>
                    <div class="keyword text-lg mb-1">violet</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈvaɪələt/</div>
                    <div class="text-gray-700">紫罗兰色</div>
                </div>

                <div class="card bg-amber-50 border border-amber-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">形状词汇</div>
                    <div class="keyword text-lg mb-1">triangle</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈtraɪæŋɡəl/</div>
                    <div class="text-gray-700">三角形</div>
                </div>

                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">形状词汇</div>
                    <div class="keyword text-lg mb-1">rectangle</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈrektæŋɡəl/</div>
                    <div class="text-gray-700">长方形</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">形状词汇</div>
                    <div class="keyword text-lg mb-1">pentagon</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈpentəɡɑːn/</div>
                    <div class="text-gray-700">五边形</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">听觉记忆</h3>
            <p class="mb-4 text-gray-700">通过音乐、韵律、重复朗读等听觉方式来加强记忆。</p>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-lime-50 border border-lime-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">音乐词汇</div>
                    <div class="keyword text-lg mb-1">melody</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈmelədi/</div>
                    <div class="text-gray-700">旋律</div>
                </div>

                <div class="card bg-rose-50 border border-rose-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">音乐词汇</div>
                    <div class="keyword text-lg mb-1">rhythm</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈrɪðəm/</div>
                    <div class="text-gray-700">节奏</div>
                </div>

                <div class="card bg-stone-50 border border-stone-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">音乐词汇</div>
                    <div class="keyword text-lg mb-1">harmony</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈhɑːrməni/</div>
                    <div class="text-gray-700">和谐</div>
                </div>

                <div class="card bg-neutral-50 border border-neutral-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">声音词汇</div>
                    <div class="keyword text-lg mb-1">whisper</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈwɪspər/</div>
                    <div class="text-gray-700">低语</div>
                </div>

                <div class="card bg-gray-50 border border-gray-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">声音词汇</div>
                    <div class="keyword text-lg mb-1">thunder</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈθʌndər/</div>
                    <div class="text-gray-700">雷声</div>
                </div>

                <div class="card bg-slate-50 border border-slate-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">声音词汇</div>
                    <div class="keyword text-lg mb-1">echo</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈekoʊ/</div>
                    <div class="text-gray-700">回声</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">动作记忆</h3>
            <p class="mb-4 text-gray-700">通过身体动作和手势来辅助词汇记忆。</p>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">动作词汇</div>
                    <div class="keyword text-lg mb-1">clap</div>
                    <div class="text-sm text-gray-600 mb-1">/klæp/</div>
                    <div class="text-gray-700">拍手</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">动作词汇</div>
                    <div class="keyword text-lg mb-1">stretch</div>
                    <div class="text-sm text-gray-600 mb-1">/stretʃ/</div>
                    <div class="text-gray-700">伸展</div>
                </div>

                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">动作词汇</div>
                    <div class="keyword text-lg mb-1">twist</div>
                    <div class="text-sm text-gray-600 mb-1">/twɪst/</div>
                    <div class="text-gray-700">扭转</div>
                </div>
            </div>
        </section>

        <!-- 词汇网络构建法 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">词汇网络构建法</h2>
            <p class="mb-4 text-gray-700">通过建立词汇之间的关联网络，形成系统性的词汇知识结构。</p>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">同义词网络</h3>
            <p class="mb-4 text-gray-700">将意思相近的词汇组织在一起，形成同义词群。</p>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">表示"大"</div>
                    <div class="keyword text-lg mb-1">big</div>
                    <div class="text-sm text-gray-600 mb-1">/bɪɡ/</div>
                    <div class="text-gray-700">大的</div>
                </div>

                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">表示"大"</div>
                    <div class="keyword text-lg mb-1">large</div>
                    <div class="text-sm text-gray-600 mb-1">/lɑːrdʒ/</div>
                    <div class="text-gray-700">大的</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">表示"大"</div>
                    <div class="keyword text-lg mb-1">huge</div>
                    <div class="text-sm text-gray-600 mb-1">/hjuːdʒ/</div>
                    <div class="text-gray-700">巨大的</div>
                </div>

                <div class="card bg-violet-50 border border-violet-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">表示"大"</div>
                    <div class="keyword text-lg mb-1">enormous</div>
                    <div class="text-sm text-gray-600 mb-1">/ɪˈnɔːrməs/</div>
                    <div class="text-gray-700">巨大的</div>
                </div>
            </div>
        </section>

        <!-- 反义词网络 -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-3 text-gray-800">反义词网络</h3>
            <p class="mb-4 text-gray-700">通过对比学习相反意思的词汇，加深对词汇含义的理解。</p>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">正面情感</div>
                    <div class="keyword text-lg mb-1">happy</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈhæpi/</div>
                    <div class="text-gray-700">快乐的</div>
                </div>

                <div class="card bg-gray-50 border border-gray-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">负面情感</div>
                    <div class="keyword text-lg mb-1">sad</div>
                    <div class="text-sm text-gray-600 mb-1">/sæd/</div>
                    <div class="text-gray-700">悲伤的</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">积极特质</div>
                    <div class="keyword text-lg mb-1">brave</div>
                    <div class="text-sm text-gray-600 mb-1">/breɪv/</div>
                    <div class="text-gray-700">勇敢的</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">消极特质</div>
                    <div class="keyword text-lg mb-1">coward</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈkaʊərd/</div>
                    <div class="text-gray-700">胆小鬼</div>
                </div>

                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">温度高</div>
                    <div class="keyword text-lg mb-1">hot</div>
                    <div class="text-sm text-gray-600 mb-1">/hɑːt/</div>
                    <div class="text-gray-700">热的</div>
                </div>

                <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">温度低</div>
                    <div class="keyword text-lg mb-1">cold</div>
                    <div class="text-sm text-gray-600 mb-1">/koʊld/</div>
                    <div class="text-gray-700">冷的</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">速度快</div>
                    <div class="keyword text-lg mb-1">fast</div>
                    <div class="text-sm text-gray-600 mb-1">/fæst/</div>
                    <div class="text-gray-700">快的</div>
                </div>

                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">速度慢</div>
                    <div class="keyword text-lg mb-1">slow</div>
                    <div class="text-sm text-gray-600 mb-1">/sloʊ/</div>
                    <div class="text-gray-700">慢的</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">词族网络</h3>
            <p class="mb-4 text-gray-700">将同一词根衍生出的不同词汇组织在一起。</p>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">act词族</div>
                    <div class="keyword text-lg mb-1">act</div>
                    <div class="text-sm text-gray-600 mb-1">/ækt/</div>
                    <div class="text-gray-700">行动</div>
                </div>

                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">act词族</div>
                    <div class="keyword text-lg mb-1">action</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈækʃən/</div>
                    <div class="text-gray-700">行动</div>
                </div>

                <div class="card bg-sky-50 border border-sky-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">act词族</div>
                    <div class="keyword text-lg mb-1">active</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈæktɪv/</div>
                    <div class="text-gray-700">积极的</div>
                </div>

                <div class="card bg-lime-50 border border-lime-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">act词族</div>
                    <div class="keyword text-lg mb-1">activity</div>
                    <div class="text-sm text-gray-600 mb-1">/ækˈtɪvəti/</div>
                    <div class="text-gray-700">活动</div>
                </div>

                <div class="card bg-amber-50 border border-amber-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">create词族</div>
                    <div class="keyword text-lg mb-1">create</div>
                    <div class="text-sm text-gray-600 mb-1">/kriˈeɪt/</div>
                    <div class="text-gray-700">创造</div>
                </div>

                <div class="card bg-rose-50 border border-rose-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">create词族</div>
                    <div class="keyword text-lg mb-1">creation</div>
                    <div class="text-sm text-gray-600 mb-1">/kriˈeɪʃən/</div>
                    <div class="text-gray-700">创造</div>
                </div>

                <div class="card bg-stone-50 border border-stone-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">create词族</div>
                    <div class="keyword text-lg mb-1">creative</div>
                    <div class="text-sm text-gray-600 mb-1">/kriˈeɪtɪv/</div>
                    <div class="text-gray-700">有创造力的</div>
                </div>

                <div class="card bg-neutral-50 border border-neutral-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">create词族</div>
                    <div class="keyword text-lg mb-1">creator</div>
                    <div class="text-sm text-gray-600 mb-1">/kriˈeɪtər/</div>
                    <div class="text-gray-700">创造者</div>
                </div>
            </div>
        </section>

        <!-- 记忆宫殿法 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">记忆宫殿法</h2>
            <p class="mb-4 text-gray-700">利用熟悉的空间位置来组织和记忆词汇，将抽象的词汇与具体的空间位置联系起来。</p>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">家庭空间记忆</h3>
            <p class="mb-4 text-gray-700">以家中的不同房间和物品为记忆锚点。</p>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">客厅词汇</div>
                    <div class="keyword text-lg mb-1">sofa</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈsoʊfə/</div>
                    <div class="text-gray-700">沙发</div>
                </div>

                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">客厅词汇</div>
                    <div class="keyword text-lg mb-1">television</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈtelɪvɪʒən/</div>
                    <div class="text-gray-700">电视</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">客厅词汇</div>
                    <div class="keyword text-lg mb-1">carpet</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈkɑːrpət/</div>
                    <div class="text-gray-700">地毯</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">厨房词汇</div>
                    <div class="keyword text-lg mb-1">refrigerator</div>
                    <div class="text-sm text-gray-600 mb-1">/rɪˈfrɪdʒəreɪtər/</div>
                    <div class="text-gray-700">冰箱</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">厨房词汇</div>
                    <div class="keyword text-lg mb-1">microwave</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈmaɪkrəweɪv/</div>
                    <div class="text-gray-700">微波炉</div>
                </div>

                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">厨房词汇</div>
                    <div class="keyword text-lg mb-1">dishwasher</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈdɪʃwɑːʃər/</div>
                    <div class="text-gray-700">洗碗机</div>
                </div>

                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">卧室词汇</div>
                    <div class="keyword text-lg mb-1">mattress</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈmætrəs/</div>
                    <div class="text-gray-700">床垫</div>
                </div>

                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">卧室词汇</div>
                    <div class="keyword text-lg mb-1">wardrobe</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈwɔːrdroʊb/</div>
                    <div class="text-gray-700">衣柜</div>
                </div>

                <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">卧室词汇</div>
                    <div class="keyword text-lg mb-1">pillow</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈpɪloʊ/</div>
                    <div class="text-gray-700">枕头</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">学校空间记忆</h3>
            <p class="mb-4 text-gray-700">以学校的不同区域来组织学术词汇。</p>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">教室词汇</div>
                    <div class="keyword text-lg mb-1">blackboard</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈblækbɔːrd/</div>
                    <div class="text-gray-700">黑板</div>
                </div>

                <div class="card bg-lime-50 border border-lime-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">教室词汇</div>
                    <div class="keyword text-lg mb-1">projector</div>
                    <div class="text-sm text-gray-600 mb-1">/prəˈdʒektər/</div>
                    <div class="text-gray-700">投影仪</div>
                </div>

                <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">教室词汇</div>
                    <div class="keyword text-lg mb-1">desk</div>
                    <div class="text-sm text-gray-600 mb-1">/desk/</div>
                    <div class="text-gray-700">课桌</div>
                </div>

                <div class="card bg-sky-50 border border-sky-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">图书馆词汇</div>
                    <div class="keyword text-lg mb-1">encyclopedia</div>
                    <div class="text-sm text-gray-600 mb-1">/ɪnˌsaɪkləˈpiːdiə/</div>
                    <div class="text-gray-700">百科全书</div>
                </div>

                <div class="card bg-violet-50 border border-violet-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">图书馆词汇</div>
                    <div class="keyword text-lg mb-1">catalog</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈkætəlɔːɡ/</div>
                    <div class="text-gray-700">目录</div>
                </div>

                <div class="card bg-amber-50 border border-amber-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">图书馆词汇</div>
                    <div class="keyword text-lg mb-1">librarian</div>
                    <div class="text-sm text-gray-600 mb-1">/laɪˈbreriən/</div>
                    <div class="text-gray-700">图书管理员</div>
                </div>
            </div>
        </section>

        <!-- 情感记忆法 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">情感记忆法</h2>
            <p class="mb-4 text-gray-700">通过情感体验来加强词汇记忆，利用情感的力量让词汇更加深刻难忘。</p>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">积极情感词汇</h3>
            <p class="mb-4 text-gray-700">学习表达正面情感和体验的词汇。</p>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">喜悦情感</div>
                    <div class="keyword text-lg mb-1">delighted</div>
                    <div class="text-sm text-gray-600 mb-1">/dɪˈlaɪtəd/</div>
                    <div class="text-gray-700">高兴的</div>
                </div>

                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">喜悦情感</div>
                    <div class="keyword text-lg mb-1">ecstatic</div>
                    <div class="text-sm text-gray-600 mb-1">/ɪkˈstætɪk/</div>
                    <div class="text-gray-700">狂喜的</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">喜悦情感</div>
                    <div class="keyword text-lg mb-1">thrilled</div>
                    <div class="text-sm text-gray-600 mb-1">/θrɪld/</div>
                    <div class="text-gray-700">激动的</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">满足情感</div>
                    <div class="keyword text-lg mb-1">satisfied</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈsætɪsfaɪd/</div>
                    <div class="text-gray-700">满意的</div>
                </div>

                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">满足情感</div>
                    <div class="keyword text-lg mb-1">content</div>
                    <div class="text-sm text-gray-600 mb-1">/kənˈtent/</div>
                    <div class="text-gray-700">满足的</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">满足情感</div>
                    <div class="keyword text-lg mb-1">fulfilled</div>
                    <div class="text-sm text-gray-600 mb-1">/fʊlˈfɪld/</div>
                    <div class="text-gray-700">充实的</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">挑战情感词汇</h3>
            <p class="mb-4 text-gray-700">学习表达困难和挑战情境的词汇。</p>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">困难情感</div>
                    <div class="keyword text-lg mb-1">frustrated</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈfrʌstreɪtəd/</div>
                    <div class="text-gray-700">沮丧的</div>
                </div>

                <div class="card bg-gray-50 border border-gray-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">困难情感</div>
                    <div class="keyword text-lg mb-1">overwhelmed</div>
                    <div class="text-sm text-gray-600 mb-1">/ˌoʊvərˈwelmd/</div>
                    <div class="text-gray-700">不知所措的</div>
                </div>

                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">困难情感</div>
                    <div class="keyword text-lg mb-1">anxious</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈæŋkʃəs/</div>
                    <div class="text-gray-700">焦虑的</div>
                </div>

                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">坚持情感</div>
                    <div class="keyword text-lg mb-1">determined</div>
                    <div class="text-sm text-gray-600 mb-1">/dɪˈtɜːrmɪnd/</div>
                    <div class="text-gray-700">坚定的</div>
                </div>

                <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">坚持情感</div>
                    <div class="keyword text-lg mb-1">persistent</div>
                    <div class="text-sm text-gray-600 mb-1">/pərˈsɪstənt/</div>
                    <div class="text-gray-700">坚持不懈的</div>
                </div>

                <div class="card bg-lime-50 border border-lime-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">坚持情感</div>
                    <div class="keyword text-lg mb-1">resilient</div>
                    <div class="text-sm text-gray-600 mb-1">/rɪˈzɪliənt/</div>
                    <div class="text-gray-700">有韧性的</div>
                </div>
            </div>
        </section>

        <!-- 游戏化记忆法 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">游戏化记忆法</h2>
            <p class="mb-4 text-gray-700">通过游戏元素和竞争机制来增加词汇学习的趣味性和动力。</p>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">词汇接龙</h3>
            <p class="mb-4 text-gray-700">通过词汇的首尾字母连接来形成记忆链条。</p>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">接龙序列1</div>
                    <div class="keyword text-lg mb-1">apple</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈæpəl/</div>
                    <div class="text-gray-700">苹果</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">接龙序列2</div>
                    <div class="keyword text-lg mb-1">elephant</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈeləfənt/</div>
                    <div class="text-gray-700">大象</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">接龙序列3</div>
                    <div class="keyword text-lg mb-1">tiger</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈtaɪɡər/</div>
                    <div class="text-gray-700">老虎</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">接龙序列4</div>
                    <div class="keyword text-lg mb-1">rabbit</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈræbɪt/</div>
                    <div class="text-gray-700">兔子</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">词汇分类竞赛</h3>
            <p class="mb-4 text-gray-700">通过快速分类词汇来提高反应速度和记忆效果。</p>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">运动类</div>
                    <div class="keyword text-lg mb-1">basketball</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈbæskətbɔːl/</div>
                    <div class="text-gray-700">篮球</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">运动类</div>
                    <div class="keyword text-lg mb-1">swimming</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈswɪmɪŋ/</div>
                    <div class="text-gray-700">游泳</div>
                </div>

                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">运动类</div>
                    <div class="keyword text-lg mb-1">tennis</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈtenɪs/</div>
                    <div class="text-gray-700">网球</div>
                </div>

                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">乐器类</div>
                    <div class="keyword text-lg mb-1">piano</div>
                    <div class="text-sm text-gray-600 mb-1">/piˈænoʊ/</div>
                    <div class="text-gray-700">钢琴</div>
                </div>

                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">乐器类</div>
                    <div class="keyword text-lg mb-1">guitar</div>
                    <div class="text-sm text-gray-600 mb-1">/ɡɪˈtɑːr/</div>
                    <div class="text-gray-700">吉他</div>
                </div>

                <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">乐器类</div>
                    <div class="keyword text-lg mb-1">violin</div>
                    <div class="text-sm text-gray-600 mb-1">/ˌvaɪəˈlɪn/</div>
                    <div class="text-gray-700">小提琴</div>
                </div>
            </div>
        </section>

        <!-- 技术辅助记忆法 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">技术辅助记忆法</h2>
            <p class="mb-4 text-gray-700">利用现代技术工具来优化词汇学习过程，提高学习效率。</p>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">数字化词汇卡片</h3>
            <p class="mb-4 text-gray-700">使用电子设备和应用程序来创建和管理词汇卡片。</p>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-sky-50 border border-sky-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">科技词汇</div>
                    <div class="keyword text-lg mb-1">application</div>
                    <div class="text-sm text-gray-600 mb-1">/ˌæplɪˈkeɪʃən/</div>
                    <div class="text-gray-700">应用程序</div>
                </div>

                <div class="card bg-violet-50 border border-violet-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">科技词汇</div>
                    <div class="keyword text-lg mb-1">algorithm</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈælɡərɪðəm/</div>
                    <div class="text-gray-700">算法</div>
                </div>

                <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">科技词汇</div>
                    <div class="keyword text-lg mb-1">database</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈdeɪtəbeɪs/</div>
                    <div class="text-gray-700">数据库</div>
                </div>

                <div class="card bg-amber-50 border border-amber-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">学习工具</div>
                    <div class="keyword text-lg mb-1">flashcard</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈflæʃkɑːrd/</div>
                    <div class="text-gray-700">闪卡</div>
                </div>

                <div class="card bg-rose-50 border border-rose-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">学习工具</div>
                    <div class="keyword text-lg mb-1">interactive</div>
                    <div class="text-sm text-gray-600 mb-1">/ˌɪntərˈæktɪv/</div>
                    <div class="text-gray-700">互动的</div>
                </div>

                <div class="card bg-stone-50 border border-stone-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">学习工具</div>
                    <div class="keyword text-lg mb-1">multimedia</div>
                    <div class="text-sm text-gray-600 mb-1">/ˌmʌltiˈmiːdiə/</div>
                    <div class="text-gray-700">多媒体</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">语音识别练习</h3>
            <p class="mb-4 text-gray-700">利用语音技术来练习发音和听力理解。</p>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-lime-50 border border-lime-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">发音练习</div>
                    <div class="keyword text-lg mb-1">pronunciation</div>
                    <div class="text-sm text-gray-600 mb-1">/prəˌnʌnsiˈeɪʃən/</div>
                    <div class="text-gray-700">发音</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">发音练习</div>
                    <div class="keyword text-lg mb-1">intonation</div>
                    <div class="text-sm text-gray-600 mb-1">/ˌɪntəˈneɪʃən/</div>
                    <div class="text-gray-700">语调</div>
                </div>

                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">发音练习</div>
                    <div class="keyword text-lg mb-1">accent</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈæksent/</div>
                    <div class="text-gray-700">口音</div>
                </div>
            </div>
        </section>

        <!-- 社交记忆法 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">社交记忆法</h2>
            <p class="mb-4 text-gray-700">通过与他人的互动和交流来加强词汇记忆和应用能力。</p>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">词汇交流活动</h3>
            <p class="mb-4 text-gray-700">通过小组讨论、角色扮演等活动来练习词汇使用。</p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">交流表达</div>
                    <div class="keyword text-lg mb-1">Let me share my opinion.</div>
                    <div class="text-sm text-gray-600 mb-1">/let miː ʃer maɪ əˈpɪnjən/</div>
                    <div class="text-gray-700">让我分享我的观点。</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">交流表达</div>
                    <div class="keyword text-lg mb-1">I'd like to add something.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪd laɪk tuː æd ˈsʌmθɪŋ/</div>
                    <div class="text-gray-700">我想补充一些内容。</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">交流表达</div>
                    <div class="keyword text-lg mb-1">That's an interesting point.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðæts ən ˈɪntrəstɪŋ pɔɪnt/</div>
                    <div class="text-gray-700">这是一个有趣的观点。</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">交流表达</div>
                    <div class="keyword text-lg mb-1">Could you elaborate on that?</div>
                    <div class="text-sm text-gray-600 mb-1">/kʊd juː ɪˈlæbəreɪt ɑːn ðæt/</div>
                    <div class="text-gray-700">你能详细说明一下吗？</div>
                </div>
            </div>
        </section>
    </div>
</body>
</html>
