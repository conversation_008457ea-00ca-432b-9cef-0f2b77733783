<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>地点介词学习</title>
    <script src="/static/tailwindcss-3.4.17.js"></script>
    <style>
        .keyword { color: #3d74ed; font-weight: bold; }
        .card { transition: transform 0.1s ease; }
    </style>
</head>
<body class="bg-white p-6">

    <!-- 地点介词概述 -->
    <div class="mb-8">
        <h2 class="text-2xl font-bold mb-4 text-gray-800">地点介词详解</h2>
        <p class="text-gray-700 leading-relaxed mb-4">
            地点介词（Prepositions of Place）是英语语法中的重要组成部分，用来描述物体、人或事物之间的空间位置关系。
            这些介词帮助我们准确表达"在哪里"的概念，是日常交流中不可或缺的语言工具。
        </p>

        <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
            <h4 class="text-lg font-semibold mb-3 text-blue-800">地点介词的重要性</h4>
            <ul class="space-y-2 text-gray-700">
                <li>• <strong>空间定位</strong>：准确描述物体的位置和方向</li>
                <li>• <strong>日常交流</strong>：在问路、描述位置时必不可少</li>
                <li>• <strong>语言精确性</strong>：避免位置描述的模糊和误解</li>
                <li>• <strong>文化表达</strong>：不同语言的空间概念存在差异</li>
            </ul>
        </div>

        <div class="bg-green-50 border border-green-200 rounded-lg p-6 mb-6">
            <h4 class="text-lg font-semibold mb-3 text-green-800">地点介词的分类</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <h5 class="font-semibold mb-2 text-green-700">基础位置介词</h5>
                    <ul class="text-sm text-gray-700 space-y-1">
                        <li>• AT - 具体地点</li>
                        <li>• IN - 内部空间</li>
                        <li>• ON - 表面接触</li>
                    </ul>
                </div>
                <div>
                    <h5 class="font-semibold mb-2 text-green-700">方向位置介词</h5>
                    <ul class="text-sm text-gray-700 space-y-1">
                        <li>• ABOVE/BELOW - 垂直关系</li>
                        <li>• OVER/UNDER - 覆盖关系</li>
                        <li>• BESIDE/NEAR - 邻近关系</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
            <h4 class="text-lg font-semibold mb-3 text-yellow-800">学习要点</h4>
            <p class="text-gray-700 mb-3">
                地点介词的使用往往与具体语境相关，同一个介词在不同情况下可能有不同的含义。
                理解每个介词的核心概念和使用场景是掌握的关键。
            </p>
            <div class="text-sm text-gray-600">
                <strong>记忆技巧：</strong>通过视觉化想象和实际场景练习来加深理解
            </div>
        </div>
    </div>

    <!-- AT 介词详解 -->
    <div class="mb-8">
        <h3 class="text-xl font-semibold mb-4 text-gray-800">AT - 在特定地点</h3>
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
            <h4 class="text-lg font-semibold mb-3 text-blue-800">AT 的核心概念</h4>
            <p class="text-gray-700 mb-4">
                <span class="keyword">AT</span> 用于指定确切的地点、位置或活动场所。它强调的是一个具体的点或位置，
                而不是空间的内部或表面。AT通常用于较小的、具体的地点，或者表示某种活动正在进行的场所。
            </p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h5 class="font-semibold mb-2 text-blue-700">使用场景</h5>
                    <ul class="text-sm text-gray-700 space-y-1">
                        <li>• 具体地址或门牌号</li>
                        <li>• 活动场所或事件地点</li>
                        <li>• 交通站点</li>
                        <li>• 工作或学习场所</li>
                        <li>• 聚会或会议地点</li>
                    </ul>
                </div>
                <div>
                    <h5 class="font-semibold mb-2 text-blue-700">语法特点</h5>
                    <ul class="text-sm text-gray-700 space-y-1">
                        <li>• 后接具体地点名词</li>
                        <li>• 不强调内部空间</li>
                        <li>• 常用于固定搭配</li>
                        <li>• 表示"到达"的概念</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
            <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在学校</div>
                <div class="keyword text-lg mb-1">I'm at school.</div>
                <div class="text-sm text-gray-600 mb-1">/aɪm æt skuːl/</div>
                <div class="text-gray-700">我在学校。</div>
            </div>
            
            <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在家</div>
                <div class="keyword text-lg mb-1">She's at home.</div>
                <div class="text-sm text-gray-600 mb-1">/ʃiːz æt hoʊm/</div>
                <div class="text-gray-700">她在家。</div>
            </div>
            
            <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在工作</div>
                <div class="keyword text-lg mb-1">He's at work.</div>
                <div class="text-sm text-gray-600 mb-1">/hiːz æt wɜrk/</div>
                <div class="text-gray-700">他在工作。</div>
            </div>
            
            <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在聚会上</div>
                <div class="keyword text-lg mb-1">We met at the party.</div>
                <div class="text-sm text-gray-600 mb-1">/wi mɛt æt ðə ˈpɑrti/</div>
                <div class="text-gray-700">我们在聚会上见面。</div>
            </div>
            
            <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在地址</div>
                <div class="keyword text-lg mb-1">I live at 123 Main Street.</div>
                <div class="text-sm text-gray-600 mb-1">/aɪ lɪv æt wʌn tuː θri meɪn strit/</div>
                <div class="text-gray-700">我住在主街123号。</div>
            </div>

            <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在车站</div>
                <div class="keyword text-lg mb-1">Meet me at the bus station.</div>
                <div class="text-sm text-gray-600 mb-1">/mit mi æt ðə bʌs ˈsteɪʃən/</div>
                <div class="text-gray-700">在公交车站见我。</div>
            </div>

            <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在门口</div>
                <div class="keyword text-lg mb-1">She's waiting at the door.</div>
                <div class="text-sm text-gray-600 mb-1">/ʃiz ˈweɪtɪŋ æt ðə dɔr/</div>
                <div class="text-gray-700">她在门口等着。</div>
            </div>
        </div>

        <div class="bg-gray-50 border border-gray-200 rounded-lg p-6 mt-6">
            <h4 class="text-lg font-semibold mb-3 text-gray-800">AT 的常见固定搭配</h4>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <h5 class="font-semibold mb-2 text-gray-700">地点类</h5>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• at home 在家</li>
                        <li>• at school 在学校</li>
                        <li>• at work 在工作</li>
                        <li>• at university 在大学</li>
                        <li>• at the office 在办公室</li>
                    </ul>
                </div>
                <div>
                    <h5 class="font-semibold mb-2 text-gray-700">活动类</h5>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• at a party 在聚会上</li>
                        <li>• at a meeting 在会议上</li>
                        <li>• at a concert 在音乐会上</li>
                        <li>• at a conference 在会议上</li>
                        <li>• at a wedding 在婚礼上</li>
                    </ul>
                </div>
                <div>
                    <h5 class="font-semibold mb-2 text-gray-700">位置类</h5>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• at the top 在顶部</li>
                        <li>• at the bottom 在底部</li>
                        <li>• at the end 在末端</li>
                        <li>• at the corner 在角落</li>
                        <li>• at the entrance 在入口</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- IN 介词详解 -->
    <div class="mb-8">
        <h3 class="text-xl font-semibold mb-4 text-gray-800">IN - 在内部/范围内</h3>
        <div class="bg-green-50 border border-green-200 rounded-lg p-6 mb-6">
            <h4 class="text-lg font-semibold mb-3 text-green-800">IN 的核心概念</h4>
            <p class="text-gray-700 mb-4">
                <span class="keyword">IN</span> 表示在某个封闭的空间内部，或在某个区域、范围之内。
                它强调的是"被包围"或"被包含"的概念，通常用于三维空间或抽象的范围概念。
            </p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h5 class="font-semibold mb-2 text-green-700">空间概念</h5>
                    <ul class="text-sm text-gray-700 space-y-1">
                        <li>• 封闭或半封闭空间</li>
                        <li>• 三维容器内部</li>
                        <li>• 地理区域范围</li>
                        <li>• 抽象空间概念</li>
                    </ul>
                </div>
                <div>
                    <h5 class="font-semibold mb-2 text-green-700">使用特点</h5>
                    <ul class="text-sm text-gray-700 space-y-1">
                        <li>• 强调内部位置</li>
                        <li>• 表示被包围状态</li>
                        <li>• 用于大范围地点</li>
                        <li>• 常与时间概念结合</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
            <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在房间里</div>
                <div class="keyword text-lg mb-1">The cat is in the room.</div>
                <div class="text-sm text-gray-600 mb-1">/ðə kæt ɪz ɪn ðə rum/</div>
                <div class="text-gray-700">猫在房间里。</div>
            </div>
            
            <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在城市里</div>
                <div class="keyword text-lg mb-1">I live in New York.</div>
                <div class="text-sm text-gray-600 mb-1">/aɪ lɪv ɪn nu jɔrk/</div>
                <div class="text-gray-700">我住在纽约。</div>
            </div>
            
            <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在盒子里</div>
                <div class="keyword text-lg mb-1">The toy is in the box.</div>
                <div class="text-sm text-gray-600 mb-1">/ðə tɔɪ ɪz ɪn ðə bɑks/</div>
                <div class="text-gray-700">玩具在盒子里。</div>
            </div>
            
            <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在车里</div>
                <div class="keyword text-lg mb-1">She's sitting in the car.</div>
                <div class="text-sm text-gray-600 mb-1">/ʃiz ˈsɪtɪŋ ɪn ðə kɑr/</div>
                <div class="text-gray-700">她坐在车里。</div>
            </div>
            
            <div class="card bg-gray-50 border border-gray-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在国家里</div>
                <div class="keyword text-lg mb-1">I was born in China.</div>
                <div class="text-sm text-gray-600 mb-1">/aɪ wʌz bɔrn ɪn ˈtʃaɪnə/</div>
                <div class="text-gray-700">我出生在中国。</div>
            </div>

            <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在书里</div>
                <div class="keyword text-lg mb-1">The answer is in the book.</div>
                <div class="text-sm text-gray-600 mb-1">/ði ˈænsər ɪz ɪn ðə bʊk/</div>
                <div class="text-gray-700">答案在书里。</div>
            </div>

            <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在水里</div>
                <div class="keyword text-lg mb-1">Fish live in water.</div>
                <div class="text-sm text-gray-600 mb-1">/fɪʃ lɪv ɪn ˈwɔtər/</div>
                <div class="text-gray-700">鱼生活在水中。</div>
            </div>
        </div>

        <div class="bg-gray-50 border border-gray-200 rounded-lg p-6 mt-6">
            <h4 class="text-lg font-semibold mb-3 text-gray-800">IN 与 AT 的区别</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="bg-white rounded-lg p-4 border">
                    <h5 class="font-semibold mb-2 text-green-700">使用 IN</h5>
                    <ul class="text-sm text-gray-700 space-y-1">
                        <li>• in London (大城市)</li>
                        <li>• in the park (公园内部)</li>
                        <li>• in the building (建筑物内)</li>
                        <li>• in my room (房间里)</li>
                    </ul>
                </div>
                <div class="bg-white rounded-lg p-4 border">
                    <h5 class="font-semibold mb-2 text-blue-700">使用 AT</h5>
                    <ul class="text-sm text-gray-700 space-y-1">
                        <li>• at the station (车站这个点)</li>
                        <li>• at the corner (角落这个位置)</li>
                        <li>• at school (学校这个场所)</li>
                        <li>• at home (家这个概念)</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="bg-green-50 border border-green-200 rounded-lg p-6 mt-6">
            <h4 class="text-lg font-semibold mb-3 text-green-800">IN 的扩展用法</h4>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <h5 class="font-semibold mb-2 text-green-700">地理位置</h5>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• in Asia 在亚洲</li>
                        <li>• in the north 在北方</li>
                        <li>• in the mountains 在山区</li>
                        <li>• in the countryside 在乡村</li>
                    </ul>
                </div>
                <div>
                    <h5 class="font-semibold mb-2 text-green-700">抽象概念</h5>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• in trouble 处于困境</li>
                        <li>• in danger 处于危险</li>
                        <li>• in love 恋爱中</li>
                        <li>• in peace 处于和平</li>
                    </ul>
                </div>
                <div>
                    <h5 class="font-semibold mb-2 text-green-700">媒介环境</h5>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• in English 用英语</li>
                        <li>• in the newspaper 在报纸上</li>
                        <li>• in the photo 在照片中</li>
                        <li>• in the mirror 在镜子里</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- ON 介词详解 -->
    <div class="mb-8">
        <h3 class="text-xl font-semibold mb-4 text-gray-800">ON - 在表面上</h3>
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-6">
            <h4 class="text-lg font-semibold mb-3 text-yellow-800">ON 的核心概念</h4>
            <p class="text-gray-700 mb-4">
                <span class="keyword">ON</span> 表示在某个表面上，或与某个表面接触。它强调的是"接触"和"支撑"的概念，
                通常指物体放置在另一个物体的表面上，或者附着在某个平面上。
            </p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h5 class="font-semibold mb-2 text-yellow-700">表面接触</h5>
                    <ul class="text-sm text-gray-700 space-y-1">
                        <li>• 物体放置在表面</li>
                        <li>• 附着或粘贴关系</li>
                        <li>• 支撑和承载</li>
                        <li>• 二维平面概念</li>
                    </ul>
                </div>
                <div>
                    <h5 class="font-semibold mb-2 text-yellow-700">特殊用法</h5>
                    <ul class="text-sm text-gray-700 space-y-1">
                        <li>• 交通工具表面</li>
                        <li>• 街道和道路</li>
                        <li>• 楼层概念</li>
                        <li>• 媒体和设备</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
            <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在桌子上</div>
                <div class="keyword text-lg mb-1">The book is on the table.</div>
                <div class="text-sm text-gray-600 mb-1">/ðə bʊk ɪz ɑn ðə ˈteɪbəl/</div>
                <div class="text-gray-700">书在桌子上。</div>
            </div>
            
            <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在墙上</div>
                <div class="keyword text-lg mb-1">The picture is on the wall.</div>
                <div class="text-sm text-gray-600 mb-1">/ðə ˈpɪktʃər ɪz ɑn ðə wɔl/</div>
                <div class="text-gray-700">画在墙上。</div>
            </div>
            
            <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在地板上</div>
                <div class="keyword text-lg mb-1">The carpet is on the floor.</div>
                <div class="text-sm text-gray-600 mb-1">/ðə ˈkɑrpɪt ɪz ɑn ðə flɔr/</div>
                <div class="text-gray-700">地毯在地板上。</div>
            </div>
            
            <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在街道上</div>
                <div class="keyword text-lg mb-1">I live on Oak Street.</div>
                <div class="text-sm text-gray-600 mb-1">/aɪ lɪv ɑn oʊk strit/</div>
                <div class="text-gray-700">我住在橡树街上。</div>
            </div>

            <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在楼层</div>
                <div class="keyword text-lg mb-1">My office is on the third floor.</div>
                <div class="text-sm text-gray-600 mb-1">/maɪ ˈɔfɪs ɪz ɑn ðə θɜrd flɔr/</div>
                <div class="text-gray-700">我的办公室在三楼。</div>
            </div>

            <div class="card bg-gray-50 border border-gray-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在公交车上</div>
                <div class="keyword text-lg mb-1">She's on the bus.</div>
                <div class="text-sm text-gray-600 mb-1">/ʃiz ɑn ðə bʌs/</div>
                <div class="text-gray-700">她在公交车上。</div>
            </div>
        </div>

        <div class="bg-gray-50 border border-gray-200 rounded-lg p-6 mt-6">
            <h4 class="text-lg font-semibold mb-3 text-gray-800">ON 的特殊用法详解</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h5 class="font-semibold mb-2 text-yellow-700">交通工具</h5>
                    <div class="bg-white rounded-lg p-4 border">
                        <p class="text-sm text-gray-700 mb-2">
                            <strong>使用 ON：</strong>大型公共交通工具
                        </p>
                        <ul class="text-sm text-gray-600 space-y-1">
                            <li>• on the bus 在公交车上</li>
                            <li>• on the train 在火车上</li>
                            <li>• on the plane 在飞机上</li>
                            <li>• on the ship 在船上</li>
                        </ul>
                        <p class="text-sm text-gray-700 mt-2 mb-2">
                            <strong>使用 IN：</strong>小型私人交通工具
                        </p>
                        <ul class="text-sm text-gray-600 space-y-1">
                            <li>• in the car 在汽车里</li>
                            <li>• in the taxi 在出租车里</li>
                        </ul>
                    </div>
                </div>
                <div>
                    <h5 class="font-semibold mb-2 text-yellow-700">媒体和设备</h5>
                    <div class="bg-white rounded-lg p-4 border">
                        <ul class="text-sm text-gray-600 space-y-1">
                            <li>• on TV 在电视上</li>
                            <li>• on the radio 在收音机上</li>
                            <li>• on the internet 在网上</li>
                            <li>• on the phone 在电话里</li>
                            <li>• on the computer 在电脑上</li>
                            <li>• on social media 在社交媒体上</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mt-6">
            <h4 class="text-lg font-semibold mb-3 text-yellow-800">ON 与其他介词的对比</h4>
            <div class="overflow-x-auto">
                <table class="w-full text-sm">
                    <thead>
                        <tr class="border-b">
                            <th class="text-left py-2 px-3">介词</th>
                            <th class="text-left py-2 px-3">概念</th>
                            <th class="text-left py-2 px-3">例句</th>
                        </tr>
                    </thead>
                    <tbody class="text-gray-700">
                        <tr class="border-b">
                            <td class="py-2 px-3 font-semibold">ON</td>
                            <td class="py-2 px-3">表面接触</td>
                            <td class="py-2 px-3">The book is on the table.</td>
                        </tr>
                        <tr class="border-b">
                            <td class="py-2 px-3 font-semibold">IN</td>
                            <td class="py-2 px-3">内部空间</td>
                            <td class="py-2 px-3">The book is in the bag.</td>
                        </tr>
                        <tr class="border-b">
                            <td class="py-2 px-3 font-semibold">AT</td>
                            <td class="py-2 px-3">具体位置点</td>
                            <td class="py-2 px-3">Meet me at the library.</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- UNDER 介词详解 -->
    <div class="mb-8">
        <h3 class="text-xl font-semibold mb-4 text-gray-800">UNDER - 在下面</h3>
        <div class="bg-purple-50 border border-purple-200 rounded-lg p-6 mb-6">
            <h4 class="text-lg font-semibold mb-3 text-purple-800">UNDER 的核心概念</h4>
            <p class="text-gray-700 mb-4">
                <span class="keyword">UNDER</span> 表示在某物的下方，通常有被覆盖、保护或隐藏的含义。
                它强调的是"在...之下"的位置关系，常常暗示着某种遮蔽或支撑的关系。
            </p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h5 class="font-semibold mb-2 text-purple-700">物理位置</h5>
                    <ul class="text-sm text-gray-700 space-y-1">
                        <li>• 直接在下方</li>
                        <li>• 被覆盖或遮蔽</li>
                        <li>• 受到保护</li>
                        <li>• 隐藏状态</li>
                    </ul>
                </div>
                <div>
                    <h5 class="font-semibold mb-2 text-purple-700">抽象概念</h5>
                    <ul class="text-sm text-gray-700 space-y-1">
                        <li>• 受到控制</li>
                        <li>• 处于影响之下</li>
                        <li>• 低于某个标准</li>
                        <li>• 在某种条件下</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
            <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在桌子下</div>
                <div class="keyword text-lg mb-1">The cat is under the table.</div>
                <div class="text-sm text-gray-600 mb-1">/ðə kæt ɪz ˈʌndər ðə ˈteɪbəl/</div>
                <div class="text-gray-700">猫在桌子下面。</div>
            </div>
            
            <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在桥下</div>
                <div class="keyword text-lg mb-1">The boat passed under the bridge.</div>
                <div class="text-sm text-gray-600 mb-1">/ðə boʊt pæst ˈʌndər ðə brɪdʒ/</div>
                <div class="text-gray-700">船从桥下经过。</div>
            </div>
            
            <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在床下</div>
                <div class="keyword text-lg mb-1">My shoes are under the bed.</div>
                <div class="text-sm text-gray-600 mb-1">/maɪ ʃuz ɑr ˈʌndər ðə bɛd/</div>
                <div class="text-gray-700">我的鞋子在床下。</div>
            </div>
            
            <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在雨伞下</div>
                <div class="keyword text-lg mb-1">We stood under the umbrella.</div>
                <div class="text-sm text-gray-600 mb-1">/wi stʊd ˈʌndər ði ʌmˈbrɛlə/</div>
                <div class="text-gray-700">我们站在雨伞下。</div>
            </div>

            <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在水下</div>
                <div class="keyword text-lg mb-1">The treasure is under water.</div>
                <div class="text-sm text-gray-600 mb-1">/ðə ˈtrɛʒər ɪz ˈʌndər ˈwɔtər/</div>
                <div class="text-gray-700">宝藏在水下。</div>
            </div>
        </div>

        <div class="bg-gray-50 border border-gray-200 rounded-lg p-6 mt-6">
            <h4 class="text-lg font-semibold mb-3 text-gray-800">UNDER 的扩展用法</h4>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <h5 class="font-semibold mb-2 text-purple-700">抽象概念</h5>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• under pressure 在压力下</li>
                        <li>• under control 在控制下</li>
                        <li>• under construction 在建设中</li>
                        <li>• under discussion 在讨论中</li>
                    </ul>
                </div>
                <div>
                    <h5 class="font-semibold mb-2 text-purple-700">年龄/数量</h5>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• under 18 18岁以下</li>
                        <li>• under $100 100美元以下</li>
                        <li>• under 5 minutes 5分钟以内</li>
                        <li>• under the limit 在限制以下</li>
                    </ul>
                </div>
                <div>
                    <h5 class="font-semibold mb-2 text-purple-700">权威/影响</h5>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• under his leadership 在他的领导下</li>
                        <li>• under the law 在法律下</li>
                        <li>• under her influence 在她的影响下</li>
                        <li>• under supervision 在监督下</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- OVER 介词详解 -->
    <div class="mb-8">
        <h3 class="text-xl font-semibold mb-4 text-gray-800">OVER - 在上方/越过</h3>
        <div class="bg-indigo-50 border border-indigo-200 rounded-lg p-6 mb-6">
            <h4 class="text-lg font-semibold mb-3 text-indigo-800">OVER 的核心概念</h4>
            <p class="text-gray-700 mb-4">
                <span class="keyword">OVER</span> 表示在某物的上方，或从一边到另一边的动作。它可以表示静态的位置关系，
                也可以表示动态的移动过程。OVER强调的是"覆盖"、"跨越"或"超过"的概念。
            </p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h5 class="font-semibold mb-2 text-indigo-700">位置关系</h5>
                    <ul class="text-sm text-gray-700 space-y-1">
                        <li>• 在上方悬挂</li>
                        <li>• 覆盖或遮盖</li>
                        <li>• 跨越空间</li>
                        <li>• 高于某物</li>
                    </ul>
                </div>
                <div>
                    <h5 class="font-semibold mb-2 text-indigo-700">动作概念</h5>
                    <ul class="text-sm text-gray-700 space-y-1">
                        <li>• 越过障碍</li>
                        <li>• 从上方通过</li>
                        <li>• 跳跃或飞越</li>
                        <li>• 遍布整个区域</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
            <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">飞过</div>
                <div class="keyword text-lg mb-1">The plane flew over the city.</div>
                <div class="text-sm text-gray-600 mb-1">/ðə pleɪn flu ˈoʊvər ðə ˈsɪti/</div>
                <div class="text-gray-700">飞机飞过城市上空。</div>
            </div>
            
            <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在头顶</div>
                <div class="keyword text-lg mb-1">The lamp hangs over the table.</div>
                <div class="text-sm text-gray-600 mb-1">/ðə læmp hæŋz ˈoʊvər ðə ˈteɪbəl/</div>
                <div class="text-gray-700">灯悬挂在桌子上方。</div>
            </div>
            
            <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">跳过</div>
                <div class="keyword text-lg mb-1">The horse jumped over the fence.</div>
                <div class="text-sm text-gray-600 mb-1">/ðə hɔrs dʒʌmpt ˈoʊvər ðə fɛns/</div>
                <div class="text-gray-700">马跳过了围栏。</div>
            </div>
            
            <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">遍布</div>
                <div class="keyword text-lg mb-1">Snow fell all over the ground.</div>
                <div class="text-sm text-gray-600 mb-1">/snoʊ fɛl ɔl ˈoʊvər ðə graʊnd/</div>
                <div class="text-gray-700">雪花飘洒在地面上。</div>
            </div>

            <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在河上</div>
                <div class="keyword text-lg mb-1">The bridge goes over the river.</div>
                <div class="text-sm text-gray-600 mb-1">/ðə brɪdʒ goʊz ˈoʊvər ðə ˈrɪvər/</div>
                <div class="text-gray-700">桥横跨河流。</div>
            </div>
        </div>

        <div class="bg-gray-50 border border-gray-200 rounded-lg p-6 mt-6">
            <h4 class="text-lg font-semibold mb-3 text-gray-800">OVER 与 ABOVE 的区别</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="bg-white rounded-lg p-4 border">
                    <h5 class="font-semibold mb-2 text-indigo-700">OVER</h5>
                    <p class="text-sm text-gray-700 mb-2">强调覆盖、跨越或直接在上方</p>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• The lamp hangs over the table</li>
                        <li>• Jump over the fence</li>
                        <li>• Clouds over the city</li>
                        <li>• 通常有接触或很近的距离</li>
                    </ul>
                </div>
                <div class="bg-white rounded-lg p-4 border">
                    <h5 class="font-semibold mb-2 text-blue-700">ABOVE</h5>
                    <p class="text-sm text-gray-700 mb-2">强调高度上的位置关系</p>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• The plane flies above the clouds</li>
                        <li>• The temperature is above zero</li>
                        <li>• Above sea level</li>
                        <li>• 通常有一定的距离</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="bg-indigo-50 border border-indigo-200 rounded-lg p-6 mt-6">
            <h4 class="text-lg font-semibold mb-3 text-indigo-800">OVER 的多重含义</h4>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                    <h5 class="font-semibold mb-2 text-indigo-700">空间位置</h5>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• over the bridge 在桥上</li>
                        <li>• over the hill 在山上</li>
                        <li>• over the sea 在海上</li>
                        <li>• over there 在那边</li>
                    </ul>
                </div>
                <div>
                    <h5 class="font-semibold mb-2 text-indigo-700">数量/程度</h5>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• over 100 people 超过100人</li>
                        <li>• over the limit 超过限制</li>
                        <li>• over budget 超出预算</li>
                        <li>• over and over 一遍又一遍</li>
                    </ul>
                </div>
                <div>
                    <h5 class="font-semibold mb-2 text-indigo-700">控制/影响</h5>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• power over others 对他人的权力</li>
                        <li>• victory over enemy 战胜敌人</li>
                        <li>• control over situation 控制局面</li>
                        <li>• influence over decision 影响决定</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- BETWEEN 介词详解 -->
    <div class="mb-8">
        <h3 class="text-xl font-semibold mb-4 text-gray-800">BETWEEN - 在两者之间</h3>
        <div class="bg-green-50 border border-green-200 rounded-lg p-6 mb-6">
            <h4 class="text-lg font-semibold mb-3 text-green-800">BETWEEN 的核心概念</h4>
            <p class="text-gray-700 mb-4">
                <span class="keyword">BETWEEN</span> 表示在两个人或物体之间的位置。它强调的是"二者之间"的概念，
                通常用于明确指出两个具体的对象，表示某物位于这两个对象的中间位置。
            </p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h5 class="font-semibold mb-2 text-green-700">使用条件</h5>
                    <ul class="text-sm text-gray-700 space-y-1">
                        <li>• 必须是两个对象</li>
                        <li>• 对象通常是具体的</li>
                        <li>• 表示中间位置</li>
                        <li>• 可以是物理或抽象概念</li>
                    </ul>
                </div>
                <div>
                    <h5 class="font-semibold mb-2 text-green-700">常见搭配</h5>
                    <ul class="text-sm text-gray-700 space-y-1">
                        <li>• between A and B</li>
                        <li>• between you and me</li>
                        <li>• between the lines</li>
                        <li>• between jobs</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
            <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">两个人之间</div>
                <div class="keyword text-lg mb-1">I sat between Tom and Jerry.</div>
                <div class="text-sm text-gray-600 mb-1">/aɪ sæt bɪˈtwin tɑm ænd ˈdʒɛri/</div>
                <div class="text-gray-700">我坐在汤姆和杰瑞之间。</div>
            </div>
            
            <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">两个城市之间</div>
                <div class="keyword text-lg mb-1">The road runs between two cities.</div>
                <div class="text-sm text-gray-600 mb-1">/ðə roʊd rʌnz bɪˈtwin tu ˈsɪtiz/</div>
                <div class="text-gray-700">这条路连接两个城市。</div>
            </div>
            
            <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">两棵树之间</div>
                <div class="keyword text-lg mb-1">The house stands between two trees.</div>
                <div class="text-sm text-gray-600 mb-1">/ðə haʊs stændz bɪˈtwin tu triz/</div>
                <div class="text-gray-700">房子坐落在两棵树之间。</div>
            </div>

            <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">时间之间</div>
                <div class="keyword text-lg mb-1">The meeting is between 2 and 4 PM.</div>
                <div class="text-sm text-gray-600 mb-1">/ðə ˈmitɪŋ ɪz bɪˈtwin tu ænd fɔr ˌpiˈɛm/</div>
                <div class="text-gray-700">会议在下午2点到4点之间。</div>
            </div>
        </div>

        <div class="bg-gray-50 border border-gray-200 rounded-lg p-6 mt-6">
            <h4 class="text-lg font-semibold mb-3 text-gray-800">BETWEEN 的扩展用法</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h5 class="font-semibold mb-2 text-green-700">抽象概念</h5>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• between jobs 在工作之间（失业）</li>
                        <li>• between you and me 你我之间（保密）</li>
                        <li>• between the lines 字里行间</li>
                        <li>• between life and death 生死之间</li>
                    </ul>
                </div>
                <div>
                    <h5 class="font-semibold mb-2 text-green-700">选择关系</h5>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• choose between A and B 在A和B之间选择</li>
                        <li>• difference between A and B A和B的区别</li>
                        <li>• relationship between A and B A和B的关系</li>
                        <li>• connection between A and B A和B的联系</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- AMONG 介词详解 -->
    <div class="mb-8">
        <h3 class="text-xl font-semibold mb-4 text-gray-800">AMONG - 在多者之间</h3>
        <div class="bg-orange-50 border border-orange-200 rounded-lg p-6 mb-6">
            <h4 class="text-lg font-semibold mb-3 text-orange-800">AMONG 的核心概念</h4>
            <p class="text-gray-700 mb-4">
                <span class="keyword">AMONG</span> 表示在三个或更多的人或物体之间。它强调的是"多者之中"的概念，
                通常用于表示某物或某人处于一个群体或集合的中间，被其他多个对象包围。
            </p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h5 class="font-semibold mb-2 text-orange-700">使用特点</h5>
                    <ul class="text-sm text-gray-700 space-y-1">
                        <li>• 三个或更多对象</li>
                        <li>• 表示被包围状态</li>
                        <li>• 强调群体概念</li>
                        <li>• 常用于抽象关系</li>
                    </ul>
                </div>
                <div>
                    <h5 class="font-semibold mb-2 text-orange-700">语义区别</h5>
                    <ul class="text-sm text-gray-700 space-y-1">
                        <li>• 与BETWEEN的区别</li>
                        <li>• 群体中的一员</li>
                        <li>• 分布在多个对象中</li>
                        <li>• 表示归属感</li>
                    </ul>
                </div>
            </div>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
            <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在朋友中</div>
                <div class="keyword text-lg mb-1">She is popular among her friends.</div>
                <div class="text-sm text-gray-600 mb-1">/ʃi ɪz ˈpɑpjələr əˈmʌŋ hər frɛndz/</div>
                <div class="text-gray-700">她在朋友中很受欢迎。</div>
            </div>
            
            <div class="card bg-gray-50 border border-gray-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在花丛中</div>
                <div class="keyword text-lg mb-1">The bee flies among the flowers.</div>
                <div class="text-sm text-gray-600 mb-1">/ðə bi flaɪz əˈmʌŋ ðə ˈflaʊərz/</div>
                <div class="text-gray-700">蜜蜂在花丛中飞舞。</div>
            </div>
            
            <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在学生中</div>
                <div class="keyword text-lg mb-1">He is the tallest among all students.</div>
                <div class="text-sm text-gray-600 mb-1">/hi ɪz ðə ˈtɔləst əˈmʌŋ ɔl ˈstudənts/</div>
                <div class="text-gray-700">他是所有学生中最高的。</div>
            </div>

            <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在人群中</div>
                <div class="keyword text-lg mb-1">I found him among the crowd.</div>
                <div class="text-sm text-gray-600 mb-1">/aɪ faʊnd hɪm əˈmʌŋ ðə kraʊd/</div>
                <div class="text-gray-700">我在人群中找到了他。</div>
            </div>
        </div>

        <div class="bg-gray-50 border border-gray-200 rounded-lg p-6 mt-6">
            <h4 class="text-lg font-semibold mb-3 text-gray-800">BETWEEN 与 AMONG 的详细对比</h4>
            <div class="overflow-x-auto">
                <table class="w-full text-sm border-collapse">
                    <thead>
                        <tr class="border-b-2">
                            <th class="text-left py-3 px-4 font-semibold">方面</th>
                            <th class="text-left py-3 px-4 font-semibold text-green-700">BETWEEN</th>
                            <th class="text-left py-3 px-4 font-semibold text-orange-700">AMONG</th>
                        </tr>
                    </thead>
                    <tbody class="text-gray-700">
                        <tr class="border-b">
                            <td class="py-2 px-4 font-medium">对象数量</td>
                            <td class="py-2 px-4">两个对象</td>
                            <td class="py-2 px-4">三个或更多对象</td>
                        </tr>
                        <tr class="border-b">
                            <td class="py-2 px-4 font-medium">位置概念</td>
                            <td class="py-2 px-4">在两者中间</td>
                            <td class="py-2 px-4">被多者包围</td>
                        </tr>
                        <tr class="border-b">
                            <td class="py-2 px-4 font-medium">例句</td>
                            <td class="py-2 px-4">between A and B</td>
                            <td class="py-2 px-4">among many people</td>
                        </tr>
                        <tr class="border-b">
                            <td class="py-2 px-4 font-medium">关系类型</td>
                            <td class="py-2 px-4">一对一关系</td>
                            <td class="py-2 px-4">一对多关系</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div class="bg-orange-50 border border-orange-200 rounded-lg p-6 mt-6">
            <h4 class="text-lg font-semibold mb-3 text-orange-800">AMONG 的常见用法</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h5 class="font-semibold mb-2 text-orange-700">群体关系</h5>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• among friends 在朋友中</li>
                        <li>• among colleagues 在同事中</li>
                        <li>• among family members 在家庭成员中</li>
                        <li>• among the best 在最好的当中</li>
                    </ul>
                </div>
                <div>
                    <h5 class="font-semibold mb-2 text-orange-700">分布概念</h5>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• distribute among 在...中分配</li>
                        <li>• spread among 在...中传播</li>
                        <li>• popular among 在...中受欢迎</li>
                        <li>• common among 在...中常见</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- BESIDE 介词详解 -->
    <div class="mb-8">
        <h3 class="text-xl font-semibold mb-4 text-gray-800">BESIDE - 在旁边</h3>
        <p class="text-gray-700 mb-4">
            <span class="keyword">BESIDE</span> 表示在某物的旁边，紧挨着。
        </p>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
            <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在河边</div>
                <div class="keyword text-lg mb-1">The house is beside the river.</div>
                <div class="text-sm text-gray-600 mb-1">/ðə haʊs ɪz bɪˈsaɪd ðə ˈrɪvər/</div>
                <div class="text-gray-700">房子在河边。</div>
            </div>
            
            <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">坐在旁边</div>
                <div class="keyword text-lg mb-1">She sits beside me.</div>
                <div class="text-sm text-gray-600 mb-1">/ʃi sɪts bɪˈsaɪd mi/</div>
                <div class="text-gray-700">她坐在我旁边。</div>
            </div>
            
            <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在床边</div>
                <div class="keyword text-lg mb-1">The lamp is beside the bed.</div>
                <div class="text-sm text-gray-600 mb-1">/ðə læmp ɪz bɪˈsaɪd ðə bɛd/</div>
                <div class="text-gray-700">台灯在床边。</div>
            </div>
        </div>
    </div>

    <!-- BEHIND 介词详解 -->
    <div class="mb-8">
        <h3 class="text-xl font-semibold mb-4 text-gray-800">BEHIND - 在后面</h3>
        <p class="text-gray-700 mb-4">
            <span class="keyword">BEHIND</span> 表示在某物的后面或背后。
        </p>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
            <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在树后</div>
                <div class="keyword text-lg mb-1">The dog is hiding behind the tree.</div>
                <div class="text-sm text-gray-600 mb-1">/ðə dɔg ɪz ˈhaɪdɪŋ bɪˈhaɪnd ðə tri/</div>
                <div class="text-gray-700">狗躲在树后面。</div>
            </div>
            
            <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在房子后</div>
                <div class="keyword text-lg mb-1">The garden is behind the house.</div>
                <div class="text-sm text-gray-600 mb-1">/ðə ˈgɑrdən ɪz bɪˈhaɪnd ðə haʊs/</div>
                <div class="text-gray-700">花园在房子后面。</div>
            </div>
            
            <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">跟在后面</div>
                <div class="keyword text-lg mb-1">The children walked behind their teacher.</div>
                <div class="text-sm text-gray-600 mb-1">/ðə ˈtʃɪldrən wɔkt bɪˈhaɪnd ðɛr ˈtitʃər/</div>
                <div class="text-gray-700">孩子们跟在老师后面走。</div>
            </div>
        </div>
    </div>

    <!-- IN FRONT OF 介词详解 -->
    <div class="mb-8">
        <h3 class="text-xl font-semibold mb-4 text-gray-800">IN FRONT OF - 在前面</h3>
        <p class="text-gray-700 mb-4">
            <span class="keyword">IN FRONT OF</span> 表示在某物的前面或前方。
        </p>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
            <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在学校前</div>
                <div class="keyword text-lg mb-1">The bus stops in front of the school.</div>
                <div class="text-sm text-gray-600 mb-1">/ðə bʌs stɑps ɪn frʌnt ʌv ðə skul/</div>
                <div class="text-gray-700">公交车停在学校前面。</div>
            </div>
            
            <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">站在前面</div>
                <div class="keyword text-lg mb-1">She stands in front of the mirror.</div>
                <div class="text-sm text-gray-600 mb-1">/ʃi stændz ɪn frʌnt ʌv ðə ˈmɪrər/</div>
                <div class="text-gray-700">她站在镜子前面。</div>
            </div>
            
            <div class="card bg-gray-50 border border-gray-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在电视前</div>
                <div class="keyword text-lg mb-1">The sofa is in front of the TV.</div>
                <div class="text-sm text-gray-600 mb-1">/ðə ˈsoʊfə ɪz ɪn frʌnt ʌv ðə ˌtiˈvi/</div>
                <div class="text-gray-700">沙发在电视前面。</div>
            </div>
        </div>
    </div>

    <!-- NEAR 介词详解 -->
    <div class="mb-8">
        <h3 class="text-xl font-semibold mb-4 text-gray-800">NEAR - 在附近</h3>
        <p class="text-gray-700 mb-4">
            <span class="keyword">NEAR</span> 表示在某物的附近，距离不远。
        </p>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
            <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在银行附近</div>
                <div class="keyword text-lg mb-1">The restaurant is near the bank.</div>
                <div class="text-sm text-gray-600 mb-1">/ðə ˈrɛstərɑnt ɪz nɪr ðə bæŋk/</div>
                <div class="text-gray-700">餐厅在银行附近。</div>
            </div>
            
            <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">住在附近</div>
                <div class="keyword text-lg mb-1">I live near the park.</div>
                <div class="text-sm text-gray-600 mb-1">/aɪ lɪv nɪr ðə pɑrk/</div>
                <div class="text-gray-700">我住在公园附近。</div>
            </div>
            
            <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">靠近海边</div>
                <div class="keyword text-lg mb-1">The hotel is near the beach.</div>
                <div class="text-sm text-gray-600 mb-1">/ðə hoʊˈtɛl ɪz nɪr ðə bitʃ/</div>
                <div class="text-gray-700">酒店在海滩附近。</div>
            </div>
        </div>
    </div>

    <!-- ABOVE 介词详解 -->
    <div class="mb-8">
        <h3 class="text-xl font-semibold mb-4 text-gray-800">ABOVE - 在上方</h3>
        <p class="text-gray-700 mb-4">
            <span class="keyword">ABOVE</span> 表示在某物的上方，通常指高度上的位置关系。
        </p>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
            <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在云层上</div>
                <div class="keyword text-lg mb-1">The plane flies above the clouds.</div>
                <div class="text-sm text-gray-600 mb-1">/ðə pleɪn flaɪz əˈbʌv ðə klaʊdz/</div>
                <div class="text-gray-700">飞机在云层上方飞行。</div>
            </div>
            
            <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">挂在上方</div>
                <div class="keyword text-lg mb-1">The clock hangs above the door.</div>
                <div class="text-sm text-gray-600 mb-1">/ðə klɑk hæŋz əˈbʌv ðə dɔr/</div>
                <div class="text-gray-700">时钟挂在门的上方。</div>
            </div>
            
            <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">高于海平面</div>
                <div class="keyword text-lg mb-1">The mountain is above sea level.</div>
                <div class="text-sm text-gray-600 mb-1">/ðə ˈmaʊntən ɪz əˈbʌv si ˈlɛvəl/</div>
                <div class="text-gray-700">山在海平面以上。</div>
            </div>
        </div>
    </div>

    <!-- BELOW 介词详解 -->
    <div class="mb-8">
        <h3 class="text-xl font-semibold mb-4 text-gray-800">BELOW - 在下方</h3>
        <p class="text-gray-700 mb-4">
            <span class="keyword">BELOW</span> 表示在某物的下方，通常指高度上的位置关系。
        </p>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
            <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在海平面下</div>
                <div class="keyword text-lg mb-1">The submarine is below sea level.</div>
                <div class="text-sm text-gray-600 mb-1">/ðə ˈsʌbmərin ɪz bɪˈloʊ si ˈlɛvəl/</div>
                <div class="text-gray-700">潜水艇在海平面以下。</div>
            </div>
            
            <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在山脚下</div>
                <div class="keyword text-lg mb-1">The village is below the mountain.</div>
                <div class="text-sm text-gray-600 mb-1">/ðə ˈvɪlɪdʒ ɪz bɪˈloʊ ðə ˈmaʊntən/</div>
                <div class="text-gray-700">村庄在山脚下。</div>
            </div>
            
            <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在零度以下</div>
                <div class="keyword text-lg mb-1">The temperature is below zero.</div>
                <div class="text-sm text-gray-600 mb-1">/ðə ˈtɛmprətʃər ɪz bɪˈloʊ ˈzɪroʊ/</div>
                <div class="text-gray-700">温度在零度以下。</div>
            </div>
        </div>
    </div>

    <!-- ACROSS 介词详解 -->
    <div class="mb-8">
        <h3 class="text-xl font-semibold mb-4 text-gray-800">ACROSS - 穿过/对面</h3>
        <p class="text-gray-700 mb-4">
            <span class="keyword">ACROSS</span> 表示从一边到另一边，或在某物的对面。
        </p>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
            <div class="card bg-gray-50 border border-gray-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">穿过马路</div>
                <div class="keyword text-lg mb-1">She walked across the street.</div>
                <div class="text-sm text-gray-600 mb-1">/ʃi wɔkt əˈkrɔs ðə strit/</div>
                <div class="text-gray-700">她穿过了马路。</div>
            </div>
            
            <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">在对面</div>
                <div class="keyword text-lg mb-1">The bank is across from the school.</div>
                <div class="text-sm text-gray-600 mb-1">/ðə bæŋk ɪz əˈkrɔs frʌm ðə skul/</div>
                <div class="text-gray-700">银行在学校对面。</div>
            </div>
            
            <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                <div class="text-sm text-gray-500 mb-1">游过河</div>
                <div class="keyword text-lg mb-1">The fish swam across the river.</div>
                <div class="text-sm text-gray-600 mb-1">/ðə fɪʃ swæm əˈkrɔs ðər ˈrɪvər/</div>
                <div class="text-gray-700">鱼游过了河。</div>
            </div>
        </div>
    </div>

    <!-- 其他重要地点介词 -->
    <div class="mb-8">
        <h3 class="text-xl font-semibold mb-4 text-gray-800">其他重要地点介词</h3>

        <!-- NEXT TO / BESIDE -->
        <div class="bg-teal-50 border border-teal-200 rounded-lg p-6 mb-6">
            <h4 class="text-lg font-semibold mb-3 text-teal-800">NEXT TO / BESIDE - 紧挨着/在旁边</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h5 class="font-semibold mb-2 text-teal-700">NEXT TO</h5>
                    <p class="text-sm text-gray-700 mb-2">强调紧邻关系，通常指相邻的位置</p>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• The bank is next to the post office.</li>
                        <li>• Sit next to me.</li>
                        <li>• The park is next to our school.</li>
                    </ul>
                </div>
                <div>
                    <h5 class="font-semibold mb-2 text-teal-700">BESIDE</h5>
                    <p class="text-sm text-gray-700 mb-2">表示在旁边，语气更正式</p>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• She stood beside the window.</li>
                        <li>• The lamp is beside the bed.</li>
                        <li>• Walk beside me.</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- OPPOSITE / ACROSS FROM -->
        <div class="bg-cyan-50 border border-cyan-200 rounded-lg p-6 mb-6">
            <h4 class="text-lg font-semibold mb-3 text-cyan-800">OPPOSITE / ACROSS FROM - 在对面</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h5 class="font-semibold mb-2 text-cyan-700">OPPOSITE</h5>
                    <p class="text-sm text-gray-700 mb-2">表示正对面的位置关系</p>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• The hotel is opposite the station.</li>
                        <li>• She sat opposite me.</li>
                        <li>• The shop is opposite the church.</li>
                    </ul>
                </div>
                <div>
                    <h5 class="font-semibold mb-2 text-cyan-700">ACROSS FROM</h5>
                    <p class="text-sm text-gray-700 mb-2">英语中更常用，表示对面</p>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• The restaurant is across from the park.</li>
                        <li>• I live across from the library.</li>
                        <li>• The store is across from here.</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- AROUND / ROUND -->
        <div class="bg-pink-50 border border-pink-200 rounded-lg p-6 mb-6">
            <h4 class="text-lg font-semibold mb-3 text-pink-800">AROUND / ROUND - 围绕/周围</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h5 class="font-semibold mb-2 text-pink-700">位置概念</h5>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• The children sat around the teacher.</li>
                        <li>• There are trees around the house.</li>
                        <li>• We walked around the lake.</li>
                        <li>• Look around you.</li>
                    </ul>
                </div>
                <div>
                    <h5 class="font-semibold mb-2 text-pink-700">抽象用法</h5>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• around the world 世界各地</li>
                        <li>• around the corner 在拐角处</li>
                        <li>• around here 在这附近</li>
                        <li>• around the clock 全天候</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- INSIDE / OUTSIDE -->
        <div class="bg-gray-50 border border-gray-200 rounded-lg p-6 mb-6">
            <h4 class="text-lg font-semibold mb-3 text-gray-800">INSIDE / OUTSIDE - 内部/外部</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h5 class="font-semibold mb-2 text-gray-700">INSIDE</h5>
                    <p class="text-sm text-gray-700 mb-2">强调在内部，比IN更明确</p>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• It's warm inside the house.</li>
                        <li>• The keys are inside my bag.</li>
                        <li>• Come inside, it's raining.</li>
                        <li>• inside information 内部消息</li>
                    </ul>
                </div>
                <div>
                    <h5 class="font-semibold mb-2 text-gray-700">OUTSIDE</h5>
                    <p class="text-sm text-gray-700 mb-2">表示在外部或外面</p>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• The dog is outside the house.</li>
                        <li>• Wait outside the office.</li>
                        <li>• It's cold outside.</li>
                        <li>• outside working hours 工作时间外</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- WITHIN / BEYOND -->
        <div class="bg-indigo-50 border border-indigo-200 rounded-lg p-6">
            <h4 class="text-lg font-semibold mb-3 text-indigo-800">WITHIN / BEYOND - 在...范围内/超出</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h5 class="font-semibold mb-2 text-indigo-700">WITHIN</h5>
                    <p class="text-sm text-gray-700 mb-2">表示在某个范围或界限内</p>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• within the city limits 在市区范围内</li>
                        <li>• within walking distance 步行距离内</li>
                        <li>• within reach 在够得着的范围内</li>
                        <li>• within sight 在视线范围内</li>
                    </ul>
                </div>
                <div>
                    <h5 class="font-semibold mb-2 text-indigo-700">BEYOND</h5>
                    <p class="text-sm text-gray-700 mb-2">表示超出某个范围或界限</p>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• beyond the mountains 在山的那边</li>
                        <li>• beyond the horizon 在地平线外</li>
                        <li>• beyond my reach 超出我的能力</li>
                        <li>• beyond belief 难以置信</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- 地点介词综合应用 -->
    <div class="mb-8">
        <h3 class="text-xl font-semibold mb-4 text-gray-800">地点介词综合应用与总结</h3>

        <!-- 使用要点总结 -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
            <h4 class="text-lg font-semibold mb-4 text-blue-800">核心使用要点</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h5 class="font-semibold mb-3 text-blue-700">基础三大介词</h5>
                    <ul class="space-y-2 text-gray-700">
                        <li><span class="keyword">AT</span> - 具体地点和活动场所</li>
                        <li><span class="keyword">IN</span> - 封闭空间和大范围区域</li>
                        <li><span class="keyword">ON</span> - 表面接触和特定位置</li>
                    </ul>
                </div>
                <div>
                    <h5 class="font-semibold mb-3 text-blue-700">方向位置介词</h5>
                    <ul class="space-y-2 text-gray-700">
                        <li><span class="keyword">ABOVE/BELOW</span> - 垂直高度关系</li>
                        <li><span class="keyword">OVER/UNDER</span> - 覆盖和遮蔽关系</li>
                        <li><span class="keyword">BETWEEN/AMONG</span> - 二者间/多者间</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 常见错误与纠正 -->
        <div class="bg-red-50 border border-red-200 rounded-lg p-6 mb-6">
            <h4 class="text-lg font-semibold mb-4 text-red-800">常见错误与纠正</h4>
            <div class="space-y-4">
                <div class="bg-white rounded-lg p-4 border">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <h5 class="font-semibold mb-2 text-red-700">❌ 错误用法</h5>
                            <ul class="text-sm text-gray-700 space-y-1">
                                <li>• I live <span class="text-red-600">in</span> 123 Main Street</li>
                                <li>• The book is <span class="text-red-600">in</span> the table</li>
                                <li>• <span class="text-red-600">Among</span> Tom and Jerry</li>
                                <li>• I'm <span class="text-red-600">in</span> home</li>
                            </ul>
                        </div>
                        <div>
                            <h5 class="font-semibold mb-2 text-green-700">✅ 正确用法</h5>
                            <ul class="text-sm text-gray-700 space-y-1">
                                <li>• I live <span class="text-green-600">at</span> 123 Main Street</li>
                                <li>• The book is <span class="text-green-600">on</span> the table</li>
                                <li>• <span class="text-green-600">Between</span> Tom and Jerry</li>
                                <li>• I'm <span class="text-green-600">at</span> home</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 记忆技巧 -->
        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-6">
            <h4 class="text-lg font-semibold mb-4 text-yellow-800">记忆技巧与方法</h4>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="bg-white rounded-lg p-4 border">
                    <h5 class="font-semibold mb-2 text-yellow-700">视觉化记忆</h5>
                    <ul class="text-sm text-gray-700 space-y-1">
                        <li>• 想象具体场景</li>
                        <li>• 画出位置关系图</li>
                        <li>• 使用空间想象</li>
                        <li>• 结合实物练习</li>
                    </ul>
                </div>
                <div class="bg-white rounded-lg p-4 border">
                    <h5 class="font-semibold mb-2 text-yellow-700">对比记忆</h5>
                    <ul class="text-sm text-gray-700 space-y-1">
                        <li>• AT vs IN vs ON</li>
                        <li>• OVER vs ABOVE</li>
                        <li>• BETWEEN vs AMONG</li>
                        <li>• BESIDE vs NEAR</li>
                    </ul>
                </div>
                <div class="bg-white rounded-lg p-4 border">
                    <h5 class="font-semibold mb-2 text-yellow-700">语境记忆</h5>
                    <ul class="text-sm text-gray-700 space-y-1">
                        <li>• 固定搭配记忆</li>
                        <li>• 句子整体记忆</li>
                        <li>• 情景对话练习</li>
                        <li>• 实际应用练习</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- 实用场景应用 -->
        <div class="bg-green-50 border border-green-200 rounded-lg p-6 mb-6">
            <h4 class="text-lg font-semibold mb-4 text-green-800">实用场景应用</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <h5 class="font-semibold mb-3 text-green-700">问路与指路</h5>
                    <div class="bg-white rounded-lg p-4 border">
                        <ul class="text-sm text-gray-700 space-y-1">
                            <li>• "The bank is <strong>next to</strong> the post office."</li>
                            <li>• "Go straight and turn left <strong>at</strong> the corner."</li>
                            <li>• "The restaurant is <strong>across from</strong> the park."</li>
                            <li>• "You'll find it <strong>between</strong> the two buildings."</li>
                        </ul>
                    </div>
                </div>
                <div>
                    <h5 class="font-semibold mb-3 text-green-700">描述位置</h5>
                    <div class="bg-white rounded-lg p-4 border">
                        <ul class="text-sm text-gray-700 space-y-1">
                            <li>• "My keys are <strong>on</strong> the kitchen table."</li>
                            <li>• "The cat is hiding <strong>under</strong> the bed."</li>
                            <li>• "There's a beautiful painting <strong>above</strong> the fireplace."</li>
                            <li>• "I left my bag <strong>in</strong> the car."</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

    </div>

</body>
</html>