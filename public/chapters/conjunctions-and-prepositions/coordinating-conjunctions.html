<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>并列连词</title>
    <script src="/static/tailwindcss-3.4.17.js"></script>
    <style>
        .keyword {
            color: #3d74ed;
            font-weight: bold;
        }
        .card {
            min-height: 120px;
        }
    </style>
</head>
<body class="bg-white">
<div class="p-6">
        
        <!-- 并列连词概述 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">并列连词基础</h2>
            <p class="text-gray-700 mb-4 leading-7">并列连词（Coordinating Conjunctions）是用来连接两个语法地位相等的词、短语或句子的连词。最常见的并列连词可以用缩写词"FANBOYS"来记忆：<span class="keyword">F</span>or, <span class="keyword">A</span>nd, <span class="keyword">N</span>or, <span class="keyword">B</span>ut, <span class="keyword">O</span>r, <span class="keyword">Y</span>et, <span class="keyword">S</span>o。</p>
            <p class="text-gray-700 leading-7">这些连词在英语中使用频率极高，掌握它们的用法对于提高英语表达的流畅性和准确性至关重要。</p>
        </section>

        <!-- AND - 表示并列和递增 -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">And - 表示并列和递增</h3>
            <p class="text-gray-700 mb-4 leading-7"><span class="keyword">And</span> 是最基础的并列连词，用于连接相似或相关的内容，表示"和"、"与"、"而且"的含义。</p>
            
            <div class="mb-4">
                <h4 class="text-lg font-medium mb-3 text-gray-700">基本用法示例</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">连接名词</div>
                        <div class="keyword text-lg mb-1">I like coffee and tea.</div>
                        <div class="text-sm text-gray-600 mb-1">/aɪ laɪk ˈkɔːfi ænd tiː/</div>
                        <div class="text-gray-700">我喜欢咖啡和茶。</div>
                    </div>
                    <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">连接形容词</div>
                        <div class="keyword text-lg mb-1">She is smart and beautiful.</div>
                        <div class="text-sm text-gray-600 mb-1">/ʃi ɪz smɑːrt ænd ˈbjuːtɪfəl/</div>
                        <div class="text-gray-700">她聪明又美丽。</div>
                    </div>
                    <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">连接动词</div>
                        <div class="keyword text-lg mb-1">He runs and swims every day.</div>
                        <div class="text-sm text-gray-600 mb-1">/hi rʌnz ænd swɪmz ˈevri deɪ/</div>
                        <div class="text-gray-700">他每天跑步和游泳。</div>
                    </div>
                    <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">连接句子</div>
                        <div class="keyword text-lg mb-1">I finished my work, and I went home.</div>
                        <div class="text-sm text-gray-600 mb-1">/aɪ ˈfɪnɪʃt maɪ wɜːrk ænd aɪ went hoʊm/</div>
                        <div class="text-gray-700">我完成了工作，然后回家了。</div>
                    </div>
                </div>
            </div>

            <div class="mb-4">
                <h4 class="text-lg font-medium mb-3 text-gray-700">特殊用法</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">表示结果</div>
                        <div class="keyword text-lg mb-1">Study hard, and you will succeed.</div>
                        <div class="text-sm text-gray-600 mb-1">/ˈstʌdi hɑːrd ænd ju wɪl səkˈsiːd/</div>
                        <div class="text-gray-700">努力学习，你就会成功。</div>
                    </div>
                    <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">表示强调</div>
                        <div class="keyword text-lg mb-1">Again and again she called.</div>
                        <div class="text-sm text-gray-600 mb-1">/əˈgen ænd əˈgen ʃi kɔːld/</div>
                        <div class="text-gray-700">她一次又一次地打电话。</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- BUT - 表示转折 -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">But - 表示转折对比</h3>
            <p class="text-gray-700 mb-4 leading-7"><span class="keyword">But</span> 用于表示转折、对比或相反的情况，相当于中文的"但是"、"可是"、"然而"。</p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">基本转折</div>
                    <div class="keyword text-lg mb-1">I like pizza, but I'm on a diet.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ laɪk ˈpiːtsə bʌt aɪm ɑːn ə ˈdaɪət/</div>
                    <div class="text-gray-700">我喜欢披萨，但我在节食。</div>
                </div>
                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">性格对比</div>
                    <div class="keyword text-lg mb-1">She is young but wise.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi ɪz jʌŋ bʌt waɪz/</div>
                    <div class="text-gray-700">她年轻但很聪慧。</div>
                </div>
                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">能力对比</div>
                    <div class="keyword text-lg mb-1">He can read, but he can't write.</div>
                    <div class="text-sm text-gray-600 mb-1">/hi kæn riːd bʌt hi kænt raɪt/</div>
                    <div class="text-gray-700">他会读，但不会写。</div>
                </div>
                <div class="card bg-gray-50 border border-gray-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">情况转折</div>
                    <div class="keyword text-lg mb-1">It's expensive, but it's worth it.</div>
                    <div class="text-sm text-gray-600 mb-1">/ɪts ɪkˈspensɪv bʌt ɪts wɜːrθ ɪt/</div>
                    <div class="text-gray-700">很贵，但物有所值。</div>
                </div>
            </div>
        </section>

        <!-- OR - 表示选择 -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">Or - 表示选择</h3>
            <p class="text-gray-700 mb-4 leading-7"><span class="keyword">Or</span> 用于表示选择关系，相当于中文的"或者"、"还是"。可以表示两个或多个选项中的任选其一。</p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">二选一</div>
                    <div class="keyword text-lg mb-1">Do you want tea or coffee?</div>
                    <div class="text-sm text-gray-600 mb-1">/du ju wɑːnt tiː ɔːr ˈkɔːfi/</div>
                    <div class="text-gray-700">你想要茶还是咖啡？</div>
                </div>
                <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">多个选择</div>
                    <div class="keyword text-lg mb-1">We can walk, drive, or take the bus.</div>
                    <div class="text-sm text-gray-600 mb-1">/wi kæn wɔːk draɪv ɔːr teɪk ðə bʌs/</div>
                    <div class="text-gray-700">我们可以走路、开车或坐公交。</div>
                </div>
                <div class="card bg-lime-50 border border-lime-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">否则/要不然</div>
                    <div class="keyword text-lg mb-1">Hurry up, or you'll be late.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈhɜːri ʌp ɔːr jul bi leɪt/</div>
                    <div class="text-gray-700">快点，否则你会迟到的。</div>
                </div>
                <div class="card bg-amber-50 border border-amber-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">补充说明</div>
                    <div class="keyword text-lg mb-1">He is a doctor or something like that.</div>
                    <div class="text-sm text-gray-600 mb-1">/hi ɪz ə ˈdɑːktər ɔːr ˈsʌmθɪŋ laɪk ðæt/</div>
                    <div class="text-gray-700">他是医生还是类似的职业。</div>
                </div>
            </div>
        </section>

        <!-- SO - 表示因果关系 -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">So - 表示因果关系</h3>
            <p class="text-gray-700 mb-4 leading-7"><span class="keyword">So</span> 用于表示结果或因果关系，相当于中文的"所以"、"因此"、"那么"。</p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div class="card bg-violet-50 border border-violet-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">因果关系</div>
                    <div class="keyword text-lg mb-1">It was raining, so I stayed home.</div>
                    <div class="text-sm text-gray-600 mb-1">/ɪt wʌz ˈreɪnɪŋ soʊ aɪ steɪd hoʊm/</div>
                    <div class="text-gray-700">下雨了，所以我待在家里。</div>
                </div>
                <div class="card bg-rose-50 border border-rose-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">结果导向</div>
                    <div class="keyword text-lg mb-1">I was tired, so I went to bed early.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ wʌz ˈtaɪərd soʊ aɪ went tu bed ˈɜːrli/</div>
                    <div class="text-gray-700">我累了，所以很早就睡了。</div>
                </div>
                <div class="card bg-sky-50 border border-sky-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">逻辑推理</div>
                    <div class="keyword text-lg mb-1">She studied hard, so she passed the exam.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi ˈstʌdid hɑːrd soʊ ʃi pæst ði ɪgˈzæm/</div>
                    <div class="text-gray-700">她努力学习，所以通过了考试。</div>
                </div>
                <div class="card bg-stone-50 border border-stone-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">决定行动</div>
                    <div class="keyword text-lg mb-1">The store was closed, so we went elsewhere.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə stɔːr wʌz kloʊzd soʊ wi went ˈelsˌwer/</div>
                    <div class="text-gray-700">商店关门了，所以我们去了别处。</div>
                </div>
            </div>
        </section>

        <!-- YET - 表示转折 -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">Yet - 表示意外转折</h3>
            <p class="text-gray-700 mb-4 leading-7"><span class="keyword">Yet</span> 表示意外的转折或对比，比but更正式，常用于书面语，相当于中文的"然而"、"可是"、"不过"。</p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div class="card bg-fuchsia-50 border border-fuchsia-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">意外转折</div>
                    <div class="keyword text-lg mb-1">He is old, yet he is very active.</div>
                    <div class="text-sm text-gray-600 mb-1">/hi ɪz oʊld jet hi ɪz ˈveri ˈæktɪv/</div>
                    <div class="text-gray-700">他年纪大了，然而却很活跃。</div>
                </div>
                <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">能力反差</div>
                    <div class="keyword text-lg mb-1">She is small, yet she is strong.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi ɪz smɔːl jet ʃi ɪz strɔːŋ/</div>
                    <div class="text-gray-700">她个子小，但很强壮。</div>
                </div>
                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">情况相反</div>
                    <div class="keyword text-lg mb-1">It's simple, yet effective.</div>
                    <div class="text-sm text-gray-600 mb-1">/ɪts ˈsɪmpəl jet ɪˈfektɪv/</div>
                    <div class="text-gray-700">这很简单，却很有效。</div>
                </div>
                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">期望相反</div>
                    <div class="keyword text-lg mb-1">The task was difficult, yet we completed it.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə tæsk wʌz ˈdɪfɪkəlt jet wi kəmˈpliːtɪd ɪt/</div>
                    <div class="text-gray-700">任务很困难，但我们完成了。</div>
                </div>
            </div>
        </section>

        <!-- FOR - 表示原因 -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">For - 表示原因解释</h3>
            <p class="text-gray-700 mb-4 leading-7"><span class="keyword">For</span> 用于表示原因或理由，较为正式，多用于书面语，相当于中文的"因为"、"由于"。</p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">解释原因</div>
                    <div class="keyword text-lg mb-1">I must leave now, for it's getting late.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ mʌst liːv naʊ fɔːr ɪts ˈgetɪŋ leɪt/</div>
                    <div class="text-gray-700">我现在必须走了，因为天晚了。</div>
                </div>
                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">推理依据</div>
                    <div class="keyword text-lg mb-1">He must be home, for his car is here.</div>
                    <div class="text-sm text-gray-600 mb-1">/hi mʌst bi hoʊm fɔːr hɪz kɑːr ɪz hɪr/</div>
                    <div class="text-gray-700">他一定在家，因为他的车在这里。</div>
                </div>
                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">行为理由</div>
                    <div class="keyword text-lg mb-1">She smiled, for she was happy.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi smaɪld fɔːr ʃi wʌz ˈhæpi/</div>
                    <div class="text-gray-700">她笑了，因为她很开心。</div>
                </div>
                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">状态说明</div>
                    <div class="keyword text-lg mb-1">The ground is wet, for it rained last night.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə graʊnd ɪz wet fɔːr ɪt reɪnd læst naɪt/</div>
                    <div class="text-gray-700">地面是湿的，因为昨晚下雨了。</div>
                </div>
            </div>
        </section>

        <!-- NOR - 表示递进否定 -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">Nor - 表示递进否定</h3>
            <p class="text-gray-700 mb-4 leading-7"><span class="keyword">Nor</span> 用于连接两个否定的概念，表示"也不"、"又不"，通常与neither搭配使用，或在否定句后继续否定。</p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">Neither...nor结构</div>
                    <div class="keyword text-lg mb-1">Neither he nor she came to the party.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈniːðər hi nɔːr ʃi keɪm tu ðə ˈpɑːrti/</div>
                    <div class="text-gray-700">他和她都没来参加聚会。</div>
                </div>
                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">递进否定</div>
                    <div class="keyword text-lg mb-1">I don't like coffee, nor do I like tea.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ doʊnt laɪk ˈkɔːfi nɔːr du aɪ laɪk tiː/</div>
                    <div class="text-gray-700">我不喜欢咖啡，也不喜欢茶。</div>
                </div>
                <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">能力否定</div>
                    <div class="keyword text-lg mb-1">He can't read, nor can he write.</div>
                    <div class="text-sm text-gray-600 mb-1">/hi kænt riːd nɔːr kæn hi raɪt/</div>
                    <div class="text-gray-700">他不能读，也不能写。</div>
                </div>
                <div class="card bg-lime-50 border border-lime-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">情况否定</div>
                    <div class="keyword text-lg mb-1">It's not hot, nor is it cold.</div>
                    <div class="text-sm text-gray-600 mb-1">/ɪts nɑːt hɑːt nɔːr ɪz ɪt koʊld/</div>
                    <div class="text-gray-700">天气不热，也不冷。</div>
                </div>
            </div>
        </section>

        <!-- 实用表达和习惯用法 -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">常用组合表达</h3>
            <p class="text-gray-700 mb-4 leading-7">并列连词在实际应用中经常与其他词汇组合，形成固定的表达方式。</p>
            
            <div class="mb-4">
                <h4 class="text-lg font-medium mb-3 text-gray-700">And的常用组合</h4>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="card bg-slate-50 border border-slate-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">so on and so forth</div>
                        <div class="keyword text-lg mb-1">Books, papers, and so on.</div>
                        <div class="text-sm text-gray-600 mb-1">/bʊks ˈpeɪpərz ænd soʊ ɑːn/</div>
                        <div class="text-gray-700">书籍、论文等等。</div>
                    </div>
                    <div class="card bg-neutral-50 border border-neutral-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">now and then</div>
                        <div class="keyword text-lg mb-1">I visit my parents now and then.</div>
                        <div class="text-sm text-gray-600 mb-1">/aɪ ˈvɪzɪt maɪ ˈperənts naʊ ænd ðen/</div>
                        <div class="text-gray-700">我时不时去看望父母。</div>
                    </div>
                    <div class="card bg-zinc-50 border border-zinc-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">more and more</div>
                        <div class="keyword text-lg mb-1">More and more people use smartphones.</div>
                        <div class="text-sm text-gray-600 mb-1">/mɔːr ænd mɔːr ˈpiːpəl juːz ˈsmɑːrtˌfoʊnz/</div>
                        <div class="text-gray-700">越来越多的人使用智能手机。</div>
                    </div>
                </div>
            </div>

            <div class="mb-4">
                <h4 class="text-lg font-medium mb-3 text-gray-700">Or的常用组合</h4>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">or so</div>
                        <div class="keyword text-lg mb-1">It takes an hour or so.</div>
                        <div class="text-sm text-gray-600 mb-1">/ɪt teɪks æn ˈaʊər ɔːr soʊ/</div>
                        <div class="text-gray-700">大约需要一个小时。</div>
                    </div>
                    <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">or else</div>
                        <div class="keyword text-lg mb-1">Study hard, or else you'll fail.</div>
                        <div class="text-sm text-gray-600 mb-1">/ˈstʌdi hɑːrd ɔːr els jul feɪl/</div>
                        <div class="text-gray-700">努力学习，否则你会失败。</div>
                    </div>
                    <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">either...or</div>
                        <div class="keyword text-lg mb-1">Either you or I will go.</div>
                        <div class="text-sm text-gray-600 mb-1">/ˈiːðər ju ɔːr aɪ wɪl goʊ/</div>
                        <div class="text-gray-700">要么你去，要么我去。</div>
                    </div>
                </div>
            </div>

            <div class="mb-4">
                <h4 class="text-lg font-medium mb-3 text-gray-700">But的常用组合</h4>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">but also</div>
                        <div class="keyword text-lg mb-1">Not only smart but also kind.</div>
                        <div class="text-sm text-gray-600 mb-1">/nɑːt ˈoʊnli smɑːrt bʌt ˈɔːlsoʊ kaɪnd/</div>
                        <div class="text-gray-700">不仅聪明而且善良。</div>
                    </div>
                    <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">but then</div>
                        <div class="keyword text-lg mb-1">I wanted to go, but then it rained.</div>
                        <div class="text-sm text-gray-600 mb-1">/aɪ ˈwɑːntɪd tu goʊ bʌt ðen ɪt reɪnd/</div>
                        <div class="text-gray-700">我想去的，但是下雨了。</div>
                    </div>
                    <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">anything but</div>
                        <div class="keyword text-lg mb-1">He is anything but lazy.</div>
                        <div class="text-sm text-gray-600 mb-1">/hi ɪz ˈeniθɪŋ bʌt ˈleɪzi/</div>
                        <div class="text-gray-700">他一点也不懒。</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 使用注意事项 -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">使用注意事项</h3>
            
            <div class="mb-4">
                <h4 class="text-lg font-medium mb-3 text-gray-700">标点符号规则</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">连接独立句子时加逗号</div>
                        <div class="keyword text-lg mb-1">I studied hard, and I passed the test.</div>
                        <div class="text-sm text-gray-600 mb-1">/aɪ ˈstʌdid hɑːrd ænd aɪ pæst ðə test/</div>
                        <div class="text-gray-700">我努力学习，并且通过了考试。</div>
                    </div>
                    <div class="card bg-sky-50 border border-sky-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">连接词组时不加逗号</div>
                        <div class="keyword text-lg mb-1">I like apples and oranges.</div>
                        <div class="text-sm text-gray-600 mb-1">/aɪ laɪk ˈæpəlz ænd ˈɔːrɪndʒɪz/</div>
                        <div class="text-gray-700">我喜欢苹果和橙子。</div>
                    </div>
                </div>
            </div>

            <div class="mb-4">
                <h4 class="text-lg font-medium mb-3 text-gray-700">语序和倒装</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="card bg-rose-50 border border-rose-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">Nor引起的倒装</div>
                        <div class="keyword text-lg mb-1">I don't smoke, nor do I drink.</div>
                        <div class="text-sm text-gray-600 mb-1">/aɪ doʊnt smoʊk nɔːr du aɪ drɪŋk/</div>
                        <div class="text-gray-700">我不抽烟，也不喝酒。</div>
                    </div>
                    <div class="card bg-violet-50 border border-violet-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">So表结果的倒装</div>
                        <div class="keyword text-lg mb-1">He likes music, so do I.</div>
                        <div class="text-sm text-gray-600 mb-1">/hi laɪks ˈmjuːzɪk soʊ du aɪ/</div>
                        <div class="text-gray-700">他喜欢音乐，我也喜欢。</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 高级用法和复合结构 -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">高级用法和复合结构</h3>
            <p class="text-gray-700 mb-4 leading-7">掌握并列连词的高级用法，能够使英语表达更加流畅自然，符合英语习惯。</p>
            
            <div class="mb-6">
                <h4 class="text-lg font-medium mb-3 text-gray-700">多重并列连词组合</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">三重并列</div>
                        <div class="keyword text-lg mb-1">I need to eat, sleep, and work.</div>
                        <div class="text-sm text-gray-600 mb-1">/aɪ niːd tu iːt sliːp ænd wɜːrk/</div>
                        <div class="text-gray-700">我需要吃饭、睡觉和工作。</div>
                    </div>
                    <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">复合转折</div>
                        <div class="keyword text-lg mb-1">She tried hard, but failed, yet never gave up.</div>
                        <div class="text-sm text-gray-600 mb-1">/ʃi traɪd hɑːrd bʌt feɪld jet ˈnevər geɪv ʌp/</div>
                        <div class="text-gray-700">她努力了，但失败了，却从未放弃。</div>
                    </div>
                    <div class="card bg-rose-50 border border-rose-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">因果链条</div>
                        <div class="keyword text-lg mb-1">It rained, so we stayed home, and watched movies.</div>
                        <div class="text-sm text-gray-600 mb-1">/ɪt reɪnd soʊ wi steɪd hoʊm ænd wɑːtʃt ˈmuːviz/</div>
                        <div class="text-gray-700">下雨了，所以我们待在家里看电影。</div>
                    </div>
                    <div class="card bg-amber-50 border border-amber-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">选择递进</div>
                        <div class="keyword text-lg mb-1">Call me, or text me, or just come over.</div>
                        <div class="text-sm text-gray-600 mb-1">/kɔːl mi ɔːr tekst mi ɔːr dʒʌst kʌm ˈoʊvər/</div>
                        <div class="text-gray-700">给我打电话，或发短信，或直接过来。</div>
                    </div>
                </div>
            </div>

            <div class="mb-6">
                <h4 class="text-lg font-medium mb-3 text-gray-700">并列连词的省略用法</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">省略重复词汇</div>
                        <div class="keyword text-lg mb-1">I can sing and (I can) dance.</div>
                        <div class="text-sm text-gray-600 mb-1">/aɪ kæn sɪŋ ænd dæns/</div>
                        <div class="text-gray-700">我会唱歌也会跳舞。</div>
                    </div>
                    <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">省略助动词</div>
                        <div class="keyword text-lg mb-1">She was tired but (she was) happy.</div>
                        <div class="text-sm text-gray-600 mb-1">/ʃi wʌz ˈtaɪərd bʌt ˈhæpi/</div>
                        <div class="text-gray-700">她累了但很开心。</div>
                    </div>
                    <div class="card bg-lime-50 border border-lime-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">省略介词短语</div>
                        <div class="keyword text-lg mb-1">He went to the store and (to the) bank.</div>
                        <div class="text-sm text-gray-600 mb-1">/hi went tu ðə stɔːr ænd bæŋk/</div>
                        <div class="text-gray-700">他去了商店和银行。</div>
                    </div>
                    <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">省略主语</div>
                        <div class="keyword text-lg mb-1">I read the book and (I) loved it.</div>
                        <div class="text-sm text-gray-600 mb-1">/aɪ riːd ðə bʊk ænd lʌvd ɪt/</div>
                        <div class="text-gray-700">我读了这本书，很喜欢。</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 不同语境中的应用 -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">不同语境中的应用</h3>
            
            <div class="mb-6">
                <h4 class="text-lg font-medium mb-3 text-gray-700">日常对话中的连词</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">购物场景</div>
                        <div class="keyword text-lg mb-1">I want the red one, but it's too expensive.</div>
                        <div class="text-sm text-gray-600 mb-1">/aɪ wɑːnt ðə red wʌn bʌt ɪts tuː ɪkˈspensɪv/</div>
                        <div class="text-gray-700">我想要红色的，但太贵了。</div>
                    </div>
                    <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">餐厅点餐</div>
                        <div class="keyword text-lg mb-1">I'll have the pasta and a salad.</div>
                        <div class="text-sm text-gray-600 mb-1">/aɪl hæv ðə ˈpɑːstə ænd ə ˈsæləd/</div>
                        <div class="text-gray-700">我要意大利面和沙拉。</div>
                    </div>
                    <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">工作讨论</div>
                        <div class="keyword text-lg mb-1">We can finish today, or work overtime tomorrow.</div>
                        <div class="text-sm text-gray-600 mb-1">/wi kæn ˈfɪnɪʃ təˈdeɪ ɔːr wɜːrk ˈoʊvərˌtaɪm təˈmɑːroʊ/</div>
                        <div class="text-gray-700">我们可以今天完成，或者明天加班。</div>
                    </div>
                    <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">旅行计划</div>
                        <div class="keyword text-lg mb-1">The weather looks bad, so let's stay indoors.</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə ˈweðər lʊks bæd soʊ lets steɪ ˈɪnˌdɔːrz/</div>
                        <div class="text-gray-700">天气看起来不好，所以我们待在室内吧。</div>
                    </div>
                </div>
            </div>

            <div class="mb-6">
                <h4 class="text-lg font-medium mb-3 text-gray-700">商务和正式场合</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="card bg-slate-50 border border-slate-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">会议发言</div>
                        <div class="keyword text-lg mb-1">The project is complex, yet achievable.</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə ˈprɑːdʒekt ɪz kəmˈpleks jet əˈtʃiːvəbəl/</div>
                        <div class="text-gray-700">这个项目很复杂，但是可以实现。</div>
                    </div>
                    <div class="card bg-stone-50 border border-stone-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">商务邮件</div>
                        <div class="keyword text-lg mb-1">We must act quickly, for time is limited.</div>
                        <div class="text-sm text-gray-600 mb-1">/wi mʌst ækt ˈkwɪkli fɔːr taɪm ɪz ˈlɪmɪtɪd/</div>
                        <div class="text-gray-700">我们必须快速行动，因为时间有限。</div>
                    </div>
                    <div class="card bg-neutral-50 border border-neutral-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">演讲表达</div>
                        <div class="keyword text-lg mb-1">We face challenges, but we also see opportunities.</div>
                        <div class="text-sm text-gray-600 mb-1">/wi feɪs ˈtʃælɪndʒɪz bʌt wi ˈɔːlsoʊ siː ˌɑːpərˈtuːnətiz/</div>
                        <div class="text-gray-700">我们面临挑战，但也看到了机遇。</div>
                    </div>
                    <div class="card bg-zinc-50 border border-zinc-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">报告总结</div>
                        <div class="keyword text-lg mb-1">Sales declined, so we need new strategies.</div>
                        <div class="text-sm text-gray-600 mb-1">/seɪlz dɪˈklaɪnd soʊ wi niːd nu ˈstrætədʒiz/</div>
                        <div class="text-gray-700">销售下降了，所以我们需要新策略。</div>
                    </div>
                </div>
            </div>

            <div class="mb-6">
                <h4 class="text-lg font-medium mb-3 text-gray-700">学术写作中的连词</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">研究发现</div>
                        <div class="keyword text-lg mb-1">The data shows improvement, yet further research is needed.</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə ˈdeɪtə ʃoʊz ɪmˈpruːvmənt jet ˈfɜːrðər rɪˈsɜːrtʃ ɪz ˈniːdɪd/</div>
                        <div class="text-gray-700">数据显示有改善，但还需要进一步研究。</div>
                    </div>
                    <div class="card bg-violet-50 border border-violet-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">论文论证</div>
                        <div class="keyword text-lg mb-1">The theory is sound, for it explains the phenomenon.</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə ˈθiːəri ɪz saʊnd fɔːr ɪt ɪkˈspleɪnz ðə fəˈnɑːmənən/</div>
                        <div class="text-gray-700">这个理论是可靠的，因为它解释了现象。</div>
                    </div>
                    <div class="card bg-fuchsia-50 border border-fuchsia-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">实验结果</div>
                        <div class="keyword text-lg mb-1">Method A failed, so we tried Method B.</div>
                        <div class="text-sm text-gray-600 mb-1">/ˈmeθəd eɪ feɪld soʊ wi traɪd ˈmeθəd biː/</div>
                        <div class="text-gray-700">方法A失败了，所以我们尝试了方法B。</div>
                    </div>
                    <div class="card bg-sky-50 border border-sky-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">结论总结</div>
                        <div class="keyword text-lg mb-1">Neither hypothesis was proven, nor was the original theory.</div>
                        <div class="text-sm text-gray-600 mb-1">/ˈniːðər haɪˈpɑːθəsɪs wʌz ˈpruːvən nɔːr wʌz ði əˈrɪdʒənəl ˈθiːəri/</div>
                        <div class="text-gray-700">假设都没有被证实，原始理论也没有。</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 语调和重音指导 -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">语调和重音指导</h3>
            <p class="text-gray-700 mb-4 leading-7">正确的语调和重音可以让你的英语听起来更地道，更符合英语的表达习惯。</p>
            
            <div class="mb-6">
                <h4 class="text-lg font-medium mb-3 text-gray-700">连词的重音规律</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">弱读连词</div>
                        <div class="keyword text-lg mb-1">Bread <span style="color: #999;">and</span> butter</div>
                        <div class="text-sm text-gray-600 mb-1">/bred ən ˈbʌtər/</div>
                        <div class="text-gray-700">面包和黄油（and弱读为/ən/）</div>
                    </div>
                    <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">强调对比时重读</div>
                        <div class="keyword text-lg mb-1">Not this <strong>BUT</strong> that</div>
                        <div class="text-sm text-gray-600 mb-1">/nɑːt ðɪs BʌT ðæt/</div>
                        <div class="text-gray-700">不是这个而是那个（but需要重读）</div>
                    </div>
                    <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">选择时重读or</div>
                        <div class="keyword text-lg mb-1">Coffee <strong>OR</strong> tea?</div>
                        <div class="text-sm text-gray-600 mb-1">/ˈkɔːfi ɔːr tiː/</div>
                        <div class="text-gray-700">咖啡还是茶？（疑问时or重读）</div>
                    </div>
                    <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">因果关系重读</div>
                        <div class="keyword text-lg mb-1">I'm tired, <strong>SO</strong> I'm leaving</div>
                        <div class="text-sm text-gray-600 mb-1">/aɪm ˈtaɪərd soʊ aɪm ˈliːvɪŋ/</div>
                        <div class="text-gray-700">我累了，所以要走了（so适度重读）</div>
                    </div>
                </div>
            </div>

            <div class="mb-6">
                <h4 class="text-lg font-medium mb-3 text-gray-700">语调模式</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">列举语调（升调）</div>
                        <div class="keyword text-lg mb-1">I need milk↗, eggs↗, and bread↘.</div>
                        <div class="text-sm text-gray-600 mb-1">/aɪ niːd mɪlk egz ænd bred/</div>
                        <div class="text-gray-700">我需要牛奶、鸡蛋和面包。</div>
                    </div>
                    <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">对比语调</div>
                        <div class="keyword text-lg mb-1">He's smart↗, but lazy↘.</div>
                        <div class="text-sm text-gray-600 mb-1">/hiz smɑːrt bʌt ˈleɪzi/</div>
                        <div class="text-gray-700">他聪明，但懒惰。</div>
                    </div>
                    <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">选择疑问语调</div>
                        <div class="keyword text-lg mb-1">Tea↗ or coffee↘?</div>
                        <div class="text-sm text-gray-600 mb-1">/tiː ɔːr ˈkɔːfi/</div>
                        <div class="text-gray-700">茶还是咖啡？</div>
                    </div>
                    <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">因果关系语调</div>
                        <div class="keyword text-lg mb-1">It rained↗, so we stayed home↘.</div>
                        <div class="text-sm text-gray-600 mb-1">/ɪt reɪnd soʊ wi steɪd hoʊm/</div>
                        <div class="text-gray-700">下雨了，所以我们待在家里。</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 更多习惯用语和固定搭配 -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">地道表达和习语</h3>

            <div class="mb-6">
                <h4 class="text-lg font-medium mb-3 text-gray-700">And的地道表达</h4>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">nice and + 形容词</div>
                        <div class="keyword text-lg mb-1">The weather is nice and warm.</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə ˈweðər ɪz naɪs ænd wɔːrm/</div>
                        <div class="text-gray-700">天气很暖和。</div>
                    </div>
                    <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">tried and true</div>
                        <div class="keyword text-lg mb-1">It's a tried and true method.</div>
                        <div class="text-sm text-gray-600 mb-1">/ɪts ə traɪd ænd truː ˈmeθəd/</div>
                        <div class="text-gray-700">这是经过验证的方法。</div>
                    </div>
                    <div class="card bg-rose-50 border border-rose-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">bits and pieces</div>
                        <div class="keyword text-lg mb-1">I collected bits and pieces of information.</div>
                        <div class="text-sm text-gray-600 mb-1">/aɪ kəˈlektɪd bɪts ænd ˈpiːsɪz əv ˌɪnfərˈmeɪʃən/</div>
                        <div class="text-gray-700">我收集了零碎的信息。</div>
                    </div>
                    <div class="card bg-amber-50 border border-amber-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">far and wide</div>
                        <div class="keyword text-lg mb-1">The news spread far and wide.</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə nuːz spred fɑːr ænd waɪd/</div>
                        <div class="text-gray-700">消息传得很远很广。</div>
                    </div>
                    <div class="card bg-lime-50 border border-lime-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">safe and sound</div>
                        <div class="keyword text-lg mb-1">They arrived safe and sound.</div>
                        <div class="text-sm text-gray-600 mb-1">/ðeɪ əˈraɪvd seɪf ænd saʊnd/</div>
                        <div class="text-gray-700">他们安全抵达了。</div>
                    </div>
                    <div class="card bg-sky-50 border border-sky-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">peace and quiet</div>
                        <div class="keyword text-lg mb-1">I need some peace and quiet.</div>
                        <div class="text-sm text-gray-600 mb-1">/aɪ niːd sʌm piːs ænd ˈkwaɪət/</div>
                        <div class="text-gray-700">我需要一些安静。</div>
                    </div>
                    <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">black and white</div>
                        <div class="keyword text-lg mb-1">The issue isn't black and white.</div>
                        <div class="text-sm text-gray-600 mb-1">/ði ˈɪʃuː ˈɪzənt blæk ænd waɪt/</div>
                        <div class="text-gray-700">这个问题不是非黑即白的。</div>
                    </div>
                    <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">ups and downs</div>
                        <div class="keyword text-lg mb-1">Life has its ups and downs.</div>
                        <div class="text-sm text-gray-600 mb-1">/laɪf hæz ɪts ʌps ænd daʊnz/</div>
                        <div class="text-gray-700">生活有起有落。</div>
                    </div>
                    <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">here and there</div>
                        <div class="keyword text-lg mb-1">I see mistakes here and there.</div>
                        <div class="text-sm text-gray-600 mb-1">/aɪ siː mɪˈsteɪks hɪr ænd ðer/</div>
                        <div class="text-gray-700">我到处都看到错误。</div>
                    </div>
                </div>
            </div>

            <div class="mb-6">
                <h4 class="text-lg font-medium mb-3 text-gray-700">But的地道表达</h4>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="card bg-violet-50 border border-violet-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">but seriously</div>
                        <div class="keyword text-lg mb-1">That was funny, but seriously, we need to focus.</div>
                        <div class="text-sm text-gray-600 mb-1">/ðæt wʌz ˈfʌni bʌt ˈsɪriəsli wi niːd tu ˈfoʊkəs/</div>
                        <div class="text-gray-700">那很好笑，但说真的，我们需要专注。</div>
                    </div>
                    <div class="card bg-fuchsia-50 border border-fuchsia-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">but then again</div>
                        <div class="keyword text-lg mb-1">It's expensive, but then again, it's high quality.</div>
                        <div class="text-sm text-gray-600 mb-1">/ɪts ɪkˈspensɪv bʌt ðen əˈgen ɪts haɪ ˈkwɑːləti/</div>
                        <div class="text-gray-700">很贵，但话说回来，质量很好。</div>
                    </div>
                    <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">all but</div>
                        <div class="keyword text-lg mb-1">The project is all but finished.</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə ˈprɑːdʒekt ɪz ɔːl bʌt ˈfɪnɪʃt/</div>
                        <div class="text-gray-700">项目基本完成了。</div>
                    </div>
                    <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">but for</div>
                        <div class="keyword text-lg mb-1">But for your help, I would have failed.</div>
                        <div class="text-sm text-gray-600 mb-1">/bʌt fɔːr jʊr help aɪ wʊd hæv feɪld/</div>
                        <div class="text-gray-700">要不是你的帮助，我就失败了。</div>
                    </div>
                    <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">can't help but</div>
                        <div class="keyword text-lg mb-1">I can't help but smile.</div>
                        <div class="text-sm text-gray-600 mb-1">/aɪ kænt help bʌt smaɪl/</div>
                        <div class="text-gray-700">我忍不住要笑。</div>
                    </div>
                    <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">but of course</div>
                        <div class="keyword text-lg mb-1">You can borrow my car, but of course!</div>
                        <div class="text-sm text-gray-600 mb-1">/ju kæn ˈbɑːroʊ maɪ kɑːr bʌt əv kɔːrs/</div>
                        <div class="text-gray-700">你可以借我的车，当然可以！</div>
                    </div>
                </div>
            </div>

            <div class="mb-6">
                <h4 class="text-lg font-medium mb-3 text-gray-700">So的地道表达</h4>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="card bg-slate-50 border border-slate-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">so far so good</div>
                        <div class="keyword text-lg mb-1">How's the project? So far so good.</div>
                        <div class="text-sm text-gray-600 mb-1">/haʊz ðə ˈprɑːdʒekt soʊ fɑːr soʊ gʊd/</div>
                        <div class="text-gray-700">项目怎么样？到目前为止还不错。</div>
                    </div>
                    <div class="card bg-stone-50 border border-stone-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">so what</div>
                        <div class="keyword text-lg mb-1">I'm late, so what?</div>
                        <div class="text-sm text-gray-600 mb-1">/aɪm leɪt soʊ wʌt/</div>
                        <div class="text-gray-700">我迟到了，那又怎样？</div>
                    </div>
                    <div class="card bg-neutral-50 border border-neutral-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">so to speak</div>
                        <div class="keyword text-lg mb-1">He's the boss, so to speak.</div>
                        <div class="text-sm text-gray-600 mb-1">/hiz ðə bɔːs soʊ tu spiːk/</div>
                        <div class="text-gray-700">可以说他是老板。</div>
                    </div>
                    <div class="card bg-zinc-50 border border-zinc-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">so much for</div>
                        <div class="keyword text-lg mb-1">So much for our vacation plans.</div>
                        <div class="text-sm text-gray-600 mb-1">/soʊ mʌtʃ fɔːr aʊər vəˈkeɪʃən plænz/</div>
                        <div class="text-gray-700">我们的度假计划泡汤了。</div>
                    </div>
                    <div class="card bg-gray-50 border border-gray-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">so long as</div>
                        <div class="keyword text-lg mb-1">You can stay, so long as you're quiet.</div>
                        <div class="text-sm text-gray-600 mb-1">/ju kæn steɪ soʊ lɔːŋ æz jʊr ˈkwaɪət/</div>
                        <div class="text-gray-700">只要你安静，你就可以留下。</div>
                    </div>
                    <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">and so on</div>
                        <div class="keyword text-lg mb-1">Apples, oranges, bananas, and so on.</div>
                        <div class="text-sm text-gray-600 mb-1">/ˈæpəlz ˈɔːrɪndʒɪz bəˈnænəz ænd soʊ ɑːn/</div>
                        <div class="text-gray-700">苹果、橙子、香蕉等等。</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 常见错误和避免方法 -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">常见错误和避免方法</h3>
            
            <div class="mb-6">
                <h4 class="text-lg font-medium mb-3 text-gray-700">标点符号错误</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="card bg-red-100 border border-red-300 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-red-600 mb-1">❌ 错误用法</div>
                        <div class="text-lg mb-1 text-red-700">I like pizza and, I like pasta.</div>
                        <div class="text-sm text-gray-600 mb-1">逗号位置错误</div>
                        <div class="text-gray-700">连接短语时不需要逗号</div>
                    </div>
                    <div class="card bg-green-100 border border-green-300 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-green-600 mb-1">✅ 正确用法</div>
                        <div class="keyword text-lg mb-1">I like pizza, and I like pasta.</div>
                        <div class="text-sm text-gray-600 mb-1">/aɪ laɪk ˈpiːtsə ænd aɪ laɪk ˈpɑːstə/</div>
                        <div class="text-gray-700">连接完整句子时需要逗号</div>
                    </div>
                </div>
            </div>

            <div class="mb-6">
                <h4 class="text-lg font-medium mb-3 text-gray-700">连词选择错误</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="card bg-red-100 border border-red-300 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-red-600 mb-1">❌ 逻辑错误</div>
                        <div class="text-lg mb-1 text-red-700">I was hungry, but I ate lunch.</div>
                        <div class="text-sm text-gray-600 mb-1">逻辑关系不当</div>
                        <div class="text-gray-700">饿了就吃饭是自然的，不是转折</div>
                    </div>
                    <div class="card bg-green-100 border border-green-300 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-green-600 mb-1">✅ 逻辑正确</div>
                        <div class="keyword text-lg mb-1">I was hungry, so I ate lunch.</div>
                        <div class="text-sm text-gray-600 mb-1">/aɪ wʌz ˈhʌŋgri soʊ aɪ eɪt lʌntʃ/</div>
                        <div class="text-gray-700">因果关系清晰</div>
                    </div>
                </div>
            </div>

            <div class="mb-6">
                <h4 class="text-lg font-medium mb-3 text-gray-700">重复连词错误</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="card bg-red-100 border border-red-300 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-red-600 mb-1">❌ 重复错误</div>
                        <div class="text-lg mb-1 text-red-700">Although it was raining, but we went out.</div>
                        <div class="text-sm text-gray-600 mb-1">although和but重复</div>
                        <div class="text-gray-700">不能同时使用两个转折连词</div>
                    </div>
                    <div class="card bg-green-100 border border-green-300 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-green-600 mb-1">✅ 单一连词</div>
                        <div class="keyword text-lg mb-1">Although it was raining, we went out.</div>
                        <div class="text-sm text-gray-600 mb-1">/ɔːlˈðoʊ ɪt wʌz ˈreɪnɪŋ wi went aʊt/</div>
                        <div class="text-gray-700">只使用一个转折连词</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 常见错误和纠正 -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">常见错误和纠正</h3>
            <p class="text-gray-700 mb-4 leading-7">了解并避免这些常见错误，能让你的英语表达更加准确和地道。</p>

            <div class="mb-6">
                <h4 class="text-lg font-medium mb-3 text-gray-700">标点符号错误</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-red-600 mb-1">❌ 错误</div>
                        <div class="text-lg mb-1">I like pizza and I like pasta.</div>
                        <div class="text-sm text-gray-600 mb-2">连接两个独立句子时缺少逗号</div>
                        <div class="text-sm text-green-600 mb-1">✅ 正确</div>
                        <div class="keyword text-lg">I like pizza, and I like pasta.</div>
                    </div>
                    <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-red-600 mb-1">❌ 错误</div>
                        <div class="text-lg mb-1">I bought apples, and oranges.</div>
                        <div class="text-sm text-gray-600 mb-2">连接简单词组时不需要逗号</div>
                        <div class="text-sm text-green-600 mb-1">✅ 正确</div>
                        <div class="keyword text-lg">I bought apples and oranges.</div>
                    </div>
                    <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-red-600 mb-1">❌ 错误</div>
                        <div class="text-lg mb-1">He is smart, but, he is lazy.</div>
                        <div class="text-sm text-gray-600 mb-2">连词前后都加逗号是错误的</div>
                        <div class="text-sm text-green-600 mb-1">✅ 正确</div>
                        <div class="keyword text-lg">He is smart, but he is lazy.</div>
                    </div>
                    <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-red-600 mb-1">❌ 错误</div>
                        <div class="text-lg mb-1">Do you want tea, or coffee?</div>
                        <div class="text-sm text-gray-600 mb-2">简单选择不需要逗号</div>
                        <div class="text-sm text-green-600 mb-1">✅ 正确</div>
                        <div class="keyword text-lg">Do you want tea or coffee?</div>
                    </div>
                </div>
            </div>

            <div class="mb-6">
                <h4 class="text-lg font-medium mb-3 text-gray-700">连词使用错误</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-red-600 mb-1">❌ 错误</div>
                        <div class="text-lg mb-1">I don't like coffee and tea.</div>
                        <div class="text-sm text-gray-600 mb-2">否定句中应该用or</div>
                        <div class="text-sm text-green-600 mb-1">✅ 正确</div>
                        <div class="keyword text-lg">I don't like coffee or tea.</div>
                    </div>
                    <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-red-600 mb-1">❌ 错误</div>
                        <div class="text-lg mb-1">Because it was raining, so I stayed home.</div>
                        <div class="text-sm text-gray-600 mb-2">不能同时使用because和so</div>
                        <div class="text-sm text-green-600 mb-1">✅ 正确</div>
                        <div class="keyword text-lg">It was raining, so I stayed home.</div>
                    </div>
                    <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-red-600 mb-1">❌ 错误</div>
                        <div class="text-lg mb-1">Although he is young, but he is wise.</div>
                        <div class="text-sm text-gray-600 mb-2">不能同时使用although和but</div>
                        <div class="text-sm text-green-600 mb-1">✅ 正确</div>
                        <div class="keyword text-lg">He is young, but he is wise.</div>
                    </div>
                    <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-red-600 mb-1">❌ 错误</div>
                        <div class="text-lg mb-1">I want to go, and but I'm busy.</div>
                        <div class="text-sm text-gray-600 mb-2">不能连续使用两个连词</div>
                        <div class="text-sm text-green-600 mb-1">✅ 正确</div>
                        <div class="keyword text-lg">I want to go, but I'm busy.</div>
                    </div>
                </div>
            </div>

            <div class="mb-6">
                <h4 class="text-lg font-medium mb-3 text-gray-700">语序和倒装错误</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-red-600 mb-1">❌ 错误</div>
                        <div class="text-lg mb-1">I don't smoke, nor I drink.</div>
                        <div class="text-sm text-gray-600 mb-2">nor后面需要倒装</div>
                        <div class="text-sm text-green-600 mb-1">✅ 正确</div>
                        <div class="keyword text-lg">I don't smoke, nor do I drink.</div>
                    </div>
                    <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-red-600 mb-1">❌ 错误</div>
                        <div class="text-lg mb-1">Neither he or she came.</div>
                        <div class="text-sm text-gray-600 mb-2">neither应该与nor搭配</div>
                        <div class="text-sm text-green-600 mb-1">✅ 正确</div>
                        <div class="keyword text-lg">Neither he nor she came.</div>
                    </div>
                    <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-red-600 mb-1">❌ 错误</div>
                        <div class="text-lg mb-1">Either you go or I go will be fine.</div>
                        <div class="text-sm text-gray-600 mb-2">either...or结构错误</div>
                        <div class="text-sm text-green-600 mb-1">✅ 正确</div>
                        <div class="keyword text-lg">Either you or I will go.</div>
                    </div>
                    <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-red-600 mb-1">❌ 错误</div>
                        <div class="text-lg mb-1">Not only he is smart but also kind.</div>
                        <div class="text-sm text-gray-600 mb-2">not only后需要倒装</div>
                        <div class="text-sm text-green-600 mb-1">✅ 正确</div>
                        <div class="keyword text-lg">Not only is he smart but also kind.</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 文化背景和使用习惯 -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">文化背景和使用习惯</h3>
            <p class="text-gray-700 mb-4 leading-7">了解英语中并列连词的文化背景和使用习惯，有助于更自然地运用这些连词。</p>

            <div class="mb-6">
                <h4 class="text-lg font-medium mb-3 text-gray-700">英语特色用法</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">Oxford Comma（牛津逗号）</div>
                        <div class="keyword text-lg mb-1">I bought apples, oranges, and bananas.</div>
                        <div class="text-sm text-gray-600 mb-1">/aɪ bɔːt ˈæpəlz ˈɔːrɪndʒɪz ænd bəˈnænəz/</div>
                        <div class="text-gray-700">英语常在最后一个and前加逗号</div>
                    </div>
                    <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">口语化的so</div>
                        <div class="keyword text-lg mb-1">So, what do you think?</div>
                        <div class="text-sm text-gray-600 mb-1">/soʊ wʌt du ju θɪŋk/</div>
                        <div class="text-gray-700">口语常用so开头引出话题</div>
                    </div>
                    <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">And的强调用法</div>
                        <div class="keyword text-lg mb-1">And another thing...</div>
                        <div class="text-sm text-gray-600 mb-1">/ænd əˈnʌðər θɪŋ/</div>
                        <div class="text-gray-700">用and开头表示补充重要信息</div>
                    </div>
                    <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">But的语气缓和</div>
                        <div class="keyword text-lg mb-1">I hear what you're saying, but...</div>
                        <div class="text-sm text-gray-600 mb-1">/aɪ hɪr wʌt jʊr ˈseɪɪŋ bʌt/</div>
                        <div class="text-gray-700">礼貌地表达不同意见</div>
                    </div>
                </div>
            </div>

            <div class="mb-6">
                <h4 class="text-lg font-medium mb-3 text-gray-700">地区差异和正式程度</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">正式场合 - For</div>
                        <div class="keyword text-lg mb-1">We must proceed carefully, for the stakes are high.</div>
                        <div class="text-sm text-gray-600 mb-1">/wi mʌst prəˈsiːd ˈkerfəli fɔːr ðə steɪks ɑːr haɪ/</div>
                        <div class="text-gray-700">正式文件和演讲中使用for表原因</div>
                    </div>
                    <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">非正式场合 - So</div>
                        <div class="keyword text-lg mb-1">It's risky, so let's be careful.</div>
                        <div class="text-sm text-gray-600 mb-1">/ɪts ˈrɪski soʊ lets bi ˈkerfəl/</div>
                        <div class="text-gray-700">日常对话中更常用so表结果</div>
                    </div>
                    <div class="card bg-rose-50 border border-rose-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">书面语 - Yet</div>
                        <div class="keyword text-lg mb-1">The challenge is great, yet not insurmountable.</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə ˈtʃælɪndʒ ɪz greɪt jet nɑːt ˌɪnsərˈmaʊntəbəl/</div>
                        <div class="text-gray-700">学术写作中yet比but更正式</div>
                    </div>
                    <div class="card bg-amber-50 border border-amber-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">口语 - But</div>
                        <div class="keyword text-lg mb-1">It's tough, but we can handle it.</div>
                        <div class="text-sm text-gray-600 mb-1">/ɪts tʌf bʌt wi kæn ˈhændəl ɪt/</div>
                        <div class="text-gray-700">日常对话中but更常用</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 高级语法结构 -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">高级语法结构</h3>
            <p class="text-gray-700 mb-4 leading-7">掌握这些高级结构，能让你的英语表达更加丰富和精确。</p>

            <div class="mb-6">
                <h4 class="text-lg font-medium mb-3 text-gray-700">并列连词的嵌套使用</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">复合并列</div>
                        <div class="keyword text-lg mb-1">I like coffee and tea, but not juice or soda.</div>
                        <div class="text-sm text-gray-600 mb-1">/aɪ laɪk ˈkɔːfi ænd tiː bʌt nɑːt dʒuːs ɔːr ˈsoʊdə/</div>
                        <div class="text-gray-700">在一个句子中使用多个连词</div>
                    </div>
                    <div class="card bg-lime-50 border border-lime-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">层次递进</div>
                        <div class="keyword text-lg mb-1">She studied hard, and she practiced daily, so she succeeded.</div>
                        <div class="text-sm text-gray-600 mb-1">/ʃi ˈstʌdid hɑːrd ænd ʃi ˈpræktɪst ˈdeɪli soʊ ʃi səkˈsiːdɪd/</div>
                        <div class="text-gray-700">表达因果链条关系</div>
                    </div>
                    <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">对比选择</div>
                        <div class="keyword text-lg mb-1">You can stay and help, or leave and miss out.</div>
                        <div class="text-sm text-gray-600 mb-1">/ju kæn steɪ ænd help ɔːr liːv ænd mɪs aʊt/</div>
                        <div class="text-gray-700">在选择中包含并列关系</div>
                    </div>
                    <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">转折递进</div>
                        <div class="keyword text-lg mb-1">He's young but talented, and he works hard, so he'll succeed.</div>
                        <div class="text-sm text-gray-600 mb-1">/hiz jʌŋ bʌt ˈtæləntɪd ænd hi wɜːrks hɑːrd soʊ hil səkˈsiːd/</div>
                        <div class="text-gray-700">复合逻辑关系表达</div>
                    </div>
                </div>
            </div>

            <div class="mb-6">
                <h4 class="text-lg font-medium mb-3 text-gray-700">并列连词与其他语法结构的结合</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="card bg-violet-50 border border-violet-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">与条件句结合</div>
                        <div class="keyword text-lg mb-1">If you study hard and practice regularly, you will improve.</div>
                        <div class="text-sm text-gray-600 mb-1">/ɪf ju ˈstʌdi hɑːrd ænd ˈpræktɪs ˈregjələrli ju wɪl ɪmˈpruːv/</div>
                        <div class="text-gray-700">在条件句中使用并列连词</div>
                    </div>
                    <div class="card bg-fuchsia-50 border border-fuchsia-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">与时间状语结合</div>
                        <div class="keyword text-lg mb-1">When I arrived, she was cooking and he was reading.</div>
                        <div class="text-sm text-gray-600 mb-1">/wen aɪ əˈraɪvd ʃi wʌz ˈkʊkɪŋ ænd hi wʌz ˈriːdɪŋ/</div>
                        <div class="text-gray-700">描述同时发生的动作</div>
                    </div>
                    <div class="card bg-sky-50 border border-sky-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">与比较结构结合</div>
                        <div class="keyword text-lg mb-1">She's not only smart but also more hardworking than others.</div>
                        <div class="text-sm text-gray-600 mb-1">/ʃiz nɑːt ˈoʊnli smɑːrt bʌt ˈɔːlsoʊ mɔːr ˈhɑːrdˌwɜːrkɪŋ ðæn ˈʌðərz/</div>
                        <div class="text-gray-700">结合比较级使用</div>
                    </div>
                    <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">与被动语态结合</div>
                        <div class="keyword text-lg mb-1">The report was written and reviewed, but not published.</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə rɪˈpɔːrt wʌz ˈrɪtən ænd rɪˈvjuːd bʌt nɑːt ˈpʌblɪʃt/</div>
                        <div class="text-gray-700">在被动语态中使用连词</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 实用技巧和记忆方法 -->
        <section class="">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">实用技巧和记忆方法</h3>

            <div class="mb-6">
                <h4 class="text-lg font-medium mb-3 text-gray-700">FANBOYS记忆法详解</h4>
                <div class="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-6 mb-4">
                    <div class="text-center mb-4">
                        <div class="text-3xl font-bold text-blue-600 mb-2">F-A-N-B-O-Y-S</div>
                        <div class="text-gray-700">七个并列连词的首字母缩写</div>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-7 gap-2">
                        <div class="text-center p-2 bg-white rounded border">
                            <div class="text-2xl font-bold text-red-500">F</div>
                            <div class="text-sm">For</div>
                            <div class="text-xs text-gray-500">原因</div>
                        </div>
                        <div class="text-center p-2 bg-white rounded border">
                            <div class="text-2xl font-bold text-orange-500">A</div>
                            <div class="text-sm">And</div>
                            <div class="text-xs text-gray-500">并列</div>
                        </div>
                        <div class="text-center p-2 bg-white rounded border">
                            <div class="text-2xl font-bold text-yellow-500">N</div>
                            <div class="text-sm">Nor</div>
                            <div class="text-xs text-gray-500">否定</div>
                        </div>
                        <div class="text-center p-2 bg-white rounded border">
                            <div class="text-2xl font-bold text-green-500">B</div>
                            <div class="text-sm">But</div>
                            <div class="text-xs text-gray-500">转折</div>
                        </div>
                        <div class="text-center p-2 bg-white rounded border">
                            <div class="text-2xl font-bold text-blue-500">O</div>
                            <div class="text-sm">Or</div>
                            <div class="text-xs text-gray-500">选择</div>
                        </div>
                        <div class="text-center p-2 bg-white rounded border">
                            <div class="text-2xl font-bold text-indigo-500">Y</div>
                            <div class="text-sm">Yet</div>
                            <div class="text-xs text-gray-500">转折</div>
                        </div>
                        <div class="text-center p-2 bg-white rounded border">
                            <div class="text-2xl font-bold text-purple-500">S</div>
                            <div class="text-sm">So</div>
                            <div class="text-xs text-gray-500">结果</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mb-6">
                <h4 class="text-lg font-medium mb-3 text-gray-700">功能分类记忆</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                        <div class="text-lg font-semibold text-green-700 mb-2">添加信息</div>
                        <div class="keyword text-lg mb-1">And</div>
                        <div class="text-sm text-gray-600 mb-2">用于添加相似或相关信息</div>
                        <div class="text-sm">I like reading <strong>and</strong> writing.</div>
                    </div>
                    <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                        <div class="text-lg font-semibold text-red-700 mb-2">对比转折</div>
                        <div class="keyword text-lg mb-1">But / Yet</div>
                        <div class="text-sm text-gray-600 mb-2">表示相反或意外的情况</div>
                        <div class="text-sm">It's expensive <strong>but</strong> worth it.</div>
                    </div>
                    <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                        <div class="text-lg font-semibold text-blue-700 mb-2">选择关系</div>
                        <div class="keyword text-lg mb-1">Or / Nor</div>
                        <div class="text-sm text-gray-600 mb-2">表示选择或否定选择</div>
                        <div class="text-sm">Tea <strong>or</strong> coffee?</div>
                    </div>
                    <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                        <div class="text-lg font-semibold text-purple-700 mb-2">因果关系</div>
                        <div class="keyword text-lg mb-1">So / For</div>
                        <div class="text-sm text-gray-600 mb-2">表示结果或原因</div>
                        <div class="text-sm">I'm tired, <strong>so</strong> I'll rest.</div>
                    </div>
                </div>
            </div>

            <div class="">
                <h4 class="text-lg font-medium mb-3 text-gray-700">语境联想记忆</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">购物场景</div>
                        <div class="keyword text-lg mb-1">I need milk, eggs, and bread, but the store is closed, so I'll go tomorrow.</div>
                        <div class="text-sm text-gray-600 mb-1">/aɪ niːd mɪlk egz ænd bred bʌt ðə stɔːr ɪz kloʊzd soʊ aɪl goʊ təˈmɑːroʊ/</div>
                        <div class="text-gray-700">一个场景中使用多个连词</div>
                    </div>
                    <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">工作场景</div>
                        <div class="keyword text-lg mb-1">The project is challenging yet rewarding, for it teaches us new skills.</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə ˈprɑːdʒekt ɪz ˈtʃælɪndʒɪŋ jet rɪˈwɔːrdɪŋ fɔːr ɪt ˈtiːtʃɪz ʌs nu skɪlz/</div>
                        <div class="text-gray-700">正式语境中的连词使用</div>
                    </div>
                </div>
            </div>
        </section>

    </div>
</body>
</html>