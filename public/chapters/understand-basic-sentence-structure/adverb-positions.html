<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>副词的位置和作用</title>
    <script src="/static/tailwindcss-3.4.17.js"></script>
    <style>
        .keyword {
            color: #3d74ed;
            font-weight: bold;
        }
        .card {
            transition: transform 0.1s ease;
        }
        .card:hover {
            transform: translateY(-1px);
        }
    </style>
</head>
<body class="bg-white">
<div class="p-6">

        <!-- 副词位置概述 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">副词在句子中的位置</h2>
            <p class="text-gray-700 mb-4">副词在英语句子中的位置非常灵活，不同的位置会产生不同的强调效果和语义。理解副词的位置规则是掌握英语语法的重要组成部分。副词的位置不仅影响句子的意思，还会改变语调和强调重点。</p>

            <!-- 基本位置介绍 -->
            <div class="bg-gray-50 border border-gray-200 rounded-lg p-6 mb-6">
                <h3 class="text-lg font-semibold mb-3 text-gray-800">副词的三个基本位置</h3>
                <p class="text-gray-700 mb-4">副词主要有三个基本位置：句首（Initial Position）、句中（Mid Position）和句末（End Position）。每个位置都有其特定的用法和语义效果。</p>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="bg-white border border-gray-300 rounded-lg p-4">
                        <h4 class="font-semibold text-gray-800 mb-2">句首位置 (Initial)</h4>
                        <p class="text-sm text-gray-600 mb-2">用于强调副词本身，常见于正式文体</p>
                        <div class="text-xs text-gray-500">特点：强调性强，语调突出</div>
                    </div>
                    <div class="bg-white border border-gray-300 rounded-lg p-4">
                        <h4 class="font-semibold text-gray-800 mb-2">句中位置 (Mid)</h4>
                        <p class="text-sm text-gray-600 mb-2">最常见的位置，语调自然</p>
                        <div class="text-xs text-gray-500">特点：平衡强调，符合英语习惯</div>
                    </div>
                    <div class="bg-white border border-gray-300 rounded-lg p-4">
                        <h4 class="font-semibold text-gray-800 mb-2">句末位置 (End)</h4>
                        <p class="text-sm text-gray-600 mb-2">强调动作的结果或方式</p>
                        <div class="text-xs text-gray-500">特点：重点在动作执行方式</div>
                    </div>
                </div>
            </div>

            <!-- 详细示例 -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">句首位置 - 强调副词</div>
                    <div class="keyword text-lg mb-1">Carefully, she opened the door.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈkerfəli, ʃi ˈoʊpənd ðə dɔr/</div>
                    <div class="text-gray-700 mb-2">小心地，她打开了门。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">语义重点：强调"小心"这个动作方式，暗示情况可能危险或需要特别注意</div>
                </div>

                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">句中位置 - 自然表达</div>
                    <div class="keyword text-lg mb-1">She carefully opened the door.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi ˈkerfəli ˈoʊpənd ðə dɔr/</div>
                    <div class="text-gray-700 mb-2">她小心地打开了门。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">语义重点：平衡描述主语和动作方式，最常用的表达方式</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">句末位置 - 强调方式</div>
                    <div class="keyword text-lg mb-1">She opened the door carefully.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi ˈoʊpənd ðə dɔr ˈkerfəli/</div>
                    <div class="text-gray-700 mb-2">她小心地打开了门。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">语义重点：强调开门的方式是"小心的"，重点在执行动作的具体方式</div>
                </div>
            </div>

            <!-- 位置选择的影响因素 -->
            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-6">
                <h3 class="text-lg font-semibold mb-3 text-gray-800">副词位置选择的影响因素</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <h4 class="font-semibold text-gray-800 mb-2">1. 语义强调</h4>
                        <ul class="text-sm text-gray-700 space-y-1">
                            <li>• 句首：强调副词本身的重要性</li>
                            <li>• 句中：平衡强调，符合自然语序</li>
                            <li>• 句末：强调动作的执行方式</li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="font-semibold text-gray-800 mb-2">2. 文体风格</h4>
                        <ul class="text-sm text-gray-700 space-y-1">
                            <li>• 正式文体：更多使用句首位置</li>
                            <li>• 日常对话：多用句中和句末位置</li>
                            <li>• 学术写作：注重逻辑连接副词的使用</li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- 副词类型与位置的关系 -->
            <div class="bg-indigo-50 border border-indigo-200 rounded-lg p-6">
                <h3 class="text-lg font-semibold mb-3 text-gray-800">不同类型副词的位置偏好</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="bg-white p-4 rounded-lg border">
                        <h4 class="font-semibold text-gray-800 mb-2">频率副词</h4>
                        <p class="text-sm text-gray-600 mb-2">always, usually, often, sometimes</p>
                        <div class="text-xs text-gray-500">偏好：句中位置（动词前/be动词后）</div>
                    </div>
                    <div class="bg-white p-4 rounded-lg border">
                        <h4 class="font-semibold text-gray-800 mb-2">方式副词</h4>
                        <p class="text-sm text-gray-600 mb-2">carefully, quickly, slowly</p>
                        <div class="text-xs text-gray-500">偏好：句末位置，也可句首强调</div>
                    </div>
                    <div class="bg-white p-4 rounded-lg border">
                        <h4 class="font-semibold text-gray-800 mb-2">时间副词</h4>
                        <p class="text-sm text-gray-600 mb-2">yesterday, tomorrow, now</p>
                        <div class="text-xs text-gray-500">偏好：句首或句末，很少句中</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 频率副词的位置 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">频率副词的位置规则</h2>
            <p class="text-gray-700 mb-4">频率副词（always, usually, often, sometimes, rarely, never等）在英语中有着严格的位置规则。它们通常位于主动词之前，助动词或be动词之后。掌握这些规则对于自然流畅的英语表达至关重要。</p>

            <!-- 频率副词分类 -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
                <h3 class="text-lg font-semibold mb-3 text-gray-800">频率副词的分类与频率程度</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-semibold text-gray-800 mb-3">按频率程度排序（从高到低）</h4>
                        <div class="space-y-2">
                            <div class="flex items-center justify-between bg-white p-2 rounded border">
                                <span class="keyword">always</span>
                                <span class="text-sm text-gray-600">100% - 总是</span>
                            </div>
                            <div class="flex items-center justify-between bg-white p-2 rounded border">
                                <span class="keyword">usually</span>
                                <span class="text-sm text-gray-600">90% - 通常</span>
                            </div>
                            <div class="flex items-center justify-between bg-white p-2 rounded border">
                                <span class="keyword">frequently</span>
                                <span class="text-sm text-gray-600">80% - 频繁地</span>
                            </div>
                            <div class="flex items-center justify-between bg-white p-2 rounded border">
                                <span class="keyword">often</span>
                                <span class="text-sm text-gray-600">70% - 经常</span>
                            </div>
                            <div class="flex items-center justify-between bg-white p-2 rounded border">
                                <span class="keyword">sometimes</span>
                                <span class="text-sm text-gray-600">50% - 有时</span>
                            </div>
                        </div>
                    </div>
                    <div>
                        <h4 class="font-semibold text-gray-800 mb-3">低频率副词</h4>
                        <div class="space-y-2">
                            <div class="flex items-center justify-between bg-white p-2 rounded border">
                                <span class="keyword">occasionally</span>
                                <span class="text-sm text-gray-600">30% - 偶尔</span>
                            </div>
                            <div class="flex items-center justify-between bg-white p-2 rounded border">
                                <span class="keyword">rarely</span>
                                <span class="text-sm text-gray-600">10% - 很少</span>
                            </div>
                            <div class="flex items-center justify-between bg-white p-2 rounded border">
                                <span class="keyword">seldom</span>
                                <span class="text-sm text-gray-600">5% - 很少</span>
                            </div>
                            <div class="flex items-center justify-between bg-white p-2 rounded border">
                                <span class="keyword">hardly ever</span>
                                <span class="text-sm text-gray-600">2% - 几乎不</span>
                            </div>
                            <div class="flex items-center justify-between bg-white p-2 rounded border">
                                <span class="keyword">never</span>
                                <span class="text-sm text-gray-600">0% - 从不</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <h3 class="text-lg font-semibold mb-3 text-gray-800">1. 在be动词之后</h3>
            <p class="text-gray-700 mb-4">当句子中有be动词（am, is, are, was, were）时，频率副词放在be动词之后。这是最基本也是最重要的规则之一。</p>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">always + be动词</div>
                    <div class="keyword text-lg mb-1">She is always happy.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi ɪz ˈɔlweɪz ˈhæpi/</div>
                    <div class="text-gray-700 mb-2">她总是很开心。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">结构：主语 + be动词 + 频率副词 + 表语</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">usually + be动词</div>
                    <div class="keyword text-lg mb-1">He is usually late.</div>
                    <div class="text-sm text-gray-600 mb-1">/hi ɪz ˈjuʒuəli leɪt/</div>
                    <div class="text-gray-700 mb-2">他通常迟到。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">注意：usually表示大约90%的频率</div>
                </div>

                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">never + be动词</div>
                    <div class="keyword text-lg mb-1">They are never bored.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðeɪ ɑr ˈnevər bɔrd/</div>
                    <div class="text-gray-700 mb-2">他们从不感到无聊。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">never是否定副词，本身就表示否定意义</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">sometimes + be动词</div>
                    <div class="keyword text-lg mb-1">I am sometimes confused.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ æm ˈsʌmtaɪmz kənˈfjuzd/</div>
                    <div class="text-gray-700 mb-2">我有时会感到困惑。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">sometimes也可以放在句首或句末</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">rarely + be动词</div>
                    <div class="keyword text-lg mb-1">She is rarely absent.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi ɪz ˈrerli ˈæbsənt/</div>
                    <div class="text-gray-700 mb-2">她很少缺席。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">rarely表示很低的频率，约10%</div>
                </div>

                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">frequently + be动词</div>
                    <div class="keyword text-lg mb-1">We are frequently busy.</div>
                    <div class="text-sm text-gray-600 mb-1">/wi ɑr ˈfrikwəntli ˈbɪzi/</div>
                    <div class="text-gray-700 mb-2">我们经常很忙。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">frequently比often的频率稍高</div>
                </div>
            </div>

            <!-- be动词的时态变化 -->
            <div class="bg-gray-50 border border-gray-200 rounded-lg p-6 mb-6">
                <h4 class="font-semibold text-gray-800 mb-3">be动词在不同时态中的频率副词位置</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <h5 class="font-medium text-gray-800 mb-2">现在时</h5>
                        <div class="space-y-2 text-sm">
                            <div class="bg-white p-2 rounded border">
                                <span class="keyword">I am always ready.</span>
                                <span class="text-gray-600 ml-2">我总是准备好的。</span>
                            </div>
                            <div class="bg-white p-2 rounded border">
                                <span class="keyword">You are often right.</span>
                                <span class="text-gray-600 ml-2">你经常是对的。</span>
                            </div>
                        </div>
                    </div>
                    <div>
                        <h5 class="font-medium text-gray-800 mb-2">过去时</h5>
                        <div class="space-y-2 text-sm">
                            <div class="bg-white p-2 rounded border">
                                <span class="keyword">He was always kind.</span>
                                <span class="text-gray-600 ml-2">他总是很善良。</span>
                            </div>
                            <div class="bg-white p-2 rounded border">
                                <span class="keyword">They were never late.</span>
                                <span class="text-gray-600 ml-2">他们从不迟到。</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <h3 class="text-lg font-semibold mb-3 text-gray-800">2. 在助动词之后，主动词之前</h3>
            <p class="text-gray-700 mb-4">当句子中有助动词（have, has, had, will, would等）或情态动词（can, could, should, must等）时，频率副词放在助动词之后，主动词之前。这个规则适用于各种复合时态。</p>

            <!-- 助动词类型分类 -->
            <div class="bg-green-50 border border-green-200 rounded-lg p-6 mb-6">
                <h4 class="font-semibold text-gray-800 mb-3">助动词的分类</h4>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="bg-white p-4 rounded border">
                        <h5 class="font-medium text-gray-800 mb-2">完成时助动词</h5>
                        <div class="text-sm text-gray-600 space-y-1">
                            <div>• have/has (现在完成时)</div>
                            <div>• had (过去完成时)</div>
                            <div>• will have (将来完成时)</div>
                        </div>
                    </div>
                    <div class="bg-white p-4 rounded border">
                        <h5 class="font-medium text-gray-800 mb-2">情态动词</h5>
                        <div class="text-sm text-gray-600 space-y-1">
                            <div>• can, could, may, might</div>
                            <div>• should, would, must</div>
                            <div>• ought to, used to</div>
                        </div>
                    </div>
                    <div class="bg-white p-4 rounded border">
                        <h5 class="font-medium text-gray-800 mb-2">其他助动词</h5>
                        <div class="text-sm text-gray-600 space-y-1">
                            <div>• will, would (将来时)</div>
                            <div>• do, does, did (强调/疑问)</div>
                            <div>• be (进行时/被动语态)</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">现在完成时</div>
                    <div class="keyword text-lg mb-1">I have always loved music.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ hæv ˈɔlweɪz lʌvd ˈmjuzɪk/</div>
                    <div class="text-gray-700 mb-2">我一直热爱音乐。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">结构：主语 + have/has + 频率副词 + 过去分词</div>
                </div>

                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">情态动词</div>
                    <div class="keyword text-lg mb-1">You should always be careful.</div>
                    <div class="text-sm text-gray-600 mb-1">/ju ʃʊd ˈɔlweɪz bi ˈkerfəl/</div>
                    <div class="text-gray-700 mb-2">你应该总是小心。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">结构：主语 + 情态动词 + 频率副词 + 动词原形</div>
                </div>

                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">过去完成时</div>
                    <div class="keyword text-lg mb-1">She had never seen snow.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi hæd ˈnevər sin snoʊ/</div>
                    <div class="text-gray-700 mb-2">她从未见过雪。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">结构：主语 + had + 频率副词 + 过去分词</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">将来时</div>
                    <div class="keyword text-lg mb-1">We will often visit you.</div>
                    <div class="text-sm text-gray-600 mb-1">/wi wɪl ˈɔfən ˈvɪzɪt ju/</div>
                    <div class="text-gray-700 mb-2">我们会经常拜访你。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">结构：主语 + will + 频率副词 + 动词原形</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">被动语态</div>
                    <div class="keyword text-lg mb-1">It is usually done quickly.</div>
                    <div class="text-sm text-gray-600 mb-1">/ɪt ɪz ˈjuʒuəli dʌn ˈkwɪkli/</div>
                    <div class="text-gray-700 mb-2">这通常很快就完成了。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">结构：主语 + be + 频率副词 + 过去分词</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">进行时</div>
                    <div class="keyword text-lg mb-1">He is always working late.</div>
                    <div class="text-sm text-gray-600 mb-1">/hi ɪz ˈɔlweɪz ˈwərkɪŋ leɪt/</div>
                    <div class="text-gray-700 mb-2">他总是工作到很晚。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">结构：主语 + be + 频率副词 + 现在分词</div>
                </div>
            </div>

            <!-- 复杂助动词结构 -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
                <h4 class="font-semibold text-gray-800 mb-3">复杂助动词结构中的频率副词位置</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <h5 class="font-medium text-gray-800 mb-2">多个助动词</h5>
                        <div class="space-y-2 text-sm">
                            <div class="bg-white p-3 rounded border">
                                <div class="keyword mb-1">She will have always been working.</div>
                                <div class="text-gray-600">她将一直在工作。</div>
                                <div class="text-xs text-gray-500 mt-1">频率副词放在第一个助动词之后</div>
                            </div>
                            <div class="bg-white p-3 rounded border">
                                <div class="keyword mb-1">They might have often visited.</div>
                                <div class="text-gray-600">他们可能经常拜访过。</div>
                                <div class="text-xs text-gray-500 mt-1">情态动词 + have + 频率副词 + 过去分词</div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <h5 class="font-medium text-gray-800 mb-2">否定句中的位置</h5>
                        <div class="space-y-2 text-sm">
                            <div class="bg-white p-3 rounded border">
                                <div class="keyword mb-1">I have never been there.</div>
                                <div class="text-gray-600">我从未去过那里。</div>
                                <div class="text-xs text-gray-500 mt-1">never本身就是否定，不需要not</div>
                            </div>
                            <div class="bg-white p-3 rounded border">
                                <div class="keyword mb-1">She doesn't usually complain.</div>
                                <div class="text-gray-600">她通常不抱怨。</div>
                                <div class="text-xs text-gray-500 mt-1">助动词否定 + 频率副词 + 主动词</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <h3 class="text-lg font-semibold mb-3 text-gray-800">3. 在实义动词之前</h3>
            <p class="text-gray-700 mb-4">当句子中只有实义动词（没有be动词或助动词）时，频率副词放在实义动词之前。这是最基本的句型结构，也是日常对话中最常见的用法。</p>

            <!-- 实义动词的定义和特点 -->
            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-6">
                <h4 class="font-semibold text-gray-800 mb-3">实义动词的特点</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <h5 class="font-medium text-gray-800 mb-2">什么是实义动词？</h5>
                        <ul class="text-sm text-gray-700 space-y-1">
                            <li>• 表示具体动作或状态的动词</li>
                            <li>• 如：go, come, eat, drink, work, study</li>
                            <li>• 区别于助动词和系动词</li>
                            <li>• 可以独立作谓语</li>
                        </ul>
                    </div>
                    <div>
                        <h5 class="font-medium text-gray-800 mb-2">频率副词的位置规则</h5>
                        <ul class="text-sm text-gray-700 space-y-1">
                            <li>• 肯定句：主语 + 频率副词 + 动词</li>
                            <li>• 否定句：主语 + don't/doesn't + 频率副词 + 动词</li>
                            <li>• 疑问句：Do/Does + 主语 + 频率副词 + 动词？</li>
                            <li>• 特殊情况：sometimes可以句首或句末</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">often + 动词</div>
                    <div class="keyword text-lg mb-1">We often go swimming.</div>
                    <div class="text-sm text-gray-600 mb-1">/wi ˈɔfən goʊ ˈswɪmɪŋ/</div>
                    <div class="text-gray-700 mb-2">我们经常去游泳。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">结构：主语 + 频率副词 + 实义动词 + 宾语</div>
                </div>

                <div class="card bg-gray-50 border border-gray-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">sometimes + 动词</div>
                    <div class="keyword text-lg mb-1">She sometimes works late.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi ˈsʌmtaɪmz wərks leɪt/</div>
                    <div class="text-gray-700 mb-2">她有时工作到很晚。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">sometimes位置灵活，也可句首或句末</div>
                </div>

                <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">rarely + 动词</div>
                    <div class="keyword text-lg mb-1">He rarely complains.</div>
                    <div class="text-sm text-gray-600 mb-1">/hi ˈrerli kəmˈpleɪnz/</div>
                    <div class="text-gray-700 mb-2">他很少抱怨。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">rarely是低频率副词，约10%的频率</div>
                </div>

                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">always + 动词</div>
                    <div class="keyword text-lg mb-1">They always arrive early.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðeɪ ˈɔlweɪz əˈraɪv ˈərli/</div>
                    <div class="text-gray-700 mb-2">他们总是早到。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">always表示100%的频率</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">usually + 动词</div>
                    <div class="keyword text-lg mb-1">I usually eat breakfast at 7.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ ˈjuʒuəli it ˈbrekfəst æt ˈsevən/</div>
                    <div class="text-gray-700 mb-2">我通常7点吃早餐。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">描述日常习惯的常用表达</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">never + 动词</div>
                    <div class="keyword text-lg mb-1">She never gives up.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi ˈnevər gɪvz ʌp/</div>
                    <div class="text-gray-700 mb-2">她从不放弃。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">never本身含否定意义，不需要not</div>
                </div>
            </div>

            <!-- 不同时态中的频率副词位置 -->
            <div class="bg-indigo-50 border border-indigo-200 rounded-lg p-6 mb-6">
                <h4 class="font-semibold text-gray-800 mb-3">不同时态中实义动词的频率副词位置</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h5 class="font-medium text-gray-800 mb-3">一般现在时</h5>
                        <div class="space-y-2 text-sm">
                            <div class="bg-white p-3 rounded border">
                                <div class="keyword mb-1">I often read books.</div>
                                <div class="text-gray-600 mb-1">我经常读书。</div>
                                <div class="text-xs text-gray-500">主语 + 频率副词 + 动词原形</div>
                            </div>
                            <div class="bg-white p-3 rounded border">
                                <div class="keyword mb-1">He usually plays tennis.</div>
                                <div class="text-gray-600 mb-1">他通常打网球。</div>
                                <div class="text-xs text-gray-500">第三人称单数动词加-s</div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <h5 class="font-medium text-gray-800 mb-3">一般过去时</h5>
                        <div class="space-y-2 text-sm">
                            <div class="bg-white p-3 rounded border">
                                <div class="keyword mb-1">We often visited museums.</div>
                                <div class="text-gray-600 mb-1">我们经常参观博物馆。</div>
                                <div class="text-xs text-gray-500">主语 + 频率副词 + 过去式动词</div>
                            </div>
                            <div class="bg-white p-3 rounded border">
                                <div class="keyword mb-1">She rarely came late.</div>
                                <div class="text-gray-600 mb-1">她很少迟到。</div>
                                <div class="text-xs text-gray-500">描述过去的习惯性动作</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 否定句和疑问句中的频率副词 -->
            <div class="bg-red-50 border border-red-200 rounded-lg p-6 mb-6">
                <h4 class="font-semibold text-gray-800 mb-3">否定句和疑问句中的频率副词位置</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h5 class="font-medium text-gray-800 mb-3">否定句</h5>
                        <div class="space-y-2 text-sm">
                            <div class="bg-white p-3 rounded border">
                                <div class="keyword mb-1">I don't usually work weekends.</div>
                                <div class="text-gray-600 mb-1">我通常不在周末工作。</div>
                                <div class="text-xs text-gray-500">don't/doesn't + 频率副词 + 动词原形</div>
                            </div>
                            <div class="bg-white p-3 rounded border">
                                <div class="keyword mb-1">She doesn't often complain.</div>
                                <div class="text-gray-600 mb-1">她不经常抱怨。</div>
                                <div class="text-xs text-gray-500">注意：never不能与not同用</div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <h5 class="font-medium text-gray-800 mb-3">疑问句</h5>
                        <div class="space-y-2 text-sm">
                            <div class="bg-white p-3 rounded border">
                                <div class="keyword mb-1">Do you often go there?</div>
                                <div class="text-gray-600 mb-1">你经常去那里吗？</div>
                                <div class="text-xs text-gray-500">Do/Does + 主语 + 频率副词 + 动词原形？</div>
                            </div>
                            <div class="bg-white p-3 rounded border">
                                <div class="keyword mb-1">Does he usually arrive early?</div>
                                <div class="text-gray-600 mb-1">他通常早到吗？</div>
                                <div class="text-xs text-gray-500">疑问句中频率副词位置不变</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 特殊频率副词的位置灵活性 -->
            <div class="bg-teal-50 border border-teal-200 rounded-lg p-6">
                <h4 class="font-semibold text-gray-800 mb-3">位置灵活的频率副词</h4>
                <p class="text-gray-700 mb-4">某些频率副词（如sometimes, occasionally, frequently）位置较为灵活，可以放在句首、句中或句末，但语义重点会有所不同。</p>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="bg-white p-4 rounded border">
                        <h5 class="font-medium text-gray-800 mb-2">句首位置</h5>
                        <div class="space-y-2 text-sm">
                            <div class="keyword">Sometimes I go jogging.</div>
                            <div class="text-gray-600">有时我去慢跑。</div>
                            <div class="text-xs text-gray-500 mt-1">强调"有时"这个频率</div>
                        </div>
                    </div>
                    <div class="bg-white p-4 rounded border">
                        <h5 class="font-medium text-gray-800 mb-2">句中位置</h5>
                        <div class="space-y-2 text-sm">
                            <div class="keyword">I sometimes go jogging.</div>
                            <div class="text-gray-600">我有时去慢跑。</div>
                            <div class="text-xs text-gray-500 mt-1">平衡强调，最自然</div>
                        </div>
                    </div>
                    <div class="bg-white p-4 rounded border">
                        <h5 class="font-medium text-gray-800 mb-2">句末位置</h5>
                        <div class="space-y-2 text-sm">
                            <div class="keyword">I go jogging sometimes.</div>
                            <div class="text-gray-600">我去慢跑，有时候。</div>
                            <div class="text-xs text-gray-500 mt-1">强调动作本身</div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 方式副词的位置 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">方式副词的位置</h2>
            <p class="text-gray-700 mb-4">方式副词（Adverbs of Manner）描述动作的执行方式、方法或状态，是英语中使用频率很高的副词类型。它们通常以-ly结尾，位置相对灵活，但有一定的规律可循。理解方式副词的位置规则对于提高英语表达的准确性和自然度非常重要。</p>

            <!-- 方式副词的特征和分类 -->
            <div class="bg-green-50 border border-green-200 rounded-lg p-6 mb-6">
                <h3 class="text-lg font-semibold mb-3 text-gray-800">方式副词的特征与分类</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-semibold text-gray-800 mb-3">形成方式</h4>
                        <div class="space-y-2 text-sm">
                            <div class="bg-white p-3 rounded border">
                                <div class="font-medium text-gray-800">形容词 + -ly</div>
                                <div class="text-gray-600">quick → quickly, careful → carefully</div>
                            </div>
                            <div class="bg-white p-3 rounded border">
                                <div class="font-medium text-gray-800">不规则形式</div>
                                <div class="text-gray-600">good → well, fast → fast, hard → hard</div>
                            </div>
                            <div class="bg-white p-3 rounded border">
                                <div class="font-medium text-gray-800">特殊变化</div>
                                <div class="text-gray-600">true → truly, whole → wholly</div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <h4 class="font-semibold text-gray-800 mb-3">语义分类</h4>
                        <div class="space-y-2 text-sm">
                            <div class="bg-white p-3 rounded border">
                                <div class="font-medium text-gray-800">速度方式</div>
                                <div class="text-gray-600">quickly, slowly, rapidly, gradually</div>
                            </div>
                            <div class="bg-white p-3 rounded border">
                                <div class="font-medium text-gray-800">态度方式</div>
                                <div class="text-gray-600">carefully, carelessly, politely, rudely</div>
                            </div>
                            <div class="bg-white p-3 rounded border">
                                <div class="font-medium text-gray-800">程度方式</div>
                                <div class="text-gray-600">completely, partially, thoroughly</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <h3 class="text-lg font-semibold mb-3 text-gray-800">1. 动词之后（最常见位置）</h3>
            <p class="text-gray-700 mb-4">方式副词最常见的位置是紧跟在动词之后，这是最自然、最符合英语习惯的表达方式。当动词没有宾语时，方式副词直接放在动词后面。</p>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">不及物动词 + 方式副词</div>
                    <div class="keyword text-lg mb-1">She speaks clearly.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi spiks ˈklɪrli/</div>
                    <div class="text-gray-700 mb-2">她说话很清楚。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">结构：主语 + 不及物动词 + 方式副词</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">不及物动词 + 方式副词</div>
                    <div class="keyword text-lg mb-1">He drives carefully.</div>
                    <div class="text-sm text-gray-600 mb-1">/hi draɪvz ˈkerfəli/</div>
                    <div class="text-gray-700 mb-2">他开车很小心。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">描述驾驶的方式和态度</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">不及物动词 + 方式副词</div>
                    <div class="keyword text-lg mb-1">They work hard.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðeɪ wərk hɑrd/</div>
                    <div class="text-gray-700 mb-2">他们工作很努力。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">注意：hard既是形容词也是副词</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">不及物动词 + 方式副词</div>
                    <div class="keyword text-lg mb-1">She smiled warmly.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi smaɪld ˈwɔrmli/</div>
                    <div class="text-gray-700 mb-2">她温暖地微笑。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">描述微笑的方式和感情色彩</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">不及物动词 + 方式副词</div>
                    <div class="keyword text-lg mb-1">The children played happily.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈtʃɪldrən pleɪd ˈhæpəli/</div>
                    <div class="text-gray-700 mb-2">孩子们快乐地玩耍。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">描述玩耍时的情绪状态</div>
                </div>

                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">不及物动词 + 方式副词</div>
                    <div class="keyword text-lg mb-1">He walked slowly.</div>
                    <div class="text-sm text-gray-600 mb-1">/hi wɔkt ˈsloʊli/</div>
                    <div class="text-gray-700 mb-2">他走得很慢。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">描述行走的速度</div>
                </div>
            </div>

            <!-- 及物动词的方式副词位置 -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
                <h4 class="font-semibold text-gray-800 mb-3">及物动词的方式副词位置</h4>
                <p class="text-gray-700 mb-4">当动词有宾语时，方式副词通常放在"动词+宾语"之后，即句末位置。这样可以避免副词与宾语之间的混淆。</p>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <h5 class="font-medium text-gray-800 mb-2">标准位置：动词+宾语+方式副词</h5>
                        <div class="space-y-2 text-sm">
                            <div class="bg-white p-3 rounded border">
                                <div class="keyword mb-1">She reads books carefully.</div>
                                <div class="text-gray-600 mb-1">她仔细地读书。</div>
                                <div class="text-xs text-gray-500">主语 + 动词 + 宾语 + 方式副词</div>
                            </div>
                            <div class="bg-white p-3 rounded border">
                                <div class="keyword mb-1">He explained the problem clearly.</div>
                                <div class="text-gray-600 mb-1">他清楚地解释了这个问题。</div>
                                <div class="text-xs text-gray-500">避免副词分割动词和宾语</div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <h5 class="font-medium text-gray-800 mb-2">特殊情况：短宾语</h5>
                        <div class="space-y-2 text-sm">
                            <div class="bg-white p-3 rounded border">
                                <div class="keyword mb-1">She carefully read it.</div>
                                <div class="text-gray-600 mb-1">她仔细地读了它。</div>
                                <div class="text-xs text-gray-500">代词宾语时，副词可前置</div>
                            </div>
                            <div class="bg-white p-3 rounded border">
                                <div class="keyword mb-1">He quickly finished lunch.</div>
                                <div class="text-gray-600 mb-1">他快速地吃完了午餐。</div>
                                <div class="text-xs text-gray-500">短宾语时，副词位置灵活</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <h3 class="text-lg font-semibold mb-3 text-gray-800">2. 句首强调位置</h3>
            <p class="text-gray-700 mb-4">方式副词放在句首时，通常用于强调动作的执行方式，创造戏剧性效果或引起读者注意。这种用法在文学作品、新闻报道和正式写作中较为常见。句首的方式副词后面通常需要用逗号分隔。</p>

            <!-- 句首方式副词的使用场合 -->
            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-6">
                <h4 class="font-semibold text-gray-800 mb-3">句首方式副词的使用场合</h4>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="bg-white p-4 rounded border">
                        <h5 class="font-medium text-gray-800 mb-2">文学描写</h5>
                        <p class="text-sm text-gray-600 mb-2">创造氛围，增强表现力</p>
                        <div class="text-xs text-gray-500">用于小说、诗歌等文学作品</div>
                    </div>
                    <div class="bg-white p-4 rounded border">
                        <h5 class="font-medium text-gray-800 mb-2">新闻报道</h5>
                        <p class="text-sm text-gray-600 mb-2">突出事件的发生方式</p>
                        <div class="text-xs text-gray-500">强调新闻事件的戏剧性</div>
                    </div>
                    <div class="bg-white p-4 rounded border">
                        <h5 class="font-medium text-gray-800 mb-2">正式演讲</h5>
                        <p class="text-sm text-gray-600 mb-2">增强语言的感染力</p>
                        <div class="text-xs text-gray-500">提高表达的说服力</div>
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">句首强调 - 神秘感</div>
                    <div class="keyword text-lg mb-1">Quietly, he entered the room.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈkwaɪətli, hi ˈentərd ðə rum/</div>
                    <div class="text-gray-700 mb-2">悄悄地，他进入了房间。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">强调动作的隐秘性，营造紧张氛围</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">句首强调 - 突然性</div>
                    <div class="keyword text-lg mb-1">Suddenly, it started raining.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈsʌdənli, ɪt ˈstɑrtəd ˈreɪnɪŋ/</div>
                    <div class="text-gray-700 mb-2">突然，开始下雨了。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">强调事件发生的突然性和意外性</div>
                </div>

                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">句首强调 - 小心谨慎</div>
                    <div class="keyword text-lg mb-1">Carefully, she opened the letter.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈkerfəli, ʃi ˈoʊpənd ðə ˈletər/</div>
                    <div class="text-gray-700 mb-2">小心地，她打开了信件。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">强调动作的谨慎程度，暗示内容重要</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">句首强调 - 优雅从容</div>
                    <div class="keyword text-lg mb-1">Gracefully, she danced across the stage.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈgreɪsfəli, ʃi dænst əˈkrɔs ðə steɪdʒ/</div>
                    <div class="text-gray-700 mb-2">优雅地，她在舞台上翩翩起舞。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">强调舞蹈动作的优美和艺术性</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">句首强调 - 迅速果断</div>
                    <div class="keyword text-lg mb-1">Quickly, they made their decision.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈkwɪkli, ðeɪ meɪd ðer dɪˈsɪʒən/</div>
                    <div class="text-gray-700 mb-2">迅速地，他们做出了决定。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">强调决策的速度和效率</div>
                </div>

                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">句首强调 - 意外惊喜</div>
                    <div class="keyword text-lg mb-1">Surprisingly, he agreed to help.</div>
                    <div class="text-sm text-gray-600 mb-1">/sərˈpraɪzɪŋli, hi əˈgrid tu help/</div>
                    <div class="text-gray-700 mb-2">令人惊讶地，他同意帮忙。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">强调结果的意外性和出人意料</div>
                </div>
            </div>

            <!-- 句首方式副词的语法规则 -->
            <div class="bg-indigo-50 border border-indigo-200 rounded-lg p-6 mb-6">
                <h4 class="font-semibold text-gray-800 mb-3">句首方式副词的语法规则</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h5 class="font-medium text-gray-800 mb-3">标点符号规则</h5>
                        <div class="space-y-2 text-sm">
                            <div class="bg-white p-3 rounded border">
                                <div class="font-medium text-gray-800">必须使用逗号</div>
                                <div class="text-gray-600">Slowly, he walked to the door.</div>
                                <div class="text-xs text-gray-500 mt-1">句首副词后必须加逗号</div>
                            </div>
                            <div class="bg-white p-3 rounded border">
                                <div class="font-medium text-gray-800">错误示例</div>
                                <div class="text-red-600">❌ Slowly he walked to the door.</div>
                                <div class="text-xs text-gray-500 mt-1">缺少逗号是常见错误</div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <h5 class="font-medium text-gray-800 mb-3">语调和重音</h5>
                        <div class="space-y-2 text-sm">
                            <div class="bg-white p-3 rounded border">
                                <div class="font-medium text-gray-800">重音位置</div>
                                <div class="text-gray-600">句首副词通常重读</div>
                                <div class="text-xs text-gray-500 mt-1">强调副词本身的重要性</div>
                            </div>
                            <div class="bg-white p-3 rounded border">
                                <div class="font-medium text-gray-800">语调变化</div>
                                <div class="text-gray-600">副词后有明显停顿</div>
                                <div class="text-xs text-gray-500 mt-1">逗号处需要语调停顿</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 句首方式副词的文体效果 -->
            <div class="bg-teal-50 border border-teal-200 rounded-lg p-6">
                <h4 class="font-semibold text-gray-800 mb-3">不同文体中的句首方式副词</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h5 class="font-medium text-gray-800 mb-3">文学作品中的应用</h5>
                        <div class="space-y-3 text-sm">
                            <div class="bg-white p-3 rounded border">
                                <div class="keyword mb-1">Silently, the snow began to fall.</div>
                                <div class="text-gray-600 mb-1">雪花静静地开始飘落。</div>
                                <div class="text-xs text-gray-500">营造宁静、诗意的氛围</div>
                            </div>
                            <div class="bg-white p-3 rounded border">
                                <div class="keyword mb-1">Desperately, she searched for the key.</div>
                                <div class="text-gray-600 mb-1">她拼命地寻找钥匙。</div>
                                <div class="text-xs text-gray-500">表现人物的紧急和焦虑</div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <h5 class="font-medium text-gray-800 mb-3">新闻报道中的应用</h5>
                        <div class="space-y-3 text-sm">
                            <div class="bg-white p-3 rounded border">
                                <div class="keyword mb-1">Dramatically, the situation changed.</div>
                                <div class="text-gray-600 mb-1">情况发生了戏剧性的变化。</div>
                                <div class="text-xs text-gray-500">强调变化的重大意义</div>
                            </div>
                            <div class="bg-white p-3 rounded border">
                                <div class="keyword mb-1">Unexpectedly, the CEO resigned.</div>
                                <div class="text-gray-600 mb-1">出人意料地，CEO辞职了。</div>
                                <div class="text-xs text-gray-500">突出新闻的意外性</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 时间副词的位置 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">时间副词的位置</h2>
            <p class="text-gray-700 mb-4">时间副词（Adverbs of Time）在英语中用来表示动作发生的时间、持续时间或频率。它们的位置相对灵活，可以位于句首、句中或句末，位置的选择取决于想要表达的强调程度、语境需要和语言习惯。掌握时间副词的位置规则对于准确表达时间概念至关重要。</p>

            <!-- 时间副词的分类 -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
                <h3 class="text-lg font-semibold mb-3 text-gray-800">时间副词的分类</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div class="bg-white p-4 rounded border">
                        <h4 class="font-semibold text-gray-800 mb-2">确定时间</h4>
                        <div class="text-sm text-gray-600 space-y-1">
                            <div>• today, tomorrow, yesterday</div>
                            <div>• now, then, soon</div>
                            <div>• recently, lately</div>
                            <div>• immediately, instantly</div>
                        </div>
                    </div>
                    <div class="bg-white p-4 rounded border">
                        <h4 class="font-semibold text-gray-800 mb-2">不确定时间</h4>
                        <div class="text-sm text-gray-600 space-y-1">
                            <div>• early, late</div>
                            <div>• before, after</div>
                            <div>• already, yet, still</div>
                            <div>• just, once, twice</div>
                        </div>
                    </div>
                    <div class="bg-white p-4 rounded border">
                        <h4 class="font-semibold text-gray-800 mb-2">持续时间</h4>
                        <div class="text-sm text-gray-600 space-y-1">
                            <div>• long, briefly</div>
                            <div>• temporarily, permanently</div>
                            <div>• forever, always</div>
                            <div>• since, until</div>
                        </div>
                    </div>
                    <div class="bg-white p-4 rounded border">
                        <h4 class="font-semibold text-gray-800 mb-2">时间关系</h4>
                        <div class="text-sm text-gray-600 space-y-1">
                            <div>• first, next, finally</div>
                            <div>• meanwhile, simultaneously</div>
                            <div>• afterwards, previously</div>
                            <div>• eventually, ultimately</div>
                        </div>
                    </div>
                </div>
            </div>

            <h3 class="text-lg font-semibold mb-3 text-gray-800">1. 句末位置（最常见且自然）</h3>
            <p class="text-gray-700 mb-4">时间副词最常见的位置是句末，这是最自然、最符合英语表达习惯的位置。句末的时间副词通常不需要逗号分隔，除非是很长的时间短语。这个位置适合大多数时间副词，特别是确定时间的副词。</p>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">确定时间 - 将来</div>
                    <div class="keyword text-lg mb-1">I will call you tomorrow.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ wɪl kɔl ju təˈmɑroʊ/</div>
                    <div class="text-gray-700 mb-2">我明天给你打电话。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">tomorrow是最常用的将来时间副词</div>
                </div>

                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">确定时间 - 过去</div>
                    <div class="keyword text-lg mb-1">She arrived yesterday.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi əˈraɪvd ˈjestərdeɪ/</div>
                    <div class="text-gray-700 mb-2">她昨天到达了。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">yesterday常与过去时态搭配使用</div>
                </div>

                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">频率时间</div>
                    <div class="keyword text-lg mb-1">We meet every week.</div>
                    <div class="text-sm text-gray-600 mb-1">/wi mit ˈevri wik/</div>
                    <div class="text-gray-700 mb-2">我们每周见面。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">表示重复发生的时间间隔</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">确定时间 - 现在</div>
                    <div class="keyword text-lg mb-1">He is working today.</div>
                    <div class="text-sm text-gray-600 mb-1">/hi ɪz ˈwərkɪŋ təˈdeɪ/</div>
                    <div class="text-gray-700 mb-2">他今天在工作。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">today可与各种时态搭配</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">近期时间</div>
                    <div class="keyword text-lg mb-1">I saw him recently.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ sɔ hɪm ˈrisəntli/</div>
                    <div class="text-gray-700 mb-2">我最近见过他。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">recently表示不久前的时间</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">即时时间</div>
                    <div class="keyword text-lg mb-1">Please come here now.</div>
                    <div class="text-sm text-gray-600 mb-1">/pliz kʌm hɪr naʊ/</div>
                    <div class="text-gray-700 mb-2">请现在过来。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">now表示此时此刻</div>
                </div>
            </div>

            <!-- 句末时间副词的语法特点 -->
            <div class="bg-gray-50 border border-gray-200 rounded-lg p-6 mb-6">
                <h4 class="font-semibold text-gray-800 mb-3">句末时间副词的语法特点</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h5 class="font-medium text-gray-800 mb-3">标点符号规则</h5>
                        <div class="space-y-2 text-sm">
                            <div class="bg-white p-3 rounded border">
                                <div class="font-medium text-gray-800">单个时间副词</div>
                                <div class="text-gray-600">She left early.</div>
                                <div class="text-xs text-gray-500 mt-1">不需要逗号分隔</div>
                            </div>
                            <div class="bg-white p-3 rounded border">
                                <div class="font-medium text-gray-800">时间短语</div>
                                <div class="text-gray-600">We'll meet next Monday morning.</div>
                                <div class="text-xs text-gray-500 mt-1">较长的时间短语也不需要逗号</div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <h5 class="font-medium text-gray-800 mb-3">语调特点</h5>
                        <div class="space-y-2 text-sm">
                            <div class="bg-white p-3 rounded border">
                                <div class="font-medium text-gray-800">自然语调</div>
                                <div class="text-gray-600">句末时间副词语调自然下降</div>
                                <div class="text-xs text-gray-500 mt-1">符合英语的自然语音节奏</div>
                            </div>
                            <div class="bg-white p-3 rounded border">
                                <div class="font-medium text-gray-800">重音位置</div>
                                <div class="text-gray-600">时间副词通常不重读</div>
                                <div class="text-xs text-gray-500 mt-1">除非需要特别强调时间</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 不同时态中的句末时间副词 -->
            <div class="bg-orange-50 border border-orange-200 rounded-lg p-6">
                <h4 class="font-semibold text-gray-800 mb-3">不同时态中的句末时间副词应用</h4>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <h5 class="font-medium text-gray-800 mb-2">过去时态</h5>
                        <div class="space-y-2 text-sm">
                            <div class="bg-white p-2 rounded border">
                                <div class="keyword">I finished work yesterday.</div>
                                <div class="text-gray-600 text-xs">我昨天完成了工作。</div>
                            </div>
                            <div class="bg-white p-2 rounded border">
                                <div class="keyword">She called me earlier.</div>
                                <div class="text-gray-600 text-xs">她早些时候给我打电话。</div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <h5 class="font-medium text-gray-800 mb-2">现在时态</h5>
                        <div class="space-y-2 text-sm">
                            <div class="bg-white p-2 rounded border">
                                <div class="keyword">He works late tonight.</div>
                                <div class="text-gray-600 text-xs">他今晚工作到很晚。</div>
                            </div>
                            <div class="bg-white p-2 rounded border">
                                <div class="keyword">We study together daily.</div>
                                <div class="text-gray-600 text-xs">我们每天一起学习。</div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <h5 class="font-medium text-gray-800 mb-2">将来时态</h5>
                        <div class="space-y-2 text-sm">
                            <div class="bg-white p-2 rounded border">
                                <div class="keyword">They will arrive soon.</div>
                                <div class="text-gray-600 text-xs">他们很快就会到达。</div>
                            </div>
                            <div class="bg-white p-2 rounded border">
                                <div class="keyword">I'll see you later.</div>
                                <div class="text-gray-600 text-xs">我稍后见你。</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <h3 class="text-lg font-semibold mb-3 text-gray-800">2. 句首强调位置</h3>
            <p class="text-gray-700 mb-4">时间副词放在句首时，通常用于强调时间的重要性，建立时间背景，或者连接前后文的时间关系。句首的时间副词后面需要用逗号分隔，这种用法在叙述、新闻报道和正式写作中很常见。</p>

            <!-- 句首时间副词的功能 -->
            <div class="bg-green-50 border border-green-200 rounded-lg p-6 mb-6">
                <h4 class="font-semibold text-gray-800 mb-3">句首时间副词的主要功能</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div class="bg-white p-4 rounded border">
                        <h5 class="font-medium text-gray-800 mb-2">建立时间背景</h5>
                        <p class="text-sm text-gray-600 mb-2">为整个句子或段落设定时间框架</p>
                        <div class="text-xs text-gray-500">常用于故事开头或场景转换</div>
                    </div>
                    <div class="bg-white p-4 rounded border">
                        <h5 class="font-medium text-gray-800 mb-2">强调时间重要性</h5>
                        <p class="text-sm text-gray-600 mb-2">突出时间因素的关键作用</p>
                        <div class="text-xs text-gray-500">用于重要事件的时间标记</div>
                    </div>
                    <div class="bg-white p-4 rounded border">
                        <h5 class="font-medium text-gray-800 mb-2">连接时间关系</h5>
                        <p class="text-sm text-gray-600 mb-2">建立事件之间的时间逻辑</p>
                        <div class="text-xs text-gray-500">用于时间顺序的叙述</div>
                    </div>
                    <div class="bg-white p-4 rounded border">
                        <h5 class="font-medium text-gray-800 mb-2">创造戏剧效果</h5>
                        <p class="text-sm text-gray-600 mb-2">增强表达的感染力和吸引力</p>
                        <div class="text-xs text-gray-500">常用于文学和新闻写作</div>
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">过去时间强调</div>
                    <div class="keyword text-lg mb-1">Yesterday, I met an old friend.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈjestərdeɪ, aɪ met ən oʊld frend/</div>
                    <div class="text-gray-700 mb-2">昨天，我遇到了一个老朋友。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">强调遇见朋友这件事发生的具体时间</div>
                </div>

                <div class="card bg-gray-50 border border-gray-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">将来时间强调</div>
                    <div class="keyword text-lg mb-1">Next week, we will start the project.</div>
                    <div class="text-sm text-gray-600 mb-1">/nekst wik, wi wɪl stɑrt ðə ˈprɑdʒekt/</div>
                    <div class="text-gray-700 mb-2">下周，我们将开始这个项目。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">强调项目开始的重要时间节点</div>
                </div>

                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">时间顺序连接</div>
                    <div class="keyword text-lg mb-1">First, we need to plan carefully.</div>
                    <div class="text-sm text-gray-600 mb-1">/fərst, wi nid tu plæn ˈkerfəli/</div>
                    <div class="text-gray-700 mb-2">首先，我们需要仔细计划。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">建立步骤的时间逻辑关系</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">即时时间强调</div>
                    <div class="keyword text-lg mb-1">Now, let's begin the meeting.</div>
                    <div class="text-sm text-gray-600 mb-1">/naʊ, lets bɪˈgɪn ðə ˈmitɪŋ/</div>
                    <div class="text-gray-700 mb-2">现在，让我们开始会议。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">强调行动的即时性和紧迫性</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">最终时间</div>
                    <div class="keyword text-lg mb-1">Finally, we reached our destination.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈfaɪnəli, wi ritʃt aʊr ˌdestəˈneɪʃən/</div>
                    <div class="text-gray-700 mb-2">最终，我们到达了目的地。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">表示经过努力后的最终结果</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">近期时间</div>
                    <div class="keyword text-lg mb-1">Recently, I've been thinking about this.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈrisəntli, aɪv bin ˈθɪŋkɪŋ əˈbaʊt ðɪs/</div>
                    <div class="text-gray-700 mb-2">最近，我一直在思考这个问题。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">引入近期的思考或变化</div>
                </div>
            </div>

            <!-- 句首时间副词的语法规则 -->
            <div class="bg-indigo-50 border border-indigo-200 rounded-lg p-6 mb-6">
                <h4 class="font-semibold text-gray-800 mb-3">句首时间副词的语法规则</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h5 class="font-medium text-gray-800 mb-3">标点符号要求</h5>
                        <div class="space-y-2 text-sm">
                            <div class="bg-white p-3 rounded border">
                                <div class="font-medium text-gray-800">✅ 正确用法</div>
                                <div class="text-gray-600">Tomorrow, I will call you.</div>
                                <div class="text-xs text-gray-500 mt-1">句首时间副词后必须加逗号</div>
                            </div>
                            <div class="bg-white p-3 rounded border">
                                <div class="font-medium text-red-600">❌ 错误用法</div>
                                <div class="text-red-600">Tomorrow I will call you.</div>
                                <div class="text-xs text-gray-500 mt-1">缺少逗号是常见错误</div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <h5 class="font-medium text-gray-800 mb-3">语调和重音</h5>
                        <div class="space-y-2 text-sm">
                            <div class="bg-white p-3 rounded border">
                                <div class="font-medium text-gray-800">重音模式</div>
                                <div class="text-gray-600">句首时间副词通常重读</div>
                                <div class="text-xs text-gray-500 mt-1">强调时间的重要性</div>
                            </div>
                            <div class="bg-white p-3 rounded border">
                                <div class="font-medium text-gray-800">语调变化</div>
                                <div class="text-gray-600">逗号处有明显停顿</div>
                                <div class="text-xs text-gray-500 mt-1">为主句内容做铺垫</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 不同文体中的句首时间副词 -->
            <div class="bg-teal-50 border border-teal-200 rounded-lg p-6 mb-6">
                <h4 class="font-semibold text-gray-800 mb-3">不同文体中的句首时间副词应用</h4>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <h5 class="font-medium text-gray-800 mb-3">叙述文体</h5>
                        <div class="space-y-2 text-sm">
                            <div class="bg-white p-3 rounded border">
                                <div class="keyword mb-1">Once upon a time, there lived a king.</div>
                                <div class="text-gray-600 mb-1">从前，有一个国王。</div>
                                <div class="text-xs text-gray-500">经典的故事开头</div>
                            </div>
                            <div class="bg-white p-3 rounded border">
                                <div class="keyword mb-1">Later that day, something unexpected happened.</div>
                                <div class="text-gray-600 mb-1">那天晚些时候，发生了意想不到的事。</div>
                                <div class="text-xs text-gray-500">推进故事情节</div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <h5 class="font-medium text-gray-800 mb-3">新闻报道</h5>
                        <div class="space-y-2 text-sm">
                            <div class="bg-white p-3 rounded border">
                                <div class="keyword mb-1">Yesterday, the president announced new policies.</div>
                                <div class="text-gray-600 mb-1">昨天，总统宣布了新政策。</div>
                                <div class="text-xs text-gray-500">突出新闻的时效性</div>
                            </div>
                            <div class="bg-white p-3 rounded border">
                                <div class="keyword mb-1">Earlier today, markets showed volatility.</div>
                                <div class="text-gray-600 mb-1">今天早些时候，市场出现波动。</div>
                                <div class="text-xs text-gray-500">强调事件的即时性</div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <h5 class="font-medium text-gray-800 mb-3">学术写作</h5>
                        <div class="space-y-2 text-sm">
                            <div class="bg-white p-3 rounded border">
                                <div class="keyword mb-1">Previously, researchers had focused on...</div>
                                <div class="text-gray-600 mb-1">以前，研究者专注于...</div>
                                <div class="text-xs text-gray-500">建立研究背景</div>
                            </div>
                            <div class="bg-white p-3 rounded border">
                                <div class="keyword mb-1">Currently, there is growing interest in...</div>
                                <div class="text-gray-600 mb-1">目前，人们对...越来越感兴趣。</div>
                                <div class="text-xs text-gray-500">描述当前研究趋势</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 句首时间副词的连接功能 -->
            <div class="bg-orange-50 border border-orange-200 rounded-lg p-6">
                <h4 class="font-semibold text-gray-800 mb-3">句首时间副词的连接功能</h4>
                <p class="text-gray-700 mb-4">句首时间副词不仅表示时间，还具有重要的连接功能，帮助建立句子间和段落间的逻辑关系。</p>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h5 class="font-medium text-gray-800 mb-3">时间顺序连接</h5>
                        <div class="space-y-2 text-sm">
                            <div class="bg-white p-3 rounded border">
                                <div class="keyword mb-1">First, we analyzed the data.</div>
                                <div class="text-gray-600">首先，我们分析了数据。</div>
                            </div>
                            <div class="bg-white p-3 rounded border">
                                <div class="keyword mb-1">Then, we drew conclusions.</div>
                                <div class="text-gray-600">然后，我们得出了结论。</div>
                            </div>
                            <div class="bg-white p-3 rounded border">
                                <div class="keyword mb-1">Finally, we presented our findings.</div>
                                <div class="text-gray-600">最后，我们展示了发现。</div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <h5 class="font-medium text-gray-800 mb-3">对比时间关系</h5>
                        <div class="space-y-2 text-sm">
                            <div class="bg-white p-3 rounded border">
                                <div class="keyword mb-1">Previously, the situation was different.</div>
                                <div class="text-gray-600">以前，情况是不同的。</div>
                            </div>
                            <div class="bg-white p-3 rounded border">
                                <div class="keyword mb-1">Now, everything has changed.</div>
                                <div class="text-gray-600">现在，一切都改变了。</div>
                            </div>
                            <div class="bg-white p-3 rounded border">
                                <div class="keyword mb-1">In the future, we expect improvements.</div>
                                <div class="text-gray-600">将来，我们期待改善。</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 句中位置的时间副词 -->
            <h3 class="text-lg font-semibold mb-3 text-gray-800">3. 句中位置（特殊情况）</h3>
            <p class="text-gray-700 mb-4">某些时间副词可以放在句中位置，特别是那些表示完成状态或持续状态的副词，如already, yet, still, just等。这些副词的位置规则与频率副词相似。</p>

            <div class="bg-red-50 border border-red-200 rounded-lg p-6">
                <h4 class="font-semibold text-gray-800 mb-3">句中时间副词的位置规则</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h5 class="font-medium text-gray-800 mb-3">完成时态中的位置</h5>
                        <div class="space-y-2 text-sm">
                            <div class="bg-white p-3 rounded border">
                                <div class="keyword mb-1">I have already finished.</div>
                                <div class="text-gray-600 mb-1">我已经完成了。</div>
                                <div class="text-xs text-gray-500">already在助动词后，主动词前</div>
                            </div>
                            <div class="bg-white p-3 rounded border">
                                <div class="keyword mb-1">She has just arrived.</div>
                                <div class="text-gray-600 mb-1">她刚刚到达。</div>
                                <div class="text-xs text-gray-500">just表示刚刚完成的动作</div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <h5 class="font-medium text-gray-800 mb-3">be动词后的位置</h5>
                        <div class="space-y-2 text-sm">
                            <div class="bg-white p-3 rounded border">
                                <div class="keyword mb-1">He is still working.</div>
                                <div class="text-gray-600 mb-1">他仍在工作。</div>
                                <div class="text-xs text-gray-500">still表示动作的持续</div>
                            </div>
                            <div class="bg-white p-3 rounded border">
                                <div class="keyword mb-1">They are already here.</div>
                                <div class="text-gray-600 mb-1">他们已经在这里了。</div>
                                <div class="text-xs text-gray-500">already在be动词后</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 多个副词的排列顺序 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">多个副词的排列顺序</h2>
            <p class="text-gray-700 mb-4">当句子中出现多个副词时，它们的排列顺序遵循特定的规律。正确的副词顺序不仅使句子听起来自然，还能准确传达说话者的意图。英语中副词的基本顺序是：方式副词 → 地点副词 → 时间副词，但这个规则有许多细节和例外情况需要掌握。</p>

            <!-- 基本排列顺序规则 -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
                <h3 class="text-lg font-semibold mb-3 text-gray-800">基本排列顺序：方式 → 地点 → 时间</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div class="bg-white p-4 rounded border text-center">
                        <div class="text-2xl mb-2">1️⃣</div>
                        <h4 class="font-semibold text-gray-800 mb-2">方式副词</h4>
                        <p class="text-sm text-gray-600 mb-2">How? 怎样？</p>
                        <div class="text-xs text-gray-500">carefully, quickly, well</div>
                    </div>
                    <div class="bg-white p-4 rounded border text-center">
                        <div class="text-2xl mb-2">2️⃣</div>
                        <h4 class="font-semibold text-gray-800 mb-2">地点副词</h4>
                        <p class="text-sm text-gray-600 mb-2">Where? 哪里？</p>
                        <div class="text-xs text-gray-500">here, there, at home</div>
                    </div>
                    <div class="bg-white p-4 rounded border text-center">
                        <div class="text-2xl mb-2">3️⃣</div>
                        <h4 class="font-semibold text-gray-800 mb-2">时间副词</h4>
                        <p class="text-sm text-gray-600 mb-2">When? 什么时候？</p>
                        <div class="text-xs text-gray-500">yesterday, now, tomorrow</div>
                    </div>
                </div>
                <div class="bg-yellow-100 border border-yellow-300 rounded-lg p-4">
                    <h4 class="font-semibold text-gray-800 mb-2">记忆口诀：MaPTi</h4>
                    <p class="text-sm text-gray-700"><strong>Ma</strong>nner (方式) → <strong>P</strong>lace (地点) → <strong>Ti</strong>me (时间)</p>
                </div>
            </div>

            <!-- 标准顺序示例 -->
            <h3 class="text-lg font-semibold mb-3 text-gray-800">1. 标准顺序：方式 + 地点 + 时间</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">完整顺序示例</div>
                    <div class="keyword text-lg mb-1">She sang beautifully at the concert last night.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi sæŋ ˈbjutəfəli æt ðə ˈkɑnsərt læst naɪt/</div>
                    <div class="text-gray-700 mb-2">她昨晚在音乐会上唱得很美。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">
                        <span class="text-blue-600">beautifully</span> (方式) +
                        <span class="text-green-600">at the concert</span> (地点) +
                        <span class="text-red-600">last night</span> (时间)
                    </div>
                </div>

                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">完整顺序示例</div>
                    <div class="keyword text-lg mb-1">He worked hard in the office yesterday.</div>
                    <div class="text-sm text-gray-600 mb-1">/hi wərkt hɑrd ɪn ði ˈɔfɪs ˈjestərdeɪ/</div>
                    <div class="text-gray-700 mb-2">他昨天在办公室努力工作。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">
                        <span class="text-blue-600">hard</span> (方式) +
                        <span class="text-green-600">in the office</span> (地点) +
                        <span class="text-red-600">yesterday</span> (时间)
                    </div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">完整顺序示例</div>
                    <div class="keyword text-lg mb-1">They played happily in the park this morning.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðeɪ pleɪd ˈhæpəli ɪn ðə pɑrk ðɪs ˈmɔrnɪŋ/</div>
                    <div class="text-gray-700 mb-2">他们今天早上在公园里快乐地玩耍。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">
                        <span class="text-blue-600">happily</span> (方式) +
                        <span class="text-green-600">in the park</span> (地点) +
                        <span class="text-red-600">this morning</span> (时间)
                    </div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">完整顺序示例</div>
                    <div class="keyword text-lg mb-1">She danced gracefully on stage tonight.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi dænst ˈgreɪsfəli ɑn steɪdʒ təˈnaɪt/</div>
                    <div class="text-gray-700 mb-2">她今晚在舞台上优雅地跳舞。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">
                        <span class="text-blue-600">gracefully</span> (方式) +
                        <span class="text-green-600">on stage</span> (地点) +
                        <span class="text-red-600">tonight</span> (时间)
                    </div>
                </div>
            </div>

            <!-- 两个副词的组合 -->
            <h3 class="text-lg font-semibold mb-3 text-gray-800">2. 两个副词的组合</h3>
            <p class="text-gray-700 mb-4">当句子中只有两个副词时，仍然遵循基本的顺序规则。最常见的组合是方式+时间、地点+时间，以及方式+地点。</p>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div>
                    <h4 class="font-semibold text-gray-800 mb-3 text-center">方式 + 时间</h4>
                    <div class="space-y-3">
                        <div class="card bg-red-50 border border-red-200 rounded-lg p-3 shadow-sm">
                            <div class="keyword text-sm mb-1">She speaks clearly today.</div>
                            <div class="text-xs text-gray-600 mb-1">她今天说话很清楚。</div>
                            <div class="text-xs text-gray-500">方式副词 + 时间副词</div>
                        </div>
                        <div class="card bg-red-50 border border-red-200 rounded-lg p-3 shadow-sm">
                            <div class="keyword text-sm mb-1">He ran quickly yesterday.</div>
                            <div class="text-xs text-gray-600 mb-1">他昨天跑得很快。</div>
                            <div class="text-xs text-gray-500">方式副词 + 时间副词</div>
                        </div>
                    </div>
                </div>
                <div>
                    <h4 class="font-semibold text-gray-800 mb-3 text-center">地点 + 时间</h4>
                    <div class="space-y-3">
                        <div class="card bg-green-50 border border-green-200 rounded-lg p-3 shadow-sm">
                            <div class="keyword text-sm mb-1">I'll meet you there tomorrow.</div>
                            <div class="text-xs text-gray-600 mb-1">我明天在那里见你。</div>
                            <div class="text-xs text-gray-500">地点副词 + 时间副词</div>
                        </div>
                        <div class="card bg-green-50 border border-green-200 rounded-lg p-3 shadow-sm">
                            <div class="keyword text-sm mb-1">She worked at home yesterday.</div>
                            <div class="text-xs text-gray-600 mb-1">她昨天在家工作。</div>
                            <div class="text-xs text-gray-500">地点副词 + 时间副词</div>
                        </div>
                    </div>
                </div>
                <div>
                    <h4 class="font-semibold text-gray-800 mb-3 text-center">方式 + 地点</h4>
                    <div class="space-y-3">
                        <div class="card bg-blue-50 border border-blue-200 rounded-lg p-3 shadow-sm">
                            <div class="keyword text-sm mb-1">He drives carefully downtown.</div>
                            <div class="text-xs text-gray-600 mb-1">他在市中心小心驾驶。</div>
                            <div class="text-xs text-gray-500">方式副词 + 地点副词</div>
                        </div>
                        <div class="card bg-blue-50 border border-blue-200 rounded-lg p-3 shadow-sm">
                            <div class="keyword text-sm mb-1">She sings beautifully here.</div>
                            <div class="text-xs text-gray-600 mb-1">她在这里唱得很美。</div>
                            <div class="text-xs text-gray-500">方式副词 + 地点副词</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 频率副词的特殊位置 -->
            <h3 class="text-lg font-semibold mb-3 text-gray-800">3. 频率副词的特殊位置</h3>
            <p class="text-gray-700 mb-4">频率副词（always, usually, often等）有其特殊的位置规则，通常不遵循方式-地点-时间的顺序，而是根据动词类型确定位置。</p>

            <div class="bg-indigo-50 border border-indigo-200 rounded-lg p-6 mb-6">
                <h4 class="font-semibold text-gray-800 mb-3">频率副词与其他副词的组合</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <h5 class="font-medium text-gray-800 mb-2">频率副词 + 方式副词</h5>
                        <div class="space-y-2 text-sm">
                            <div class="bg-white p-3 rounded border">
                                <div class="keyword mb-1">She always sings beautifully at concerts.</div>
                                <div class="text-gray-600 mb-1">她在音乐会上总是唱得很美。</div>
                                <div class="text-xs text-gray-500">频率副词在动词前，方式副词在动词后</div>
                            </div>
                            <div class="bg-white p-3 rounded border">
                                <div class="keyword mb-1">He usually works hard in the office.</div>
                                <div class="text-gray-600 mb-1">他通常在办公室努力工作。</div>
                                <div class="text-xs text-gray-500">频率副词 + 方式副词 + 地点副词</div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <h5 class="font-medium text-gray-800 mb-2">频率副词 + 时间副词</h5>
                        <div class="space-y-2 text-sm">
                            <div class="bg-white p-3 rounded border">
                                <div class="keyword mb-1">I often go there on weekends.</div>
                                <div class="text-gray-600 mb-1">我经常在周末去那里。</div>
                                <div class="text-xs text-gray-500">频率副词 + 地点副词 + 时间副词</div>
                            </div>
                            <div class="bg-white p-3 rounded border">
                                <div class="keyword mb-1">They usually meet here every Friday.</div>
                                <div class="text-gray-600 mb-1">他们通常每周五在这里见面。</div>
                                <div class="text-xs text-gray-500">频率副词 + 地点副词 + 时间副词</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 程度副词的位置 -->
            <h3 class="text-lg font-semibold mb-3 text-gray-800">4. 程度副词的位置</h3>
            <p class="text-gray-700 mb-4">程度副词（very, quite, extremely等）通常紧贴在它们所修饰的词之前，不参与方式-地点-时间的排序。</p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">程度副词 + 方式副词</div>
                    <div class="keyword text-lg mb-1">She sings very beautifully at home every day.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi sɪŋz ˈveri ˈbjutəfəli æt hoʊm ˈevri deɪ/</div>
                    <div class="text-gray-700 mb-2">她每天在家唱得非常美。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">程度副词修饰方式副词，不影响整体顺序</div>
                </div>

                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">程度副词 + 方式副词</div>
                    <div class="keyword text-lg mb-1">He drives quite carefully in the city today.</div>
                    <div class="text-sm text-gray-600 mb-1">/hi draɪvz kwaɪt ˈkerfəli ɪn ðə ˈsɪti təˈdeɪ/</div>
                    <div class="text-gray-700 mb-2">他今天在城里开车相当小心。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">quite修饰carefully，整体作为方式副词</div>
                </div>
            </div>
        </section>

        <!-- 地点副词的位置 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">地点副词的位置</h2>
            <p class="text-gray-700 mb-4">地点副词（Adverbs of Place）用来表示动作发生的地点、方向或位置关系。它们在句子中的位置相对固定，通常位于句末，但也可以位于句首以示强调。理解地点副词的位置规则对于准确描述空间关系和动作发生的场所非常重要。</p>

            <!-- 地点副词的分类 -->
            <div class="bg-green-50 border border-green-200 rounded-lg p-6 mb-6">
                <h3 class="text-lg font-semibold mb-3 text-gray-800">地点副词的分类</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div class="bg-white p-4 rounded border">
                        <h4 class="font-semibold text-gray-800 mb-2">指示地点</h4>
                        <div class="text-sm text-gray-600 space-y-1">
                            <div>• here (这里)</div>
                            <div>• there (那里)</div>
                            <div>• where (哪里)</div>
                            <div>• somewhere (某处)</div>
                            <div>• anywhere (任何地方)</div>
                            <div>• nowhere (无处)</div>
                        </div>
                    </div>
                    <div class="bg-white p-4 rounded border">
                        <h4 class="font-semibold text-gray-800 mb-2">方向副词</h4>
                        <div class="text-sm text-gray-600 space-y-1">
                            <div>• up (向上)</div>
                            <div>• down (向下)</div>
                            <div>• in (向内)</div>
                            <div>• out (向外)</div>
                            <div>• away (离开)</div>
                            <div>• back (回来)</div>
                        </div>
                    </div>
                    <div class="bg-white p-4 rounded border">
                        <h4 class="font-semibold text-gray-800 mb-2">位置关系</h4>
                        <div class="text-sm text-gray-600 space-y-1">
                            <div>• inside (里面)</div>
                            <div>• outside (外面)</div>
                            <div>• upstairs (楼上)</div>
                            <div>• downstairs (楼下)</div>
                            <div>• nearby (附近)</div>
                            <div>• far away (远处)</div>
                        </div>
                    </div>
                    <div class="bg-white p-4 rounded border">
                        <h4 class="font-semibold text-gray-800 mb-2">具体地点</h4>
                        <div class="text-sm text-gray-600 space-y-1">
                            <div>• home (家)</div>
                            <div>• abroad (国外)</div>
                            <div>• downtown (市中心)</div>
                            <div>• overseas (海外)</div>
                            <div>• indoors (室内)</div>
                            <div>• outdoors (户外)</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 句末位置（标准位置） -->
            <h3 class="text-lg font-semibold mb-3 text-gray-800">1. 句末位置（最常见且自然）</h3>
            <p class="text-gray-700 mb-4">地点副词最常见的位置是句末，这是最自然的表达方式。句末的地点副词通常不需要逗号分隔，直接跟在动词或宾语之后。</p>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">指示地点副词</div>
                    <div class="keyword text-lg mb-1">She lives here.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi lɪvz hɪr/</div>
                    <div class="text-gray-700 mb-2">她住在这里。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">here是最常用的指示地点副词</div>
                </div>

                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">方向副词</div>
                    <div class="keyword text-lg mb-1">They went outside.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðeɪ went ˈaʊtˌsaɪd/</div>
                    <div class="text-gray-700 mb-2">他们出去了。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">outside表示向外的方向</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">位置关系副词</div>
                    <div class="keyword text-lg mb-1">The children are playing upstairs.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈtʃɪldrən ɑr ˈpleɪɪŋ ˈʌpˌsterz/</div>
                    <div class="text-gray-700 mb-2">孩子们在楼上玩。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">upstairs描述具体的位置关系</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">具体地点副词</div>
                    <div class="keyword text-lg mb-1">He works downtown.</div>
                    <div class="text-sm text-gray-600 mb-1">/hi wərks ˈdaʊnˌtaʊn/</div>
                    <div class="text-gray-700 mb-2">他在市中心工作。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">downtown指具体的地理位置</div>
                </div>

                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">方向副词</div>
                    <div class="keyword text-lg mb-1">Please come back.</div>
                    <div class="text-sm text-gray-600 mb-1">/pliz kʌm bæk/</div>
                    <div class="text-gray-700 mb-2">请回来。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">back表示返回的方向</div>
                </div>

                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">环境地点副词</div>
                    <div class="keyword text-lg mb-1">We prefer to eat outdoors.</div>
                    <div class="text-sm text-gray-600 mb-1">/wi prɪˈfər tu it ˈaʊtˌdɔrz/</div>
                    <div class="text-gray-700 mb-2">我们更喜欢在户外用餐。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">outdoors描述用餐的环境</div>
                </div>
            </div>

            <!-- 句末地点副词的语法特点 -->
            <div class="bg-gray-50 border border-gray-200 rounded-lg p-6 mb-6">
                <h4 class="font-semibold text-gray-800 mb-3">句末地点副词的语法特点</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h5 class="font-medium text-gray-800 mb-3">与动词的关系</h5>
                        <div class="space-y-2 text-sm">
                            <div class="bg-white p-3 rounded border">
                                <div class="font-medium text-gray-800">不及物动词</div>
                                <div class="text-gray-600">She lives there.</div>
                                <div class="text-xs text-gray-500 mt-1">直接跟在动词后面</div>
                            </div>
                            <div class="bg-white p-3 rounded border">
                                <div class="font-medium text-gray-800">及物动词</div>
                                <div class="text-gray-600">I put the book there.</div>
                                <div class="text-xs text-gray-500 mt-1">跟在动词和宾语后面</div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <h5 class="font-medium text-gray-800 mb-3">语调特点</h5>
                        <div class="space-y-2 text-sm">
                            <div class="bg-white p-3 rounded border">
                                <div class="font-medium text-gray-800">自然语调</div>
                                <div class="text-gray-600">句末地点副词语调自然</div>
                                <div class="text-xs text-gray-500 mt-1">不需要特别强调</div>
                            </div>
                            <div class="bg-white p-3 rounded border">
                                <div class="font-medium text-gray-800">重音位置</div>
                                <div class="text-gray-600">通常不重读</div>
                                <div class="text-xs text-gray-500 mt-1">除非需要对比或强调</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 句首强调位置 -->
            <h3 class="text-lg font-semibold mb-3 text-gray-800">2. 句首强调位置</h3>
            <p class="text-gray-700 mb-4">地点副词放在句首时，通常用于强调地点的重要性，建立场景背景，或者引导读者的注意力。句首的地点副词后面需要用逗号分隔。</p>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">位置指引</div>
                    <div class="keyword text-lg mb-1">Upstairs, you'll find the library.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈʌpˌsterz, jul faɪnd ðə ˈlaɪˌbreri/</div>
                    <div class="text-gray-700 mb-2">楼上，你会找到图书馆。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">为听者提供明确的位置指引</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">场景设定</div>
                    <div class="keyword text-lg mb-1">Outside, the weather was terrible.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈaʊtˌsaɪd, ðə ˈweðər wəz ˈterəbəl/</div>
                    <div class="text-gray-700 mb-2">外面，天气很糟糕。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">建立故事或描述的场景背景</div>
                </div>

                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">对比强调</div>
                    <div class="keyword text-lg mb-1">Here, we have better facilities.</div>
                    <div class="text-sm text-gray-600 mb-1">/hɪr, wi hæv ˈbetər fəˈsɪlətiz/</div>
                    <div class="text-gray-700 mb-2">在这里，我们有更好的设施。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">强调"这里"与其他地方的对比</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">环境描述</div>
                    <div class="keyword text-lg mb-1">Indoors, the temperature is comfortable.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈɪnˌdɔrz, ðə ˈtempərətʃər ɪz ˈkʌmfərtəbəl/</div>
                    <div class="text-gray-700 mb-2">室内，温度很舒适。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">描述特定环境的特征</div>
                </div>

                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">方向指示</div>
                    <div class="keyword text-lg mb-1">Nearby, there's a good restaurant.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈnɪrˌbaɪ, ðerz ə gʊd ˈrestərənt/</div>
                    <div class="text-gray-700 mb-2">附近，有一家不错的餐厅。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">提供相对位置的信息</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">地理位置</div>
                    <div class="keyword text-lg mb-1">Abroad, the situation is different.</div>
                    <div class="text-sm text-gray-600 mb-1">/əˈbrɔd, ðə ˌsɪtʃuˈeɪʃən ɪz ˈdɪfərənt/</div>
                    <div class="text-gray-700 mb-2">在国外，情况是不同的。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">强调不同地理位置的对比</div>
                </div>
            </div>
        </section>

            <!-- 地点与时间副词的组合 -->
            <h3 class="text-lg font-semibold mb-3 text-gray-800">3. 地点 + 时间的标准顺序</h3>
            <p class="text-gray-700 mb-4">当句子中同时出现地点副词和时间副词时，标准顺序是地点副词在前，时间副词在后。这个规则是英语副词排序的重要组成部分。</p>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">地点 + 时间</div>
                    <div class="keyword text-lg mb-1">I'll meet you there tomorrow.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪl mit ju ðer təˈmɑroʊ/</div>
                    <div class="text-gray-700 mb-2">我明天在那里见你。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">there (地点) + tomorrow (时间)</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">地点 + 时间</div>
                    <div class="keyword text-lg mb-1">She worked at home yesterday.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi wərkt æt hoʊm ˈjestərdeɪ/</div>
                    <div class="text-gray-700 mb-2">她昨天在家工作。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">at home (地点) + yesterday (时间)</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">地点 + 时间</div>
                    <div class="keyword text-lg mb-1">They studied in the library last night.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðeɪ ˈstʌdid ɪn ðə ˈlaɪˌbreri læst naɪt/</div>
                    <div class="text-gray-700 mb-2">他们昨晚在图书馆学习。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">in the library (地点) + last night (时间)</div>
                </div>

                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">地点 + 时间</div>
                    <div class="keyword text-lg mb-1">We'll have dinner downtown tonight.</div>
                    <div class="text-sm text-gray-600 mb-1">/wil hæv ˈdɪnər ˈdaʊnˌtaʊn təˈnaɪt/</div>
                    <div class="text-gray-700 mb-2">我们今晚在市中心吃晚餐。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">downtown (地点) + tonight (时间)</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">地点 + 时间</div>
                    <div class="keyword text-lg mb-1">He exercises outdoors every morning.</div>
                    <div class="text-sm text-gray-600 mb-1">/hi ˈeksərˌsaɪzəz ˈaʊtˌdɔrz ˈevri ˈmɔrnɪŋ/</div>
                    <div class="text-gray-700 mb-2">他每天早上在户外锻炼。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">outdoors (地点) + every morning (时间)</div>
                </div>

                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">地点 + 时间</div>
                    <div class="keyword text-lg mb-1">She travels abroad frequently.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi ˈtrævəlz əˈbrɔd ˈfrikwəntli/</div>
                    <div class="text-gray-700 mb-2">她经常出国旅行。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">abroad (地点) + frequently (频率时间)</div>
                </div>
            </div>

            <!-- 地点副词的特殊用法 -->
            <div class="bg-teal-50 border border-teal-200 rounded-lg p-6 mb-6">
                <h4 class="font-semibold text-gray-800 mb-3">地点副词的特殊用法和注意事项</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h5 class="font-medium text-gray-800 mb-3">方向副词的双重含义</h5>
                        <div class="space-y-2 text-sm">
                            <div class="bg-white p-3 rounded border">
                                <div class="font-medium text-gray-800">静态位置</div>
                                <div class="text-gray-600">The cat is outside.</div>
                                <div class="text-xs text-gray-500 mt-1">猫在外面（位置）</div>
                            </div>
                            <div class="bg-white p-3 rounded border">
                                <div class="font-medium text-gray-800">动态方向</div>
                                <div class="text-gray-600">The cat went outside.</div>
                                <div class="text-xs text-gray-500 mt-1">猫出去了（方向）</div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <h5 class="font-medium text-gray-800 mb-3">地点副词与介词短语的区别</h5>
                        <div class="space-y-2 text-sm">
                            <div class="bg-white p-3 rounded border">
                                <div class="font-medium text-gray-800">地点副词</div>
                                <div class="text-gray-600">She lives nearby.</div>
                                <div class="text-xs text-gray-500 mt-1">单独使用，不需要宾语</div>
                            </div>
                            <div class="bg-white p-3 rounded border">
                                <div class="font-medium text-gray-800">介词短语</div>
                                <div class="text-gray-600">She lives near the school.</div>
                                <div class="text-xs text-gray-500 mt-1">需要介词和宾语</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 地点副词在不同句型中的应用 -->
            <div class="bg-orange-50 border border-orange-200 rounded-lg p-6">
                <h4 class="font-semibold text-gray-800 mb-3">地点副词在不同句型中的应用</h4>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <h5 class="font-medium text-gray-800 mb-3">陈述句</h5>
                        <div class="space-y-2 text-sm">
                            <div class="bg-white p-3 rounded border">
                                <div class="keyword mb-1">I work here.</div>
                                <div class="text-gray-600 mb-1">我在这里工作。</div>
                                <div class="text-xs text-gray-500">标准的陈述句结构</div>
                            </div>
                            <div class="bg-white p-3 rounded border">
                                <div class="keyword mb-1">They moved away.</div>
                                <div class="text-gray-600 mb-1">他们搬走了。</div>
                                <div class="text-xs text-gray-500">表示方向的变化</div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <h5 class="font-medium text-gray-800 mb-3">疑问句</h5>
                        <div class="space-y-2 text-sm">
                            <div class="bg-white p-3 rounded border">
                                <div class="keyword mb-1">Where do you live?</div>
                                <div class="text-gray-600 mb-1">你住在哪里？</div>
                                <div class="text-xs text-gray-500">疑问副词where</div>
                            </div>
                            <div class="bg-white p-3 rounded border">
                                <div class="keyword mb-1">Are you going there?</div>
                                <div class="text-gray-600 mb-1">你要去那里吗？</div>
                                <div class="text-xs text-gray-500">一般疑问句中的地点副词</div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <h5 class="font-medium text-gray-800 mb-3">祈使句</h5>
                        <div class="space-y-2 text-sm">
                            <div class="bg-white p-3 rounded border">
                                <div class="keyword mb-1">Come here!</div>
                                <div class="text-gray-600 mb-1">过来！</div>
                                <div class="text-xs text-gray-500">命令或请求</div>
                            </div>
                            <div class="bg-white p-3 rounded border">
                                <div class="keyword mb-1">Go upstairs.</div>
                                <div class="text-gray-600 mb-1">上楼去。</div>
                                <div class="text-xs text-gray-500">指示方向</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 程度副词的位置 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">程度副词的位置</h2>
            <p class="text-gray-700 mb-4">程度副词（Adverbs of Degree）用来表示形容词、副词或动词的强度、程度或数量。它们在句子中的位置相对固定，通常紧贴在它们所修饰的词之前。程度副词是英语中使用频率很高的副词类型，掌握它们的位置规则对于准确表达程度和强调非常重要。</p>

            <!-- 程度副词的分类 -->
            <div class="bg-purple-50 border border-purple-200 rounded-lg p-6 mb-6">
                <h3 class="text-lg font-semibold mb-3 text-gray-800">程度副词的分类与强度</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div class="bg-white p-4 rounded border">
                        <h4 class="font-semibold text-gray-800 mb-2">极高程度</h4>
                        <div class="text-sm text-gray-600 space-y-1">
                            <div>• extremely (极其)</div>
                            <div>• incredibly (难以置信地)</div>
                            <div>• tremendously (极大地)</div>
                            <div>• exceptionally (异常地)</div>
                            <div>• remarkably (显著地)</div>
                        </div>
                    </div>
                    <div class="bg-white p-4 rounded border">
                        <h4 class="font-semibold text-gray-800 mb-2">高程度</h4>
                        <div class="text-sm text-gray-600 space-y-1">
                            <div>• very (非常)</div>
                            <div>• really (真的)</div>
                            <div>• highly (高度地)</div>
                            <div>• greatly (大大地)</div>
                            <div>• deeply (深深地)</div>
                        </div>
                    </div>
                    <div class="bg-white p-4 rounded border">
                        <h4 class="font-semibold text-gray-800 mb-2">中等程度</h4>
                        <div class="text-sm text-gray-600 space-y-1">
                            <div>• quite (相当)</div>
                            <div>• rather (颇为)</div>
                            <div>• fairly (相当)</div>
                            <div>• pretty (相当)</div>
                            <div>• moderately (适度地)</div>
                        </div>
                    </div>
                    <div class="bg-white p-4 rounded border">
                        <h4 class="font-semibold text-gray-800 mb-2">低程度</h4>
                        <div class="text-sm text-gray-600 space-y-1">
                            <div>• slightly (稍微)</div>
                            <div>• somewhat (有些)</div>
                            <div>• a little (一点)</div>
                            <div>• barely (几乎不)</div>
                            <div>• hardly (几乎不)</div>
                        </div>
                    </div>
                </div>
            </div>

            <h3 class="text-lg font-semibold mb-3 text-gray-800">1. 修饰形容词</h3>
            <p class="text-gray-700 mb-4">程度副词最常见的用法是修饰形容词，表示形容词所描述特征的强度或程度。程度副词必须紧贴在形容词之前，不能分离。</p>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">高程度修饰</div>
                    <div class="keyword text-lg mb-1">She is very beautiful.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi ɪz ˈveri ˈbjutəfəl/</div>
                    <div class="text-gray-700 mb-2">她非常漂亮。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">very是最常用的程度副词</div>
                </div>

                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">中等程度修饰</div>
                    <div class="keyword text-lg mb-1">The movie is quite interesting.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈmuvi ɪz kwaɪt ˈɪntrəstɪŋ/</div>
                    <div class="text-gray-700 mb-2">这部电影相当有趣。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">quite表示中等偏上的程度</div>
                </div>

                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">极高程度修饰</div>
                    <div class="keyword text-lg mb-1">It's extremely cold today.</div>
                    <div class="text-sm text-gray-600 mb-1">/ɪts ɪkˈstrimli koʊld təˈdeɪ/</div>
                    <div class="text-gray-700 mb-2">今天极其寒冷。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">extremely表示极高的程度</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">强调程度</div>
                    <div class="keyword text-lg mb-1">This is really important.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðɪs ɪz ˈrili ɪmˈpɔrtənt/</div>
                    <div class="text-gray-700 mb-2">这真的很重要。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">really常用于口语强调</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">中等程度</div>
                    <div class="keyword text-lg mb-1">He's rather tall.</div>
                    <div class="text-sm text-gray-600 mb-1">/hiz ˈræðər tɔl/</div>
                    <div class="text-gray-700 mb-2">他相当高。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">rather常用于英式英语</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">低程度修饰</div>
                    <div class="keyword text-lg mb-1">I'm slightly tired.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪm ˈslaɪtli ˈtaɪərd/</div>
                    <div class="text-gray-700 mb-2">我有点累。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">slightly表示轻微的程度</div>
                </div>
            </div>

            <!-- 程度副词修饰形容词的语法规则 -->
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-6">
                <h4 class="font-semibold text-gray-800 mb-3">程度副词修饰形容词的语法规则</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h5 class="font-medium text-gray-800 mb-3">位置规则</h5>
                        <div class="space-y-2 text-sm">
                            <div class="bg-white p-3 rounded border">
                                <div class="font-medium text-gray-800">✅ 正确</div>
                                <div class="text-gray-600">She is very happy.</div>
                                <div class="text-xs text-gray-500 mt-1">程度副词紧贴形容词前</div>
                            </div>
                            <div class="bg-white p-3 rounded border">
                                <div class="font-medium text-red-600">❌ 错误</div>
                                <div class="text-red-600">She is happy very.</div>
                                <div class="text-xs text-gray-500 mt-1">程度副词不能放在形容词后</div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <h5 class="font-medium text-gray-800 mb-3">强度搭配</h5>
                        <div class="space-y-2 text-sm">
                            <div class="bg-white p-3 rounded border">
                                <div class="font-medium text-gray-800">可分级形容词</div>
                                <div class="text-gray-600">very good, quite nice</div>
                                <div class="text-xs text-gray-500 mt-1">可以用程度副词修饰</div>
                            </div>
                            <div class="bg-white p-3 rounded border">
                                <div class="font-medium text-gray-800">不可分级形容词</div>
                                <div class="text-gray-600">completely perfect</div>
                                <div class="text-xs text-gray-500 mt-1">只能用特定程度副词</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <h3 class="text-lg font-semibold mb-3 text-gray-800">2. 修饰副词</h3>
            <p class="text-gray-700 mb-4">程度副词也可以修饰其他副词，特别是方式副词，用来表示动作执行的程度或强度。这种用法在描述动作的精确程度时非常有用。</p>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">修饰方式副词</div>
                    <div class="keyword text-lg mb-1">He runs very fast.</div>
                    <div class="text-sm text-gray-600 mb-1">/hi rʌnz ˈveri fæst/</div>
                    <div class="text-gray-700 mb-2">他跑得非常快。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">very修饰方式副词fast</div>
                </div>

                <div class="card bg-gray-50 border border-gray-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">修饰方式副词</div>
                    <div class="keyword text-lg mb-1">She speaks quite clearly.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi spiks kwaɪt ˈklɪrli/</div>
                    <div class="text-gray-700 mb-2">她说话相当清楚。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">quite修饰方式副词clearly</div>
                </div>

                <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">修饰方式副词</div>
                    <div class="keyword text-lg mb-1">They work rather slowly.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðeɪ wərk ˈræðər ˈsloʊli/</div>
                    <div class="text-gray-700 mb-2">他们工作相当慢。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">rather修饰方式副词slowly</div>
                </div>

                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">修饰方式副词</div>
                    <div class="keyword text-lg mb-1">She dances extremely gracefully.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi dænsəz ɪkˈstrimli ˈgreɪsfəli/</div>
                    <div class="text-gray-700 mb-2">她跳舞极其优雅。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">extremely修饰方式副词gracefully</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">修饰方式副词</div>
                    <div class="keyword text-lg mb-1">He drives incredibly carefully.</div>
                    <div class="text-sm text-gray-600 mb-1">/hi draɪvz ɪnˈkredəbli ˈkerfəli/</div>
                    <div class="text-gray-700 mb-2">他开车极其小心。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">incredibly修饰方式副词carefully</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">修饰方式副词</div>
                    <div class="keyword text-lg mb-1">They responded surprisingly quickly.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðeɪ rɪˈspɑndəd sərˈpraɪzɪŋli ˈkwɪkli/</div>
                    <div class="text-gray-700 mb-2">他们回应得出人意料地快。</div>
                    <div class="text-xs text-gray-500 bg-white p-2 rounded">surprisingly修饰方式副词quickly</div>
                </div>
            </div>

            <!-- 程度副词修饰其他类型副词 -->
            <div class="bg-green-50 border border-green-200 rounded-lg p-6 mb-6">
                <h4 class="font-semibold text-gray-800 mb-3">程度副词修饰不同类型副词</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h5 class="font-medium text-gray-800 mb-3">修饰频率副词</h5>
                        <div class="space-y-2 text-sm">
                            <div class="bg-white p-3 rounded border">
                                <div class="keyword mb-1">She visits very often.</div>
                                <div class="text-gray-600 mb-1">她拜访得非常频繁。</div>
                                <div class="text-xs text-gray-500">very修饰频率副词often</div>
                            </div>
                            <div class="bg-white p-3 rounded border">
                                <div class="keyword mb-1">He calls quite regularly.</div>
                                <div class="text-gray-600 mb-1">他打电话相当有规律。</div>
                                <div class="text-xs text-gray-500">quite修饰频率副词regularly</div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <h5 class="font-medium text-gray-800 mb-3">修饰时间副词</h5>
                        <div class="space-y-2 text-sm">
                            <div class="bg-white p-3 rounded border">
                                <div class="keyword mb-1">Come very soon.</div>
                                <div class="text-gray-600 mb-1">很快就来。</div>
                                <div class="text-xs text-gray-500">very修饰时间副词soon</div>
                            </div>
                            <div class="bg-white p-3 rounded border">
                                <div class="keyword mb-1">It happened quite recently.</div>
                                <div class="text-gray-600 mb-1">这发生得相当近。</div>
                                <div class="text-xs text-gray-500">quite修饰时间副词recently</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 程度副词的特殊用法 -->
            <h3 class="text-lg font-semibold mb-3 text-gray-800">3. 程度副词的特殊用法</h3>
            <p class="text-gray-700 mb-4">某些程度副词有特殊的用法和位置规则，需要特别注意。这些特殊用法往往与语义和语用有关。</p>

            <div class="bg-orange-50 border border-orange-200 rounded-lg p-6 mb-6">
                <h4 class="font-semibold text-gray-800 mb-3">特殊程度副词的用法</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h5 class="font-medium text-gray-800 mb-3">enough的位置</h5>
                        <div class="space-y-2 text-sm">
                            <div class="bg-white p-3 rounded border">
                                <div class="font-medium text-gray-800">形容词 + enough</div>
                                <div class="text-gray-600">She is old enough to drive.</div>
                                <div class="text-xs text-gray-500 mt-1">enough放在形容词后面</div>
                            </div>
                            <div class="bg-white p-3 rounded border">
                                <div class="font-medium text-gray-800">副词 + enough</div>
                                <div class="text-gray-600">He runs fast enough.</div>
                                <div class="text-xs text-gray-500 mt-1">enough放在副词后面</div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <h5 class="font-medium text-gray-800 mb-3">too的双重含义</h5>
                        <div class="space-y-2 text-sm">
                            <div class="bg-white p-3 rounded border">
                                <div class="font-medium text-gray-800">程度过高</div>
                                <div class="text-gray-600">It's too hot today.</div>
                                <div class="text-xs text-gray-500 mt-1">表示程度过度</div>
                            </div>
                            <div class="bg-white p-3 rounded border">
                                <div class="font-medium text-gray-800">表示"也"</div>
                                <div class="text-gray-600">I like it too.</div>
                                <div class="text-xs text-gray-500 mt-1">放在句末表示"也"</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 程度副词的语义层次 -->
            <div class="bg-red-50 border border-red-200 rounded-lg p-6">
                <h4 class="font-semibold text-gray-800 mb-3">程度副词的语义层次和搭配</h4>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <h5 class="font-medium text-gray-800 mb-3">强化程度</h5>
                        <div class="space-y-2 text-sm">
                            <div class="bg-white p-2 rounded border">
                                <div class="keyword text-xs">absolutely perfect</div>
                                <div class="text-gray-600 text-xs">绝对完美</div>
                            </div>
                            <div class="bg-white p-2 rounded border">
                                <div class="keyword text-xs">completely different</div>
                                <div class="text-gray-600 text-xs">完全不同</div>
                            </div>
                            <div class="bg-white p-2 rounded border">
                                <div class="keyword text-xs">totally wrong</div>
                                <div class="text-gray-600 text-xs">完全错误</div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <h5 class="font-medium text-gray-800 mb-3">缓和程度</h5>
                        <div class="space-y-2 text-sm">
                            <div class="bg-white p-2 rounded border">
                                <div class="keyword text-xs">somewhat difficult</div>
                                <div class="text-gray-600 text-xs">有些困难</div>
                            </div>
                            <div class="bg-white p-2 rounded border">
                                <div class="keyword text-xs">fairly good</div>
                                <div class="text-gray-600 text-xs">相当好</div>
                            </div>
                            <div class="bg-white p-2 rounded border">
                                <div class="keyword text-xs">relatively easy</div>
                                <div class="text-gray-600 text-xs">相对容易</div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <h5 class="font-medium text-gray-800 mb-3">否定程度</h5>
                        <div class="space-y-2 text-sm">
                            <div class="bg-white p-2 rounded border">
                                <div class="keyword text-xs">hardly visible</div>
                                <div class="text-gray-600 text-xs">几乎看不见</div>
                            </div>
                            <div class="bg-white p-2 rounded border">
                                <div class="keyword text-xs">barely audible</div>
                                <div class="text-gray-600 text-xs">几乎听不见</div>
                            </div>
                            <div class="bg-white p-2 rounded border">
                                <div class="keyword text-xs">scarcely believable</div>
                                <div class="text-gray-600 text-xs">几乎不可信</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 否定副词的位置 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">否定副词的位置和倒装</h2>
            <p class="text-gray-700 mb-4">某些否定副词（never, rarely, seldom, hardly等）位于句首时，需要使用倒装语序。</p>

            <h3 class="text-lg font-semibold mb-3 text-gray-800">1. 正常语序</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">正常语序</div>
                    <div class="keyword text-lg mb-1">I have never seen this before.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ hæv ˈnevər sin ðɪs bɪˈfɔr/</div>
                    <div class="text-gray-700">我以前从未见过这个。</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">正常语序</div>
                    <div class="keyword text-lg mb-1">She rarely goes out.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi ˈrerli goʊz aʊt/</div>
                    <div class="text-gray-700">她很少出去。</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">正常语序</div>
                    <div class="keyword text-lg mb-1">We seldom meet.</div>
                    <div class="text-sm text-gray-600 mb-1">/wi ˈseldəm mit/</div>
                    <div class="text-gray-700">我们很少见面。</div>
                </div>
            </div>

            <h3 class="text-lg font-semibold mb-3 text-gray-800">2. 倒装语序（句首强调）</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">倒装语序</div>
                    <div class="keyword text-lg mb-1">Never have I seen this before.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈnevər hæv aɪ sin ðɪs bɪˈfɔr/</div>
                    <div class="text-gray-700">我以前从未见过这个。</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">倒装语序</div>
                    <div class="keyword text-lg mb-1">Rarely does she go out.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈrerli dʌz ʃi goʊ aʊt/</div>
                    <div class="text-gray-700">她很少出去。</div>
                </div>
            </div>
        </section>

        <!-- 连接副词的位置 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">连接副词的位置</h2>
            <p class="text-gray-700 mb-4">连接副词（however, therefore, moreover, furthermore等）可以位于句首、句中或句末，用来连接句子或段落。</p>

            <h3 class="text-lg font-semibold mb-3 text-gray-800">1. 句首位置</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">句首连接副词</div>
                    <div class="keyword text-lg mb-1">However, I disagree with you.</div>
                    <div class="text-sm text-gray-600 mb-1">/haʊˈevər, aɪ ˌdɪsəˈgri wɪð ju/</div>
                    <div class="text-gray-700">然而，我不同意你的观点。</div>
                </div>

                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">句首连接副词</div>
                    <div class="keyword text-lg mb-1">Therefore, we must act quickly.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈðerfɔr, wi mʌst ækt ˈkwɪkli/</div>
                    <div class="text-gray-700">因此，我们必须迅速行动。</div>
                </div>
            </div>

            <h3 class="text-lg font-semibold mb-3 text-gray-800">2. 句中位置</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">句中连接副词</div>
                    <div class="keyword text-lg mb-1">I, however, disagree with you.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ, haʊˈevər, ˌdɪsəˈgri wɪð ju/</div>
                    <div class="text-gray-700">然而，我不同意你的观点。</div>
                </div>

                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">句中连接副词</div>
                    <div class="keyword text-lg mb-1">We must, therefore, act quickly.</div>
                    <div class="text-sm text-gray-600 mb-1">/wi mʌst, ˈðerfɔr, ækt ˈkwɪkli/</div>
                    <div class="text-gray-700">因此，我们必须迅速行动。</div>
                </div>
            </div>
        </section>

        <!-- 疑问副词的位置 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">疑问副词的位置</h2>
            <p class="text-gray-700 mb-4">疑问副词（when, where, why, how等）在疑问句中位于句首，在陈述句中的位置较为灵活。</p>

            <h3 class="text-lg font-semibold mb-3 text-gray-800">1. 疑问句中</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-gray-50 border border-gray-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">疑问副词句首</div>
                    <div class="keyword text-lg mb-1">When will you arrive?</div>
                    <div class="text-sm text-gray-600 mb-1">/wen wɪl ju əˈraɪv/</div>
                    <div class="text-gray-700">你什么时候到达？</div>
                </div>

                <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">疑问副词句首</div>
                    <div class="keyword text-lg mb-1">Where do you live?</div>
                    <div class="text-sm text-gray-600 mb-1">/wer du ju lɪv/</div>
                    <div class="text-gray-700">你住在哪里？</div>
                </div>

                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">疑问副词句首</div>
                    <div class="keyword text-lg mb-1">How did you know?</div>
                    <div class="text-sm text-gray-600 mb-1">/haʊ dɪd ju noʊ/</div>
                    <div class="text-gray-700">你怎么知道的？</div>
                </div>
            </div>

            <h3 class="text-lg font-semibold mb-3 text-gray-800">2. 间接疑问句中</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">间接疑问句</div>
                    <div class="keyword text-lg mb-1">I wonder when he will arrive.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ ˈwʌndər wen hi wɪl əˈraɪv/</div>
                    <div class="text-gray-700">我想知道他什么时候到达。</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">间接疑问句</div>
                    <div class="keyword text-lg mb-1">Tell me where you live.</div>
                    <div class="text-sm text-gray-600 mb-1">/tel mi wer ju lɪv/</div>
                    <div class="text-gray-700">告诉我你住在哪里。</div>
                </div>
            </div>
        </section>

        <!-- 多个副词的排列顺序 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">多个副词的排列顺序</h2>
            <p class="text-gray-700 mb-4">当句子中有多个副词时，通常遵循：方式 → 地点 → 时间的顺序。</p>

            <h3 class="text-lg font-semibold mb-3 text-gray-800">1. 方式 + 地点 + 时间</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">方式+地点+时间</div>
                    <div class="keyword text-lg mb-1">She sang beautifully at the concert last night.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi sæŋ ˈbjutəfəli æt ðə ˈkɑnsərt læst naɪt/</div>
                    <div class="text-gray-700">她昨晚在音乐会上唱得很美。</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">方式+地点+时间</div>
                    <div class="keyword text-lg mb-1">He worked hard in the office yesterday.</div>
                    <div class="text-sm text-gray-600 mb-1">/hi wərkt hɑrd ɪn ði ˈɔfɪs ˈjestərdeɪ/</div>
                    <div class="text-gray-700">他昨天在办公室努力工作。</div>
                </div>
            </div>

            <h3 class="text-lg font-semibold mb-3 text-gray-800">2. 频率副词的特殊位置</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">频率副词位置</div>
                    <div class="keyword text-lg mb-1">She always sings beautifully at concerts.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi ˈɔlweɪz sɪŋz ˈbjutəfəli æt ˈkɑnsərts/</div>
                    <div class="text-gray-700">她在音乐会上总是唱得很美。</div>
                </div>

                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">频率副词位置</div>
                    <div class="keyword text-lg mb-1">He usually works hard in the office.</div>
                    <div class="text-sm text-gray-600 mb-1">/hi ˈjuʒuəli wərks hɑrd ɪn ði ˈɔfɪs/</div>
                    <div class="text-gray-700">他通常在办公室努力工作。</div>
                </div>
            </div>
        </section>

        <!-- 副词位置的语义差异 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">副词位置的语义差异</h2>
            <p class="text-gray-700 mb-4">同一个副词在不同位置可能会产生不同的语义或强调效果。</p>

            <h3 class="text-lg font-semibold mb-3 text-gray-800">1. only的位置变化</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">修饰主语</div>
                    <div class="keyword text-lg mb-1">Only John can solve this problem.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈoʊnli dʒɑn kæn sɑlv ðɪs ˈprɑbləm/</div>
                    <div class="text-gray-700">只有约翰能解决这个问题。</div>
                </div>

                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">修饰动词</div>
                    <div class="keyword text-lg mb-1">John can only solve this problem.</div>
                    <div class="text-sm text-gray-600 mb-1">/dʒɑn kæn ˈoʊnli sɑlv ðɪs ˈprɑbləm/</div>
                    <div class="text-gray-700">约翰只能解决这个问题。</div>
                </div>
            </div>

            <h3 class="text-lg font-semibold mb-3 text-gray-800">2. even的位置变化</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-gray-50 border border-gray-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">修饰主语</div>
                    <div class="keyword text-lg mb-1">Even children understand this.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈivən ˈtʃɪldrən ˌʌndərˈstænd ðɪs/</div>
                    <div class="text-gray-700">连孩子都理解这个。</div>
                </div>

                <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">修饰动词</div>
                    <div class="keyword text-lg mb-1">Children even understand this.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈtʃɪldrən ˈivən ˌʌndərˈstænd ðɪs/</div>
                    <div class="text-gray-700">孩子们甚至理解这个。</div>
                </div>
            </div>
        </section>

        <!-- 副词位置的常见错误 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">副词位置的常见错误</h2>
            <p class="text-gray-700 mb-4">学习者在使用副词时经常犯的位置错误及其纠正方法。</p>

            <h3 class="text-lg font-semibold mb-3 text-gray-800">1. 频率副词位置错误</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-red-100 border border-red-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-red-600 mb-1">❌ 错误</div>
                    <div class="text-lg mb-1">I go always to school by bus.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ goʊ ˈɔlweɪz tu skul baɪ bʌs/</div>
                    <div class="text-gray-700">我总是乘公交车上学。</div>
                </div>

                <div class="card bg-green-100 border border-green-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-green-600 mb-1">✅ 正确</div>
                    <div class="keyword text-lg mb-1">I always go to school by bus.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ ˈɔlweɪz goʊ tu skul baɪ bʌs/</div>
                    <div class="text-gray-700">我总是乘公交车上学。</div>
                </div>
            </div>

            <h3 class="text-lg font-semibold mb-3 text-gray-800">2. 程度副词位置错误</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-red-100 border border-red-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-red-600 mb-1">❌ 错误</div>
                    <div class="text-lg mb-1">She is beautiful very.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi ɪz ˈbjutəfəl ˈveri/</div>
                    <div class="text-gray-700">她非常漂亮。</div>
                </div>

                <div class="card bg-green-100 border border-green-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-green-600 mb-1">✅ 正确</div>
                    <div class="keyword text-lg mb-1">She is very beautiful.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi ɪz ˈveri ˈbjutəfəl/</div>
                    <div class="text-gray-700">她非常漂亮。</div>
                </div>
            </div>

            <h3 class="text-lg font-semibold mb-3 text-gray-800">3. 方式副词位置错误</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-red-100 border border-red-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-red-600 mb-1">❌ 错误</div>
                    <div class="text-lg mb-1">He quickly very runs.</div>
                    <div class="text-sm text-gray-600 mb-1">/hi ˈkwɪkli ˈveri rʌnz/</div>
                    <div class="text-gray-700">他跑得非常快。</div>
                </div>

                <div class="card bg-green-100 border border-green-300 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-green-600 mb-1">✅ 正确</div>
                    <div class="keyword text-lg mb-1">He runs very quickly.</div>
                    <div class="text-sm text-gray-600 mb-1">/hi rʌnz ˈveri ˈkwɪkli/</div>
                    <div class="text-gray-700">他跑得非常快。</div>
                </div>
            </div>
        </section>

        <!-- 特殊副词的位置规则 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">特殊副词的位置规则</h2>
            <p class="text-gray-700 mb-4">某些副词有特殊的位置规则，需要特别注意。</p>

            <h3 class="text-lg font-semibold mb-3 text-gray-800">1. enough的位置</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">形容词 + enough</div>
                    <div class="keyword text-lg mb-1">She is old enough to drive.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi ɪz oʊld ɪˈnʌf tu draɪv/</div>
                    <div class="text-gray-700">她年龄够大可以开车了。</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">enough + 名词</div>
                    <div class="keyword text-lg mb-1">We have enough time.</div>
                    <div class="text-sm text-gray-600 mb-1">/wi hæv ɪˈnʌf taɪm/</div>
                    <div class="text-gray-700">我们有足够的时间。</div>
                </div>
            </div>

            <h3 class="text-lg font-semibold mb-3 text-gray-800">2. too的位置</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">too + 形容词</div>
                    <div class="keyword text-lg mb-1">It's too hot today.</div>
                    <div class="text-sm text-gray-600 mb-1">/ɪts tu hɑt təˈdeɪ/</div>
                    <div class="text-gray-700">今天太热了。</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">too + 副词</div>
                    <div class="keyword text-lg mb-1">He drives too fast.</div>
                    <div class="text-sm text-gray-600 mb-1">/hi draɪvz tu fæst/</div>
                    <div class="text-gray-700">他开车太快了。</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">句末表示"也"</div>
                    <div class="keyword text-lg mb-1">I like coffee too.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ laɪk ˈkɔfi tu/</div>
                    <div class="text-gray-700">我也喜欢咖啡。</div>
                </div>
            </div>
        </section>

        <!-- 实用练习示例 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">实用练习示例</h2>
            <p class="text-gray-700 mb-4">通过实际例句练习掌握副词的正确位置。</p>

            <h3 class="text-lg font-semibold mb-3 text-gray-800">1. 日常对话中的副词位置</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">日常对话</div>
                    <div class="keyword text-lg mb-1">I usually have breakfast at 7 AM.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ ˈjuʒuəli hæv ˈbrekfəst æt ˈsevən eɪ em/</div>
                    <div class="text-gray-700">我通常早上7点吃早餐。</div>
                </div>

                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">日常对话</div>
                    <div class="keyword text-lg mb-1">She carefully locked the door before leaving.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi ˈkerfəli lɑkt ðə dɔr bɪˈfɔr ˈlivɪŋ/</div>
                    <div class="text-gray-700">她离开前小心地锁了门。</div>
                </div>
            </div>

            <h3 class="text-lg font-semibold mb-3 text-gray-800">2. 商务场合中的副词位置</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">商务对话</div>
                    <div class="keyword text-lg mb-1">We need to carefully review the contract.</div>
                    <div class="text-sm text-gray-600 mb-1">/wi nid tu ˈkerfəli rɪˈvju ðə ˈkɑntrækt/</div>
                    <div class="text-gray-700">我们需要仔细审查合同。</div>
                </div>

                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">商务对话</div>
                    <div class="keyword text-lg mb-1">The project is progressing smoothly.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈprɑdʒekt ɪz prəˈgresɪŋ ˈsmuðli/</div>
                    <div class="text-gray-700">项目进展顺利。</div>
                </div>
            </div>

            <h3 class="text-lg font-semibold mb-3 text-gray-800">3. 学术写作中的副词位置</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-gray-50 border border-gray-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">学术写作</div>
                    <div class="keyword text-lg mb-1">Furthermore, the research clearly demonstrates...</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈfərðərˌmɔr, ðə rɪˈsərtʃ ˈklɪrli ˈdemənˌstreɪts/</div>
                    <div class="text-gray-700">此外，研究清楚地表明...</div>
                </div>

                <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">学术写作</div>
                    <div class="keyword text-lg mb-1">The results were carefully analyzed.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə rɪˈzʌlts wər ˈkerfəli ˈænəˌlaɪzd/</div>
                    <div class="text-gray-700">结果被仔细分析了。</div>
                </div>
            </div>
        </section>

        <!-- 总结要点 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">副词位置要点总结</h2>
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                <h3 class="text-lg font-semibold mb-3 text-gray-800">关键记忆点：</h3>
                <ul class="space-y-2 text-gray-700">
                    <li class="flex items-start">
                        <span class="keyword mr-2">•</span>
                        <span>频率副词：be动词后，实义动词前，助动词后</span>
                    </li>
                    <li class="flex items-start">
                        <span class="keyword mr-2">•</span>
                        <span>方式副词：通常在动词后或句末</span>
                    </li>
                    <li class="flex items-start">
                        <span class="keyword mr-2">•</span>
                        <span>时间地点副词：地点在前，时间在后</span>
                    </li>
                    <li class="flex items-start">
                        <span class="keyword mr-2">•</span>
                        <span>程度副词：在所修饰词之前</span>
                    </li>
                    <li class="flex items-start">
                        <span class="keyword mr-2">•</span>
                        <span>否定副词句首：需要倒装</span>
                    </li>
                    <li class="flex items-start">
                        <span class="keyword mr-2">•</span>
                        <span>多个副词：方式→地点→时间</span>
                    </li>
                </ul>
            </div>
        </section>

        <!-- 常用副词位置速查表 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">常用副词位置速查</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">频率副词</div>
                    <div class="keyword text-base mb-1">always, usually, often</div>
                    <div class="text-sm text-gray-600 mb-1">动词前/be动词后</div>
                    <div class="text-gray-700 text-sm">I always study.</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">方式副词</div>
                    <div class="keyword text-base mb-1">carefully, quickly</div>
                    <div class="text-sm text-gray-600 mb-1">动词后/句末</div>
                    <div class="text-gray-700 text-sm">He works hard.</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">程度副词</div>
                    <div class="keyword text-base mb-1">very, quite, rather</div>
                    <div class="text-sm text-gray-600 mb-1">修饰词前</div>
                    <div class="text-gray-700 text-sm">Very good!</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">时间副词</div>
                    <div class="keyword text-base mb-1">yesterday, tomorrow</div>
                    <div class="text-sm text-gray-600 mb-1">句首/句末</div>
                    <div class="text-gray-700 text-sm">See you tomorrow.</div>
                </div>
            </div>
        </section>

    </div>
</body>
</html>