<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>主语和谓语识别 - 英语学习</title>
    <script src="/static/tailwindcss-3.4.17.js"></script>
    <style>
        .keyword {
            color: #3d74ed;
            font-weight: bold;
        }
    </style>
</head>
<body class="bg-white">
    <div class="p-6">
        <!-- 主语和谓语基础概念 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">主语和谓语：句子的核心结构</h2>
            <p class="text-gray-700 mb-4">
                在英语句子中，主语（Subject）和谓语（Predicate）是构成句子的两个基本要素。主语是句子描述的对象，谓语则说明主语做什么或处于什么状态。理解主语和谓语的识别方法是掌握英语语法的基础。
            </p>
        </section>

        <!-- 主语的识别 -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">主语的识别</h3>
            <p class="text-gray-700 mb-4">
                主语通常是句子中执行动作或被描述的人、事物或概念。主语可以是名词、代词、动名词、不定式或从句。
            </p>

            <!-- 主语类型示例 -->
            <h4 class="text-lg font-medium mb-3 text-gray-800">主语的类型</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">名词作主语</div>
                    <div class="keyword text-lg mb-1">The cat sleeps.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə kæt sliːps/</div>
                    <div class="text-gray-700">猫在睡觉。</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">代词作主语</div>
                    <div class="keyword text-lg mb-1">She sings beautifully.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi sɪŋz ˈbjuːtɪfəli/</div>
                    <div class="text-gray-700">她唱得很美。</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">动名词作主语</div>
                    <div class="keyword text-lg mb-1">Swimming is fun.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈswɪmɪŋ ɪz fʌn/</div>
                    <div class="text-gray-700">游泳很有趣。</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">不定式作主语</div>
                    <div class="keyword text-lg mb-1">To learn English is important.</div>
                    <div class="text-sm text-gray-600 mb-1">/tu lɜːrn ˈɪŋɡlɪʃ ɪz ɪmˈpɔːrtənt/</div>
                    <div class="text-gray-700">学英语很重要。</div>
                </div>

                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">从句作主语</div>
                    <div class="keyword text-lg mb-1">What he said is true.</div>
                    <div class="text-sm text-gray-600 mb-1">/wʌt hi sed ɪz truː/</div>
                    <div class="text-gray-700">他说的是真的。</div>
                </div>

                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">复合主语</div>
                    <div class="keyword text-lg mb-1">Tom and Jerry are friends.</div>
                    <div class="text-sm text-gray-600 mb-1">/tɑːm ænd ˈdʒeri ɑːr frends/</div>
                    <div class="text-gray-700">汤姆和杰瑞是朋友。</div>
                </div>
            </div>
        </section>

        <!-- 谓语的识别 -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">谓语的识别</h3>
            <p class="text-gray-700 mb-4">
                谓语是句子中说明主语动作或状态的部分，通常包含动词。谓语可以是简单谓语（只有动词）或复合谓语（动词+其他成分）。
            </p>

            <!-- 谓语类型示例 -->
            <h4 class="text-lg font-medium mb-3 text-gray-800">谓语的类型</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">简单谓语</div>
                    <div class="keyword text-lg mb-1">Birds fly.</div>
                    <div class="text-sm text-gray-600 mb-1">/bɜːrdz flaɪ/</div>
                    <div class="text-gray-700">鸟儿飞翔。</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">复合谓语</div>
                    <div class="keyword text-lg mb-1">She can speak English.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi kæn spiːk ˈɪŋɡlɪʃ/</div>
                    <div class="text-gray-700">她会说英语。</div>
                </div>

                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">系动词+表语</div>
                    <div class="keyword text-lg mb-1">The weather is nice.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈweðər ɪz naɪs/</div>
                    <div class="text-gray-700">天气很好。</div>
                </div>
            </div>
        </section>

        <!-- 主语和谓语的识别方法 -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">主语和谓语的识别方法</h3>
            <p class="text-gray-700 mb-4">
                掌握正确的识别方法可以帮助我们快速找到句子的主语和谓语。
            </p>

            <h4 class="text-lg font-medium mb-3 text-gray-800">识别主语的方法</h4>
            <div class="mb-4">
                <p class="text-gray-700 mb-2">1. 问"谁"或"什么"在执行动作</p>
                <p class="text-gray-700 mb-2">2. 找到动词，然后问"谁/什么+动词"</p>
                <p class="text-gray-700 mb-4">3. 主语通常在句子开头（除倒装句外）</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">识别示例</div>
                    <div class="keyword text-lg mb-1">The students study hard.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈstuːdənts ˈstʌdi hɑːrd/</div>
                    <div class="text-gray-700">学生们努力学习。（谁在学习？学生们）</div>
                </div>

                <div class="card bg-lime-50 border border-lime-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">识别示例</div>
                    <div class="keyword text-lg mb-1">My favorite book is on the table.</div>
                    <div class="text-sm text-gray-600 mb-1">/maɪ ˈfeɪvərɪt bʊk ɪz ɑːn ðə ˈteɪbəl/</div>
                    <div class="text-gray-700">我最喜欢的书在桌子上。（什么在桌子上？书）</div>
                </div>
            </div>

            <h4 class="text-lg font-medium mb-3 text-gray-800">识别谓语的方法</h4>
            <div class="mb-4">
                <p class="text-gray-700 mb-2">1. 找到句子中的动词</p>
                <p class="text-gray-700 mb-2">2. 确定动词及其相关成分</p>
                <p class="text-gray-700 mb-4">3. 谓语包括动词和修饰动词的成分</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-rose-50 border border-rose-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">谓语识别</div>
                    <div class="keyword text-lg mb-1">She will visit her grandmother tomorrow.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi wɪl ˈvɪzɪt hər ˈɡrænmʌðər təˈmɑːroʊ/</div>
                    <div class="text-gray-700">她明天会去看望她的祖母。（谓语：will visit）</div>
                </div>

                <div class="card bg-amber-50 border border-amber-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">谓语识别</div>
                    <div class="keyword text-lg mb-1">The children are playing in the park.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈtʃɪldrən ɑːr ˈpleɪɪŋ ɪn ðə pɑːrk/</div>
                    <div class="text-gray-700">孩子们在公园里玩。（谓语：are playing）</div>
                </div>
            </div>
        </section>

        <!-- 基本句型结构 -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">基本句型结构</h3>
            <p class="text-gray-700 mb-4">
                英语的基本句型都是围绕主语和谓语构建的。掌握这些基本句型有助于更好地理解句子结构。
            </p>

            <h4 class="text-lg font-medium mb-3 text-gray-800">五种基本句型</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-violet-50 border border-violet-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">主+谓（S+V）</div>
                    <div class="keyword text-lg mb-1">Time flies.</div>
                    <div class="text-sm text-gray-600 mb-1">/taɪm flaɪz/</div>
                    <div class="text-gray-700">时光飞逝。</div>
                </div>

                <div class="card bg-sky-50 border border-sky-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">主+谓+宾（S+V+O）</div>
                    <div class="keyword text-lg mb-1">I love music.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ lʌv ˈmjuːzɪk/</div>
                    <div class="text-gray-700">我喜欢音乐。</div>
                </div>

                <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">主+系+表（S+V+P）</div>
                    <div class="keyword text-lg mb-1">She is beautiful.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi ɪz ˈbjuːtɪfəl/</div>
                    <div class="text-gray-700">她很美丽。</div>
                </div>

                <div class="card bg-fuchsia-50 border border-fuchsia-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">主+谓+间宾+直宾（S+V+IO+DO）</div>
                    <div class="keyword text-lg mb-1">He gave me a book.</div>
                    <div class="text-sm text-gray-600 mb-1">/hi ɡeɪv mi ə bʊk/</div>
                    <div class="text-gray-700">他给了我一本书。</div>
                </div>

                <div class="card bg-slate-50 border border-slate-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">主+谓+宾+宾补（S+V+O+C）</div>
                    <div class="keyword text-lg mb-1">We made him happy.</div>
                    <div class="text-sm text-gray-600 mb-1">/wi meɪd hɪm ˈhæpi/</div>
                    <div class="text-gray-700">我们让他开心。</div>
                </div>
            </div>
        </section>

        <!-- 特殊情况的主语和谓语 -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">特殊情况的主语和谓语</h3>
            <p class="text-gray-700 mb-4">
                在某些特殊句型中，主语和谓语的位置或形式可能与常规情况不同，需要特别注意。
            </p>

            <h4 class="text-lg font-medium mb-3 text-gray-800">倒装句</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">完全倒装</div>
                    <div class="keyword text-lg mb-1">Here comes the bus.</div>
                    <div class="text-sm text-gray-600 mb-1">/hɪr kʌmz ðə bʌs/</div>
                    <div class="text-gray-700">公交车来了。（主语：the bus）</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">部分倒装</div>
                    <div class="keyword text-lg mb-1">Never have I seen such beauty.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈnevər hæv aɪ siːn sʌtʃ ˈbjuːti/</div>
                    <div class="text-gray-700">我从未见过如此美景。（主语：I）</div>
                </div>
            </div>

            <h4 class="text-lg font-medium mb-3 text-gray-800">疑问句</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">一般疑问句</div>
                    <div class="keyword text-lg mb-1">Are you ready?</div>
                    <div class="text-sm text-gray-600 mb-1">/ɑːr ju ˈredi/</div>
                    <div class="text-gray-700">你准备好了吗？（主语：you）</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">特殊疑问句</div>
                    <div class="keyword text-lg mb-1">What did you do yesterday?</div>
                    <div class="text-sm text-gray-600 mb-1">/wʌt dɪd ju duː ˈjestərdeɪ/</div>
                    <div class="text-gray-700">你昨天做了什么？（主语：you）</div>
                </div>
            </div>

            <h4 class="text-lg font-medium mb-3 text-gray-800">祈使句</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">肯定祈使句</div>
                    <div class="keyword text-lg mb-1">Close the door.</div>
                    <div class="text-sm text-gray-600 mb-1">/kloʊz ðə dɔːr/</div>
                    <div class="text-gray-700">关门。（主语：you 省略）</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">否定祈使句</div>
                    <div class="keyword text-lg mb-1">Don't be late.</div>
                    <div class="text-sm text-gray-600 mb-1">/doʊnt bi leɪt/</div>
                    <div class="text-gray-700">别迟到。（主语：you 省略）</div>
                </div>
            </div>

            <h4 class="text-lg font-medium mb-3 text-gray-800">There be 句型</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">There be 单数</div>
                    <div class="keyword text-lg mb-1">There is a cat on the roof.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðer ɪz ə kæt ɑːn ðə ruːf/</div>
                    <div class="text-gray-700">屋顶上有一只猫。（主语：a cat）</div>
                </div>

                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">There be 复数</div>
                    <div class="keyword text-lg mb-1">There are many books on the shelf.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðer ɑːr ˈmeni bʊks ɑːn ðə ʃelf/</div>
                    <div class="text-gray-700">书架上有很多书。（主语：many books）</div>
                </div>
            </div>
        </section>

        <!-- 复合句中的主语和谓语 -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">复合句中的主语和谓语</h3>
            <p class="text-gray-700 mb-4">
                复合句包含多个分句，每个分句都有自己的主语和谓语。理解复合句的结构对于准确识别主语和谓语至关重要。
            </p>

            <h4 class="text-lg font-medium mb-3 text-gray-800">并列复合句</h4>
            <div class="grid grid-cols-1 gap-4 mb-6">
                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">并列句</div>
                    <div class="keyword text-lg mb-1">I like coffee, but she prefers tea.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ laɪk ˈkɔːfi bʌt ʃi prɪˈfɜːrz tiː/</div>
                    <div class="text-gray-700">我喜欢咖啡，但她更喜欢茶。（两个主谓结构：I like / she prefers）</div>
                </div>

                <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">并列句</div>
                    <div class="keyword text-lg mb-1">The sun was shining, and the birds were singing.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə sʌn wʌz ˈʃaɪnɪŋ ænd ðə bɜːrdz wər ˈsɪŋɪŋ/</div>
                    <div class="text-gray-700">阳光明媚，鸟儿在歌唱。（两个主谓结构）</div>
                </div>
            </div>

            <h4 class="text-lg font-medium mb-3 text-gray-800">主从复合句</h4>
            <div class="grid grid-cols-1 gap-4 mb-6">
                <div class="card bg-lime-50 border border-lime-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">宾语从句</div>
                    <div class="keyword text-lg mb-1">I know that he is coming.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ noʊ ðæt hi ɪz ˈkʌmɪŋ/</div>
                    <div class="text-gray-700">我知道他要来了。（主句：I know；从句：he is coming）</div>
                </div>

                <div class="card bg-rose-50 border border-rose-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">状语从句</div>
                    <div class="keyword text-lg mb-1">When it rains, we stay inside.</div>
                    <div class="text-sm text-gray-600 mb-1">/wen ɪt reɪnz wi steɪ ɪnˈsaɪd/</div>
                    <div class="text-gray-700">下雨时，我们待在室内。（从句：it rains；主句：we stay）</div>
                </div>

                <div class="card bg-amber-50 border border-amber-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">定语从句</div>
                    <div class="keyword text-lg mb-1">The book that I bought is interesting.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə bʊk ðæt aɪ bɔːt ɪz ˈɪntrəstɪŋ/</div>
                    <div class="text-gray-700">我买的那本书很有趣。（主句：The book is interesting；从句：I bought）</div>
                </div>
            </div>
        </section>

        <!-- 练习与应用 -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">练习与应用</h3>
            <p class="text-gray-700 mb-4">
                通过大量的练习来巩固主语和谓语的识别技能。以下是一些常见的练习句子。
            </p>

            <h4 class="text-lg font-medium mb-3 text-gray-800">基础练习句子</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-violet-50 border border-violet-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">练习句子</div>
                    <div class="keyword text-lg mb-1">My sister plays piano every day.</div>
                    <div class="text-sm text-gray-600 mb-1">/maɪ ˈsɪstər pleɪz piˈænoʊ ˈevri deɪ/</div>
                    <div class="text-gray-700">我姐姐每天弹钢琴。（主语：My sister；谓语：plays）</div>
                </div>

                <div class="card bg-sky-50 border border-sky-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">练习句子</div>
                    <div class="keyword text-lg mb-1">The old man walks slowly in the park.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə oʊld mæn wɔːks ˈsloʊli ɪn ðə pɑːrk/</div>
                    <div class="text-gray-700">老人在公园里慢慢地走。（主语：The old man；谓语：walks）</div>
                </div>

                <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">练习句子</div>
                    <div class="keyword text-lg mb-1">Reading books helps improve vocabulary.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈriːdɪŋ bʊks helps ɪmˈpruːv voʊˈkæbjəleri/</div>
                    <div class="text-gray-700">读书有助于提高词汇量。（主语：Reading books；谓语：helps）</div>
                </div>

                <div class="card bg-fuchsia-50 border border-fuchsia-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">练习句子</div>
                    <div class="keyword text-lg mb-1">What you said yesterday was very helpful.</div>
                    <div class="text-sm text-gray-600 mb-1">/wʌt ju sed ˈjestərdeɪ wʌz ˈveri ˈhelpfəl/</div>
                    <div class="text-gray-700">你昨天说的话很有帮助。（主语：What you said yesterday；谓语：was）</div>
                </div>
            </div>

            <h4 class="text-lg font-medium mb-3 text-gray-800">进阶练习句子</h4>
            <div class="grid grid-cols-1 gap-4 mb-6">
                <div class="card bg-slate-50 border border-slate-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">复杂句子</div>
                    <div class="keyword text-lg mb-1">The students who study hard will pass the exam easily.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈstuːdənts huː ˈstʌdi hɑːrd wɪl pæs ðə ɪɡˈzæm ˈiːzəli/</div>
                    <div class="text-gray-700">努力学习的学生会轻松通过考试。（主语：The students who study hard；谓语：will pass）</div>
                </div>

                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">复杂句子</div>
                    <div class="keyword text-lg mb-1">Although it was raining, we decided to go hiking.</div>
                    <div class="text-sm text-gray-600 mb-1">/ɔːlˈðoʊ ɪt wʌz ˈreɪnɪŋ wi dɪˈsaɪdəd tu ɡoʊ ˈhaɪkɪŋ/</div>
                    <div class="text-gray-700">尽管在下雨，我们还是决定去远足。（从句：it was raining；主句：we decided）</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">复杂句子</div>
                    <div class="keyword text-lg mb-1">Neither Tom nor his friends were at the party last night.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈniːðər tɑːm nɔːr hɪz frends wər æt ðə ˈpɑːrti læst naɪt/</div>
                    <div class="text-gray-700">汤姆和他的朋友们昨晚都没有参加聚会。（主语：Neither Tom nor his friends；谓语：were）</div>
                </div>
            </div>
        </section>

        <!-- 常见错误和注意事项 -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">常见错误和注意事项</h3>
            <p class="text-gray-700 mb-4">
                在识别主语和谓语时，学习者经常会犯一些常见错误。了解这些错误有助于避免类似问题。
            </p>

            <h4 class="text-lg font-medium mb-3 text-gray-800">常见错误类型</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">错误：混淆主语</div>
                    <div class="keyword text-lg mb-1">The books on the table are mine.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə bʊks ɑːn ðə ˈteɪbəl ɑːr maɪn/</div>
                    <div class="text-gray-700">桌子上的书是我的。（主语是books，不是table）</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">错误：忽略助动词</div>
                    <div class="keyword text-lg mb-1">She has been studying for hours.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi hæz bɪn ˈstʌdiɪŋ fɔːr ˈaʊərz/</div>
                    <div class="text-gray-700">她已经学习了几个小时。（完整谓语：has been studying）</div>
                </div>

                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">错误：倒装句识别</div>
                    <div class="keyword text-lg mb-1">Under the tree sits a little girl.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈʌndər ðə triː sɪts ə ˈlɪtəl ɡɜːrl/</div>
                    <div class="text-gray-700">树下坐着一个小女孩。（主语：a little girl，不是tree）</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">错误：复合主语</div>
                    <div class="keyword text-lg mb-1">Both Mary and John like ice cream.</div>
                    <div class="text-sm text-gray-600 mb-1">/boʊθ ˈmeri ænd dʒɑːn laɪk ˈaɪs kriːm/</div>
                    <div class="text-gray-700">玛丽和约翰都喜欢冰淇淋。（主语：Both Mary and John）</div>
                </div>
            </div>

            <h4 class="text-lg font-medium mb-3 text-gray-800">识别技巧总结</h4>
            <div class="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-6">
                <ul class="text-gray-700 space-y-2">
                    <li>• 先找动词，再确定主语</li>
                    <li>• 注意主语可能是短语或从句</li>
                    <li>• 谓语包括所有与动词相关的成分</li>
                    <li>• 倒装句中主语在谓语后面</li>
                    <li>• 复合句中每个分句都有主谓结构</li>
                    <li>• 祈使句的主语通常省略</li>
                    <li>• There be句型的主语在be动词后面</li>
                </ul>
            </div>
        </section>

        <!-- 主语的深入分析 -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">主语的深入分析</h3>
            <p class="text-gray-700 mb-4">
                主语是句子的核心成分之一，它不仅仅是执行动作的人或物，更是句子语义和语法结构的基础。深入理解主语的各种形式和特点，对于掌握英语语法至关重要。
            </p>

            <h4 class="text-lg font-medium mb-3 text-gray-800">主语的语法特征</h4>
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
                <ul class="text-gray-700 space-y-2">
                    <li>• <strong>位置特征：</strong>通常位于句首，但在倒装句中可能后置</li>
                    <li>• <strong>数的一致性：</strong>主语的单复数决定谓语动词的形式</li>
                    <li>• <strong>格的变化：</strong>在某些语言中主语有主格形式</li>
                    <li>• <strong>语义角色：</strong>可以是施事者、经历者、主题等</li>
                    <li>• <strong>省略特征：</strong>在特定情况下可以省略（如祈使句）</li>
                </ul>
            </div>

            <h4 class="text-lg font-medium mb-3 text-gray-800">形式主语与真实主语</h4>
            <p class="text-gray-700 mb-4">
                英语中经常使用形式主语（Formal Subject）来代替真实主语（Real Subject），这种结构使句子更加平衡和自然。
            </p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">It作形式主语</div>
                    <div class="keyword text-lg mb-1">It is important to study English.</div>
                    <div class="text-sm text-gray-600 mb-1">/ɪt ɪz ɪmˈpɔːrtənt tu ˈstʌdi ˈɪŋɡlɪʃ/</div>
                    <div class="text-gray-700">学英语很重要。（形式主语：It；真实主语：to study English）</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">It作形式主语</div>
                    <div class="keyword text-lg mb-1">It seems that he is busy.</div>
                    <div class="text-sm text-gray-600 mb-1">/ɪt siːmz ðæt hi ɪz ˈbɪzi/</div>
                    <div class="text-gray-700">看起来他很忙。（形式主语：It；真实主语：that he is busy）</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">There作引导词</div>
                    <div class="keyword text-lg mb-1">There exists a solution to this problem.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðer ɪɡˈzɪsts ə səˈluːʃən tu ðɪs ˈprɑːbləm/</div>
                    <div class="text-gray-700">这个问题存在解决方案。（There是引导词，主语：a solution）</div>
                </div>

                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">动名词短语主语</div>
                    <div class="keyword text-lg mb-1">Learning a foreign language takes time.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈlɜːrnɪŋ ə ˈfɔːrən ˈlæŋɡwɪdʒ teɪks taɪm/</div>
                    <div class="text-gray-700">学习外语需要时间。（主语：Learning a foreign language）</div>
                </div>
            </div>

            <h4 class="text-lg font-medium mb-3 text-gray-800">主语的语义分类</h4>
            <p class="text-gray-700 mb-4">
                从语义角度来看，主语可以承担不同的语义角色，这些角色反映了主语在句子中的实际功能。
            </p>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">施事者（Agent）</div>
                    <div class="keyword text-lg mb-1">The teacher explains the lesson.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈtiːtʃər ɪkˈspleɪnz ðə ˈlesən/</div>
                    <div class="text-gray-700">老师解释课程。（主语是动作的执行者）</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">经历者（Experiencer）</div>
                    <div class="keyword text-lg mb-1">John feels happy today.</div>
                    <div class="text-sm text-gray-600 mb-1">/dʒɑːn fiːlz ˈhæpi təˈdeɪ/</div>
                    <div class="text-gray-700">约翰今天感到高兴。（主语体验情感状态）</div>
                </div>

                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">主题（Theme）</div>
                    <div class="keyword text-lg mb-1">The book is on the table.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə bʊk ɪz ɑːn ðə ˈteɪbəl/</div>
                    <div class="text-gray-700">书在桌子上。（主语是被描述的对象）</div>
                </div>

                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">受事者（Patient）</div>
                    <div class="keyword text-lg mb-1">The window was broken by the ball.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈwɪndoʊ wʌz ˈbroʊkən baɪ ðə bɔːl/</div>
                    <div class="text-gray-700">窗户被球打破了。（主语是动作的承受者）</div>
                </div>

                <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">工具（Instrument）</div>
                    <div class="keyword text-lg mb-1">The key opens the door.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə kiː ˈoʊpənz ðə dɔːr/</div>
                    <div class="text-gray-700">钥匙打开门。（主语是完成动作的工具）</div>
                </div>

                <div class="card bg-lime-50 border border-lime-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">原因（Cause）</div>
                    <div class="keyword text-lg mb-1">The storm caused the power outage.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə stɔːrm kɔːzd ðə ˈpaʊər ˈaʊtɪdʒ/</div>
                    <div class="text-gray-700">暴风雨导致停电。（主语是事件的原因）</div>
                </div>
            </div>
        </section>

        <!-- 谓语的深入分析 -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">谓语的深入分析</h3>
            <p class="text-gray-700 mb-4">
                谓语是句子的核心，它不仅表达动作或状态，还承载着时态、语态、语气等重要语法信息。深入理解谓语的构成和功能是掌握英语语法的关键。
            </p>

            <h4 class="text-lg font-medium mb-3 text-gray-800">谓语的构成要素</h4>
            <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                <ul class="text-gray-700 space-y-2">
                    <li>• <strong>主要动词（Main Verb）：</strong>表达主要动作或状态的动词</li>
                    <li>• <strong>助动词（Auxiliary Verb）：</strong>帮助构成时态、语态、语气的动词</li>
                    <li>• <strong>情态动词（Modal Verb）：</strong>表达可能性、必要性、意愿等的动词</li>
                    <li>• <strong>系动词（Linking Verb）：</strong>连接主语和表语的动词</li>
                    <li>• <strong>动词短语（Phrasal Verb）：</strong>动词与介词或副词组成的短语</li>
                </ul>
            </div>

            <h4 class="text-lg font-medium mb-3 text-gray-800">谓语的时态系统</h4>
            <p class="text-gray-700 mb-4">
                英语的时态系统通过谓语动词的变化来表达动作发生的时间和状态。理解时态是识别和分析谓语的重要方面。
            </p>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">一般现在时</div>
                    <div class="keyword text-lg mb-1">She works in a hospital.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi wɜːrks ɪn ə ˈhɑːspɪtəl/</div>
                    <div class="text-gray-700">她在医院工作。（谓语：works）</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">现在进行时</div>
                    <div class="keyword text-lg mb-1">They are studying English.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðeɪ ɑːr ˈstʌdiɪŋ ˈɪŋɡlɪʃ/</div>
                    <div class="text-gray-700">他们正在学英语。（谓语：are studying）</div>
                </div>

                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">现在完成时</div>
                    <div class="keyword text-lg mb-1">I have finished my homework.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ hæv ˈfɪnɪʃt maɪ ˈhoʊmwɜːrk/</div>
                    <div class="text-gray-700">我已经完成了作业。（谓语：have finished）</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">一般过去时</div>
                    <div class="keyword text-lg mb-1">He visited Paris last year.</div>
                    <div class="text-sm text-gray-600 mb-1">/hi ˈvɪzɪtəd ˈpærɪs læst jɪr/</div>
                    <div class="text-gray-700">他去年访问了巴黎。（谓语：visited）</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">过去进行时</div>
                    <div class="keyword text-lg mb-1">We were watching TV at 8 PM.</div>
                    <div class="text-sm text-gray-600 mb-1">/wi wər ˈwɑːtʃɪŋ ˌtiː ˈviː æt eɪt ˌpiː ˈem/</div>
                    <div class="text-gray-700">晚上8点我们在看电视。（谓语：were watching）</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">将来时</div>
                    <div class="keyword text-lg mb-1">She will graduate next month.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi wɪl ˈɡrædʒueɪt nekst mʌnθ/</div>
                    <div class="text-gray-700">她下个月毕业。（谓语：will graduate）</div>
                </div>
            </div>

            <h4 class="text-lg font-medium mb-3 text-gray-800">谓语的语态变化</h4>
            <p class="text-gray-700 mb-4">
                英语中的语态（Voice）通过谓语动词的形式变化来表达主语与动作之间的关系，主要包括主动语态和被动语态。
            </p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">主动语态</div>
                    <div class="keyword text-lg mb-1">The chef prepares delicious meals.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ʃef prɪˈperz dɪˈlɪʃəs miːlz/</div>
                    <div class="text-gray-700">厨师准备美味的饭菜。（主语执行动作）</div>
                </div>

                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">被动语态</div>
                    <div class="keyword text-lg mb-1">Delicious meals are prepared by the chef.</div>
                    <div class="text-sm text-gray-600 mb-1">/dɪˈlɪʃəs miːlz ɑːr prɪˈperd baɪ ðə ʃef/</div>
                    <div class="text-gray-700">美味的饭菜由厨师准备。（主语承受动作）</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">被动语态（完成时）</div>
                    <div class="keyword text-lg mb-1">The project has been completed.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈprɑːdʒekt hæz bɪn kəmˈpliːtəd/</div>
                    <div class="text-gray-700">项目已经完成了。（谓语：has been completed）</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">被动语态（进行时）</div>
                    <div class="keyword text-lg mb-1">The house is being painted.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə haʊs ɪz ˈbiːɪŋ ˈpeɪntəd/</div>
                    <div class="text-gray-700">房子正在被粉刷。（谓语：is being painted）</div>
                </div>
            </div>
        </section>

        <!-- 主谓一致性原则 -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">主谓一致性原则</h3>
            <p class="text-gray-700 mb-4">
                主谓一致（Subject-Verb Agreement）是英语语法的基本原则之一，要求谓语动词在人称和数上与主语保持一致。掌握这一原则对于正确使用英语至关重要。
            </p>

            <h4 class="text-lg font-medium mb-3 text-gray-800">基本一致性规则</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">单数主语</div>
                    <div class="keyword text-lg mb-1">The cat sleeps on the sofa.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə kæt sliːps ɑːn ðə ˈsoʊfə/</div>
                    <div class="text-gray-700">猫在沙发上睡觉。（单数主语+单数动词）</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">复数主语</div>
                    <div class="keyword text-lg mb-1">The cats sleep on the sofa.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə kæts sliːp ɑːn ðə ˈsoʊfə/</div>
                    <div class="text-gray-700">猫们在沙发上睡觉。（复数主语+复数动词）</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">第三人称单数</div>
                    <div class="keyword text-lg mb-1">She studies every evening.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi ˈstʌdiz ˈevri ˈiːvnɪŋ/</div>
                    <div class="text-gray-700">她每晚学习。（第三人称单数+动词加s）</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">第一、二人称</div>
                    <div class="keyword text-lg mb-1">I/You study every evening.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ/ju ˈstʌdi ˈevri ˈiːvnɪŋ/</div>
                    <div class="text-gray-700">我/你每晚学习。（动词原形）</div>
                </div>
            </div>

            <h4 class="text-lg font-medium mb-3 text-gray-800">特殊情况的主谓一致</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">集体名词</div>
                    <div class="keyword text-lg mb-1">The team is/are playing well.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə tiːm ɪz/ɑːr ˈpleɪɪŋ wel/</div>
                    <div class="text-gray-700">团队表现很好。（整体概念用单数，个体概念用复数）</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">不定代词</div>
                    <div class="keyword text-lg mb-1">Everyone is here.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈevriˌwʌn ɪz hɪr/</div>
                    <div class="text-gray-700">每个人都在这里。（不定代词通常用单数）</div>
                </div>

                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">分数/百分比</div>
                    <div class="keyword text-lg mb-1">Half of the students are absent.</div>
                    <div class="text-sm text-gray-600 mb-1">/hæf ʌv ðə ˈstuːdənts ɑːr ˈæbsənt/</div>
                    <div class="text-gray-700">一半的学生缺席。（根据of后面的名词决定）</div>
                </div>

                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">就近原则</div>
                    <div class="keyword text-lg mb-1">Either Tom or his friends are coming.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈiːðər tɑːm ɔːr hɪz frends ɑːr ˈkʌmɪŋ/</div>
                    <div class="text-gray-700">汤姆或他的朋友们要来。（动词与最近的主语一致）</div>
                </div>
            </div>
        </section>

        <!-- 语序变化中的主谓识别 -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">语序变化中的主谓识别</h3>
            <p class="text-gray-700 mb-4">
                英语虽然基本语序是SVO（主-谓-宾），但在某些情况下会出现语序变化。理解这些变化对于准确识别主语和谓语非常重要。
            </p>

            <h4 class="text-lg font-medium mb-3 text-gray-800">完全倒装结构</h4>
            <p class="text-gray-700 mb-4">
                完全倒装是指谓语动词完全位于主语之前的结构，常见于以地点副词、方位副词开头的句子。
            </p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">地点副词倒装</div>
                    <div class="keyword text-lg mb-1">In the garden stands a beautiful tree.</div>
                    <div class="text-sm text-gray-600 mb-1">/ɪn ðə ˈɡɑːrdən stændz ə ˈbjuːtɪfəl triː/</div>
                    <div class="text-gray-700">花园里矗立着一棵美丽的树。（主语：a beautiful tree）</div>
                </div>

                <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">方位副词倒装</div>
                    <div class="keyword text-lg mb-1">Up flew the bird into the sky.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʌp fluː ðə bɜːrd ˈɪntu ðə skaɪ/</div>
                    <div class="text-gray-700">鸟儿飞向天空。（主语：the bird）</div>
                </div>

                <div class="card bg-lime-50 border border-lime-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">Here/There倒装</div>
                    <div class="keyword text-lg mb-1">There goes the last bus.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðer ɡoʊz ðə læst bʌs/</div>
                    <div class="text-gray-700">最后一班公交车走了。（主语：the last bus）</div>
                </div>

                <div class="card bg-rose-50 border border-rose-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">表语前置倒装</div>
                    <div class="keyword text-lg mb-1">Gone are the days of our youth.</div>
                    <div class="text-sm text-gray-600 mb-1">/ɡɔːn ɑːr ðə deɪz ʌv ˈaʊər juːθ/</div>
                    <div class="text-gray-700">我们青春的日子一去不复返了。（主语：the days of our youth）</div>
                </div>
            </div>

            <h4 class="text-lg font-medium mb-3 text-gray-800">部分倒装结构</h4>
            <p class="text-gray-700 mb-4">
                部分倒装是指只有助动词或情态动词位于主语之前，主要动词仍在主语之后的结构。
            </p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-amber-50 border border-amber-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">否定副词倒装</div>
                    <div class="keyword text-lg mb-1">Never have I seen such beauty.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈnevər hæv aɪ siːn sʌtʃ ˈbjuːti/</div>
                    <div class="text-gray-700">我从未见过如此美景。（主语：I；谓语：have seen）</div>
                </div>

                <div class="card bg-violet-50 border border-violet-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">Only倒装</div>
                    <div class="keyword text-lg mb-1">Only then did he realize his mistake.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈoʊnli ðen dɪd hi ˈriəˌlaɪz hɪz mɪˈsteɪk/</div>
                    <div class="text-gray-700">只有那时他才意识到自己的错误。（主语：he；谓语：did realize）</div>
                </div>

                <div class="card bg-sky-50 border border-sky-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">So/Such倒装</div>
                    <div class="keyword text-lg mb-1">So tired was she that she fell asleep.</div>
                    <div class="text-sm text-gray-600 mb-1">/soʊ ˈtaɪərd wʌz ʃi ðæt ʃi fel əˈsliːp/</div>
                    <div class="text-gray-700">她太累了，以至于睡着了。（主语：she；谓语：was）</div>
                </div>

                <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">虚拟语气倒装</div>
                    <div class="keyword text-lg mb-1">Had I known earlier, I would have helped.</div>
                    <div class="text-sm text-gray-600 mb-1">/hæd aɪ noʊn ˈɜːrliər aɪ wʊd hæv helpt/</div>
                    <div class="text-gray-700">如果我早知道，我会帮忙的。（主语：I；谓语：had known）</div>
                </div>
            </div>
        </section>

        <!-- 复杂句型中的主谓分析 -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">复杂句型中的主谓分析</h3>
            <p class="text-gray-700 mb-4">
                在复杂的句型结构中，主语和谓语的识别需要更加仔细的分析。这些结构包括嵌套从句、并列结构、省略结构等。
            </p>

            <h4 class="text-lg font-medium mb-3 text-gray-800">嵌套从句结构</h4>
            <p class="text-gray-700 mb-4">
                嵌套从句是指从句内部还包含其他从句的复杂结构，每个层次都有自己的主谓关系。
            </p>

            <div class="grid grid-cols-1 gap-4 mb-6">
                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">多层嵌套</div>
                    <div class="keyword text-lg mb-1">I believe that the book that you recommended is excellent.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ bɪˈliːv ðæt ðə bʊk ðæt ju ˌrekəˈmendəd ɪz ˈeksələnt/</div>
                    <div class="text-gray-700">我相信你推荐的那本书很棒。</div>
                    <div class="text-sm text-gray-600 mt-2">
                        主句：I believe | 宾语从句：the book is excellent | 定语从句：you recommended
                    </div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">复合嵌套</div>
                    <div class="keyword text-lg mb-1">What he said that surprised me was completely true.</div>
                    <div class="text-sm text-gray-600 mb-1">/wʌt hi sed ðæt sərˈpraɪzd mi wʌz kəmˈpliːtli truː/</div>
                    <div class="text-gray-700">他说的让我惊讶的话完全是真的。</div>
                    <div class="text-sm text-gray-600 mt-2">
                        主语从句：What he said that surprised me | 谓语：was | 表语：completely true
                    </div>
                </div>
            </div>

            <h4 class="text-lg font-medium mb-3 text-gray-800">并列结构分析</h4>
            <p class="text-gray-700 mb-4">
                并列结构中可能包含并列的主语、谓语或整个句子，需要准确识别每个并列成分的主谓关系。
            </p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">并列主语</div>
                    <div class="keyword text-lg mb-1">Both the teacher and the students were excited.</div>
                    <div class="text-sm text-gray-600 mb-1">/boʊθ ðə ˈtiːtʃər ænd ðə ˈstuːdənts wər ɪkˈsaɪtəd/</div>
                    <div class="text-gray-700">老师和学生们都很兴奋。（并列主语：the teacher and the students）</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">并列谓语</div>
                    <div class="keyword text-lg mb-1">She studied hard and passed the exam.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi ˈstʌdid hɑːrd ænd pæst ðə ɪɡˈzæm/</div>
                    <div class="text-gray-700">她努力学习并通过了考试。（并列谓语：studied and passed）</div>
                </div>

                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">并列句子</div>
                    <div class="keyword text-lg mb-1">The sun was shining, the birds were singing, and everyone was happy.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə sʌn wʌz ˈʃaɪnɪŋ ðə bɜːrdz wər ˈsɪŋɪŋ ænd ˈevriˌwʌn wʌz ˈhæpi/</div>
                    <div class="text-gray-700">阳光明媚，鸟儿歌唱，每个人都很快乐。（三个并列的主谓结构）</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">省略结构</div>
                    <div class="keyword text-lg mb-1">John likes coffee, and Mary tea.</div>
                    <div class="text-sm text-gray-600 mb-1">/dʒɑːn laɪks ˈkɔːfi ænd ˈmeri tiː/</div>
                    <div class="text-gray-700">约翰喜欢咖啡，玛丽喜欢茶。（第二个分句省略了likes）</div>
                </div>
            </div>
        </section>

        <!-- 实用应用场景 -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">实用应用场景</h3>
            <p class="text-gray-700 mb-4">
                掌握主语和谓语的识别在日常英语学习和使用中有很多实际应用，从基础的语法学习到高级的语言分析都离不开这一技能。
            </p>

            <h4 class="text-lg font-medium mb-3 text-gray-800">学术写作中的应用</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">句子结构分析</div>
                    <div class="keyword text-lg mb-1">Sentence Structure Analysis</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈsentəns ˈstrʌktʃər əˈnæləsɪs/</div>
                    <div class="text-gray-700">在学术写作中，准确识别主谓结构有助于构建清晰、逻辑性强的句子。</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">语法检查</div>
                    <div class="keyword text-lg mb-1">Grammar Checking</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈɡræmər ˈtʃekɪŋ/</div>
                    <div class="text-gray-700">通过主谓识别可以发现并纠正主谓不一致等常见语法错误。</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">句式变换</div>
                    <div class="keyword text-lg mb-1">Sentence Transformation</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈsentəns ˌtrænsfərˈmeɪʃən/</div>
                    <div class="text-gray-700">理解主谓关系有助于进行主动语态与被动语态之间的转换。</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">修辞效果</div>
                    <div class="keyword text-lg mb-1">Rhetorical Effects</div>
                    <div class="text-sm text-gray-600 mb-1">/rɪˈtɔːrɪkəl ɪˈfekts/</div>
                    <div class="text-gray-700">通过调整主谓结构可以创造不同的修辞效果和语言风格。</div>
                </div>
            </div>

            <h4 class="text-lg font-medium mb-3 text-gray-800">阅读理解中的应用</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">快速理解</div>
                    <div class="keyword text-lg mb-1">Rapid Comprehension</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈræpɪd ˌkɑːmprɪˈhenʃən/</div>
                    <div class="text-gray-700">快速识别主谓结构有助于抓住句子的核心意思，提高阅读效率。</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">长难句分析</div>
                    <div class="keyword text-lg mb-1">Complex Sentence Analysis</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈkɑːmpleks ˈsentəns əˈnæləsɪs/</div>
                    <div class="text-gray-700">在面对复杂长句时，主谓识别是理解句子结构的关键步骤。</div>
                </div>

                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">语境推断</div>
                    <div class="keyword text-lg mb-1">Context Inference</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈkɑːntekst ˈɪnfərəns/</div>
                    <div class="text-gray-700">通过分析主谓关系可以更好地推断词汇含义和语境信息。</div>
                </div>

                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">文本分析</div>
                    <div class="keyword text-lg mb-1">Text Analysis</div>
                    <div class="text-sm text-gray-600 mb-1">/tekst əˈnæləsɪs/</div>
                    <div class="text-gray-700">主谓结构分析是进行深层文本分析和文学批评的基础工具。</div>
                </div>
            </div>

            <h4 class="text-lg font-medium mb-3 text-gray-800">口语交流中的应用</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">流利表达</div>
                    <div class="keyword text-lg mb-1">Fluent Expression</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈfluːənt ɪkˈspreʃən/</div>
                    <div class="text-gray-700">掌握主谓结构有助于组织语言，使口语表达更加流利自然。</div>
                </div>

                <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">语法准确性</div>
                    <div class="keyword text-lg mb-1">Grammatical Accuracy</div>
                    <div class="text-sm text-gray-600 mb-1">/ɡrəˈmætɪkəl ˈækjərəsi/</div>
                    <div class="text-gray-700">在口语中保持主谓一致可以提高语言的准确性和专业性。</div>
                </div>

                <div class="card bg-lime-50 border border-lime-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">听力理解</div>
                    <div class="keyword text-lg mb-1">Listening Comprehension</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈlɪsənɪŋ ˌkɑːmprɪˈhenʃən/</div>
                    <div class="text-gray-700">识别主谓结构有助于在听力过程中快速抓住关键信息。</div>
                </div>

                <div class="card bg-rose-50 border border-rose-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">交际策略</div>
                    <div class="keyword text-lg mb-1">Communication Strategy</div>
                    <div class="text-sm text-gray-600 mb-1">/kəˌmjuːnɪˈkeɪʃən ˈstrætədʒi/</div>
                    <div class="text-gray-700">在交流中灵活运用不同的主谓结构可以增强表达效果。</div>
                </div>
            </div>
        </section>

        <!-- 学习建议和总结 -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-4 text-gray-800">学习建议和总结</h3>
            <p class="text-gray-700 mb-4">
                主语和谓语的识别是英语语法学习的基础，需要通过系统的学习和大量的练习来掌握。以下是一些有效的学习建议。
            </p>

            <h4 class="text-lg font-medium mb-3 text-gray-800">学习策略</h4>
            <div class="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6 mb-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h5 class="font-semibold text-gray-800 mb-3">基础阶段</h5>
                        <ul class="text-gray-700 space-y-2">
                            <li>• 从简单句开始，逐步过渡到复合句</li>
                            <li>• 大量练习基本句型的主谓识别</li>
                            <li>• 熟记常见的主语和谓语形式</li>
                            <li>• 注意主谓一致性规则的应用</li>
                        </ul>
                    </div>
                    <div>
                        <h5 class="font-semibold text-gray-800 mb-3">进阶阶段</h5>
                        <ul class="text-gray-700 space-y-2">
                            <li>• 学习特殊句型中的主谓识别</li>
                            <li>• 掌握倒装句的主谓关系</li>
                            <li>• 分析复杂句型的嵌套结构</li>
                            <li>• 理解语境对主谓关系的影响</li>
                        </ul>
                    </div>
                </div>
            </div>

            <h4 class="text-lg font-medium mb-3 text-gray-800">实践方法</h4>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">句子分析</div>
                    <div class="keyword text-lg mb-1">Sentence Parsing</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈsentəns ˈpɑːrsɪŋ/</div>
                    <div class="text-gray-700">定期分析不同类型的句子，标出主语和谓语成分。</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">造句练习</div>
                    <div class="keyword text-lg mb-1">Sentence Construction</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈsentəns kənˈstrʌkʃən/</div>
                    <div class="text-gray-700">主动构造包含不同主谓结构的句子，加深理解。</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">阅读实践</div>
                    <div class="keyword text-lg mb-1">Reading Practice</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈriːdɪŋ ˈpræktɪs/</div>
                    <div class="text-gray-700">在阅读过程中有意识地识别主谓结构，提高敏感度。</div>
                </div>
            </div>

            <h4 class="text-lg font-medium mb-3 text-gray-800">关键要点总结</h4>
            <div class="bg-gray-50 border border-gray-200 rounded-lg p-6 mb-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h5 class="font-semibold text-gray-800 mb-3">主语识别要点</h5>
                        <ul class="text-gray-700 space-y-1 text-sm">
                            <li>• 主语是句子描述的对象</li>
                            <li>• 可以是名词、代词、短语或从句</li>
                            <li>• 通常位于句首，但可能因倒装而后置</li>
                            <li>• 决定谓语动词的人称和数</li>
                            <li>• 在祈使句中通常省略</li>
                            <li>• 注意区分形式主语和真实主语</li>
                        </ul>
                    </div>
                    <div>
                        <h5 class="font-semibold text-gray-800 mb-3">谓语识别要点</h5>
                        <ul class="text-gray-700 space-y-1 text-sm">
                            <li>• 谓语表达动作或状态</li>
                            <li>• 包含主要动词和辅助成分</li>
                            <li>• 承载时态、语态、语气信息</li>
                            <li>• 必须与主语在人称和数上一致</li>
                            <li>• 可能包含助动词、情态动词等</li>
                            <li>• 在倒装句中位置可能发生变化</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <p class="text-gray-700 text-center font-medium">
                    掌握主语和谓语的识别是英语语法学习的基石，通过系统学习和持续练习，
                    你将能够准确分析各种复杂的句子结构，为进一步的英语学习打下坚实基础。
                </p>
            </div>
        </section>
    </div>
</body>
</html>