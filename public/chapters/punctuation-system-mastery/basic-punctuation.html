<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>基础标点符号</title>
    <script src="/static/tailwindcss-3.4.17.js"></script>
    <style>
        .keyword {
            color: #3d74ed;
            font-weight: bold;
        }
    </style>
</head>
<body class="bg-white">
    <div class="p-6">
        <!-- 句号 (Period) 部分 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">句号 (Period) - 句子的终结符</h2>
            <p class="text-gray-700 mb-6">句号是英语中最基本的标点符号，用于标示陈述句的结束。它表示一个完整思想的结束，是句子结构中不可缺少的组成部分。</p>
            
            <h3 class="text-xl font-semibold mb-4 text-gray-800">基本用法</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">简单陈述句</div>
                    <div class="keyword text-lg mb-1">I like coffee.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ laɪk ˈkɔːfi/</div>
                    <div class="text-gray-700">我喜欢咖啡。</div>
                </div>
                
                <div class="card bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">事实陈述</div>
                    <div class="keyword text-lg mb-1">The sun rises in the east.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə sʌn ˈraɪzɪz ɪn ðə iːst/</div>
                    <div class="text-gray-700">太阳从东方升起。</div>
                </div>
                
                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">描述性句子</div>
                    <div class="keyword text-lg mb-1">She has long black hair.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi hæz lɔːŋ blæk her/</div>
                    <div class="text-gray-700">她有一头长长的黑发。</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">复合句中的句号使用</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">并列复合句</div>
                    <div class="keyword text-lg mb-1">I went to the store, and I bought some milk.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ went tu ðə stɔːr ænd aɪ bɔːt sʌm mɪlk/</div>
                    <div class="text-gray-700">我去了商店，买了一些牛奶。</div>
                </div>
                
                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">主从复合句</div>
                    <div class="keyword text-lg mb-1">When it rains, I stay inside.</div>
                    <div class="text-sm text-gray-600 mb-1">/wen ɪt reɪnz aɪ steɪ ɪnˈsaɪd/</div>
                    <div class="text-gray-700">下雨时，我待在室内。</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">缩写词后的句号</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-4 mb-6">
                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">先生</div>
                    <div class="keyword text-lg mb-1">Mr.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈmɪstər/</div>
                    <div class="text-gray-700">先生</div>
                </div>
                
                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">女士</div>
                    <div class="keyword text-lg mb-1">Mrs.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈmɪsɪz/</div>
                    <div class="text-gray-700">夫人</div>
                </div>
                
                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">博士</div>
                    <div class="keyword text-lg mb-1">Dr.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈdɑːktər/</div>
                    <div class="text-gray-700">博士</div>
                </div>
                
                <div class="card bg-red-50 border border-red-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">等等</div>
                    <div class="keyword text-lg mb-1">etc.</div>
                    <div class="text-sm text-gray-600 mb-1">/ɪtˈsetərə/</div>
                    <div class="text-gray-700">等等</div>
                </div>
            </div>
        </section>

        <!-- 逗号 (Comma) 部分 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">逗号 (Comma) - 句子的分隔符</h2>
            <p class="text-gray-700 mb-6">逗号是英语中使用频率最高的标点符号之一，主要用于分隔句子中的不同成分，使句子结构更加清晰，便于理解。</p>
            
            <h3 class="text-xl font-semibold mb-4 text-gray-800">列举项目时的逗号</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">三个项目列举</div>
                    <div class="keyword text-lg mb-1">I need apples, bananas, and oranges.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ niːd ˈæpəlz bəˈnænəz ænd ˈɔːrɪndʒɪz/</div>
                    <div class="text-gray-700">我需要苹果、香蕉和橙子。</div>
                </div>
                
                <div class="card bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">形容词列举</div>
                    <div class="keyword text-lg mb-1">She is smart, kind, and beautiful.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi ɪz smɑːrt kaɪnd ænd ˈbjuːtɪfəl/</div>
                    <div class="text-gray-700">她聪明、善良又美丽。</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">连接独立子句</h3>
            <div class="grid grid-cols-1 gap-4 mb-6">
                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">并列连词前的逗号</div>
                    <div class="keyword text-lg mb-1">I wanted to go to the movies, but I had to work.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ ˈwɑːntɪd tu ɡoʊ tu ðə ˈmuːviz bʌt aɪ hæd tu wɜːrk/</div>
                    <div class="text-gray-700">我想去看电影，但我必须工作。</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">转折关系</div>
                    <div class="keyword text-lg mb-1">The weather was cold, yet we decided to go hiking.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈweðər wʌz koʊld jet wi dɪˈsaɪdɪd tu ɡoʊ ˈhaɪkɪŋ/</div>
                    <div class="text-gray-700">天气很冷，但我们还是决定去远足。</div>
                </div>

                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">因果关系</div>
                    <div class="keyword text-lg mb-1">It was raining heavily, so we stayed indoors.</div>
                    <div class="text-sm text-gray-600 mb-1">/ɪt wʌz ˈreɪnɪŋ ˈhevɪli soʊ wi steɪd ˈɪndɔːrz/</div>
                    <div class="text-gray-700">雨下得很大，所以我们待在室内。</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">分隔从句</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">时间状语从句</div>
                    <div class="keyword text-lg mb-1">When I was young, I loved to read.</div>
                    <div class="text-sm text-gray-600 mb-1">/wen aɪ wʌz jʌŋ aɪ lʌvd tu riːd/</div>
                    <div class="text-gray-700">当我年轻的时候，我喜欢读书。</div>
                </div>

                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">条件状语从句</div>
                    <div class="keyword text-lg mb-1">If you study hard, you will succeed.</div>
                    <div class="text-sm text-gray-600 mb-1">/ɪf ju ˈstʌdi hɑːrd ju wɪl səkˈsiːd/</div>
                    <div class="text-gray-700">如果你努力学习，你会成功的。</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">原因状语从句</div>
                    <div class="keyword text-lg mb-1">Because it was late, we went home.</div>
                    <div class="text-sm text-gray-600 mb-1">/bɪˈkɔːz ɪt wʌz leɪt wi went hoʊm/</div>
                    <div class="text-gray-700">因为很晚了，我们回家了。</div>
                </div>

                <div class="card bg-red-50 border border-red-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">让步状语从句</div>
                    <div class="keyword text-lg mb-1">Although it was difficult, I finished the task.</div>
                    <div class="text-sm text-gray-600 mb-1">/ɔːlˈðoʊ ɪt wʌz ˈdɪfɪkəlt aɪ ˈfɪnɪʃt ðə tæsk/</div>
                    <div class="text-gray-700">虽然很困难，但我完成了任务。</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">分隔插入语</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">插入性说明</div>
                    <div class="keyword text-lg mb-1">My brother, who lives in New York, is a doctor.</div>
                    <div class="text-sm text-gray-600 mb-1">/maɪ ˈbrʌðər hu lɪvz ɪn nu jɔːrk ɪz ə ˈdɑːktər/</div>
                    <div class="text-gray-700">我住在纽约的哥哥是一名医生。</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">补充信息</div>
                    <div class="keyword text-lg mb-1">The book, published in 2020, became a bestseller.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə bʊk ˈpʌblɪʃt ɪn ˈtwenti ˈtwenti bɪˈkeɪm ə ˈbestˌselər/</div>
                    <div class="text-gray-700">这本2020年出版的书成为了畅销书。</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">地址和日期中的逗号</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">完整地址</div>
                    <div class="keyword text-lg mb-1">123 Main Street, New York, NY 10001</div>
                    <div class="text-sm text-gray-600 mb-1">/wʌn tu θri meɪn striːt nu jɔːrk/</div>
                    <div class="text-gray-700">纽约州纽约市主街123号</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">日期格式</div>
                    <div class="keyword text-lg mb-1">December 25, 2023</div>
                    <div class="text-sm text-gray-600 mb-1">/dɪˈsembər ˈtwenti faɪf ˈtwenti ˈtwenti θri/</div>
                    <div class="text-gray-700">2023年12月25日</div>
                </div>

                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">城市和州</div>
                    <div class="keyword text-lg mb-1">Los Angeles, California</div>
                    <div class="text-sm text-gray-600 mb-1">/lɔːs ˈændʒələs kælɪˈfɔːrnjə/</div>
                    <div class="text-gray-700">加利福尼亚州洛杉矶</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">称呼和问候语</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">直接称呼</div>
                    <div class="keyword text-lg mb-1">Hello, John.</div>
                    <div class="text-sm text-gray-600 mb-1">/həˈloʊ dʒɑːn/</div>
                    <div class="text-gray-700">你好，约翰。</div>
                </div>

                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">句中称呼</div>
                    <div class="keyword text-lg mb-1">Thank you, Mary, for your help.</div>
                    <div class="text-sm text-gray-600 mb-1">/θæŋk ju ˈmeri fɔːr jʊr help/</div>
                    <div class="text-gray-700">谢谢你的帮助，玛丽。</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">句末称呼</div>
                    <div class="keyword text-lg mb-1">See you later, Tom.</div>
                    <div class="text-sm text-gray-600 mb-1">/si ju ˈleɪtər tɑːm/</div>
                    <div class="text-gray-700">回头见，汤姆。</div>
                </div>

                <div class="card bg-red-50 border border-red-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">正式称呼</div>
                    <div class="keyword text-lg mb-1">Good morning, Dr. Smith.</div>
                    <div class="text-sm text-gray-600 mb-1">/ɡʊd ˈmɔːrnɪŋ ˈdɑːktər smɪθ/</div>
                    <div class="text-gray-700">早上好，史密斯博士。</div>
                </div>
            </div>
        </section>

        <!-- 问号 (Question Mark) 部分 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">问号 (Question Mark) - 疑问的标志</h2>
            <p class="text-gray-700 mb-6">问号用于标示疑问句的结束，表示说话者正在询问信息、寻求确认或表达疑惑。问号是疑问句不可缺少的标点符号。</p>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">一般疑问句</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">be动词疑问句</div>
                    <div class="keyword text-lg mb-1">Are you ready?</div>
                    <div class="text-sm text-gray-600 mb-1">/ɑːr ju ˈredi/</div>
                    <div class="text-gray-700">你准备好了吗？</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">助动词疑问句</div>
                    <div class="keyword text-lg mb-1">Do you like music?</div>
                    <div class="text-sm text-gray-600 mb-1">/du ju laɪk ˈmjuzɪk/</div>
                    <div class="text-gray-700">你喜欢音乐吗？</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">情态动词疑问句</div>
                    <div class="keyword text-lg mb-1">Can you swim?</div>
                    <div class="text-sm text-gray-600 mb-1">/kæn ju swɪm/</div>
                    <div class="text-gray-700">你会游泳吗？</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">现在完成时疑问句</div>
                    <div class="keyword text-lg mb-1">Have you finished your homework?</div>
                    <div class="text-sm text-gray-600 mb-1">/hæv ju ˈfɪnɪʃt jʊr ˈhoʊmwɜːrk/</div>
                    <div class="text-gray-700">你完成作业了吗？</div>
                </div>

                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">过去时疑问句</div>
                    <div class="keyword text-lg mb-1">Did you see the movie?</div>
                    <div class="text-sm text-gray-600 mb-1">/dɪd ju si ðə ˈmuːvi/</div>
                    <div class="text-gray-700">你看了那部电影吗？</div>
                </div>

                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">将来时疑问句</div>
                    <div class="keyword text-lg mb-1">Will you come tomorrow?</div>
                    <div class="text-sm text-gray-600 mb-1">/wɪl ju kʌm təˈmɑːroʊ/</div>
                    <div class="text-gray-700">你明天会来吗？</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">特殊疑问句</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">询问人物</div>
                    <div class="keyword text-lg mb-1">Who is that person?</div>
                    <div class="text-sm text-gray-600 mb-1">/hu ɪz ðæt ˈpɜːrsən/</div>
                    <div class="text-gray-700">那个人是谁？</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">询问事物</div>
                    <div class="keyword text-lg mb-1">What is your favorite color?</div>
                    <div class="text-sm text-gray-600 mb-1">/wʌt ɪz jʊr ˈfeɪvərɪt ˈkʌlər/</div>
                    <div class="text-gray-700">你最喜欢的颜色是什么？</div>
                </div>

                <div class="card bg-red-50 border border-red-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">询问地点</div>
                    <div class="keyword text-lg mb-1">Where do you live?</div>
                    <div class="text-sm text-gray-600 mb-1">/wer du ju lɪv/</div>
                    <div class="text-gray-700">你住在哪里？</div>
                </div>

                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">询问时间</div>
                    <div class="keyword text-lg mb-1">When did you arrive?</div>
                    <div class="text-sm text-gray-600 mb-1">/wen dɪd ju əˈraɪv/</div>
                    <div class="text-gray-700">你什么时候到的？</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">询问原因</div>
                    <div class="keyword text-lg mb-1">Why are you late?</div>
                    <div class="text-sm text-gray-600 mb-1">/waɪ ɑːr ju leɪt/</div>
                    <div class="text-gray-700">你为什么迟到了？</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">询问方式</div>
                    <div class="keyword text-lg mb-1">How do you make coffee?</div>
                    <div class="text-sm text-gray-600 mb-1">/haʊ du ju meɪk ˈkɔːfi/</div>
                    <div class="text-gray-700">你怎么煮咖啡？</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">询问数量</div>
                    <div class="keyword text-lg mb-1">How many books do you have?</div>
                    <div class="text-sm text-gray-600 mb-1">/haʊ ˈmeni bʊks du ju hæv/</div>
                    <div class="text-gray-700">你有多少本书？</div>
                </div>

                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">询问程度</div>
                    <div class="keyword text-lg mb-1">How much does it cost?</div>
                    <div class="text-sm text-gray-600 mb-1">/haʊ mʌtʃ dʌz ɪt kɔːst/</div>
                    <div class="text-gray-700">这个多少钱？</div>
                </div>

                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">询问选择</div>
                    <div class="keyword text-lg mb-1">Which one do you prefer?</div>
                    <div class="text-sm text-gray-600 mb-1">/wɪtʃ wʌn du ju prɪˈfɜːr/</div>
                    <div class="text-gray-700">你更喜欢哪一个？</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">选择疑问句</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">二选一</div>
                    <div class="keyword text-lg mb-1">Do you want tea or coffee?</div>
                    <div class="text-sm text-gray-600 mb-1">/du ju wɑːnt ti ɔːr ˈkɔːfi/</div>
                    <div class="text-gray-700">你要茶还是咖啡？</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">多项选择</div>
                    <div class="keyword text-lg mb-1">Would you like red, blue, or green?</div>
                    <div class="text-sm text-gray-600 mb-1">/wʊd ju laɪk red blu ɔːr ɡriːn/</div>
                    <div class="text-gray-700">你想要红色、蓝色还是绿色？</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">反意疑问句</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-red-50 border border-red-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">肯定句+否定疑问</div>
                    <div class="keyword text-lg mb-1">You are coming, aren't you?</div>
                    <div class="text-sm text-gray-600 mb-1">/ju ɑːr ˈkʌmɪŋ ˈɑːrnt ju/</div>
                    <div class="text-gray-700">你会来的，不是吗？</div>
                </div>

                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">否定句+肯定疑问</div>
                    <div class="keyword text-lg mb-1">You don't like it, do you?</div>
                    <div class="text-sm text-gray-600 mb-1">/ju doʊnt laɪk ɪt du ju/</div>
                    <div class="text-gray-700">你不喜欢它，是吗？</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">过去时反意疑问</div>
                    <div class="keyword text-lg mb-1">She went home, didn't she?</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi went hoʊm ˈdɪdnt ʃi/</div>
                    <div class="text-gray-700">她回家了，不是吗？</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">情态动词反意疑问</div>
                    <div class="keyword text-lg mb-1">You can help me, can't you?</div>
                    <div class="text-sm text-gray-600 mb-1">/ju kæn help mi kænt ju/</div>
                    <div class="text-gray-700">你能帮我，不是吗？</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">完成时反意疑问</div>
                    <div class="keyword text-lg mb-1">They have arrived, haven't they?</div>
                    <div class="text-sm text-gray-600 mb-1">/ðeɪ hæv əˈraɪvd ˈhævnt ðeɪ/</div>
                    <div class="text-gray-700">他们已经到了，不是吗？</div>
                </div>

                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">将来时反意疑问</div>
                    <div class="keyword text-lg mb-1">It will rain tomorrow, won't it?</div>
                    <div class="text-sm text-gray-600 mb-1">/ɪt wɪl reɪn təˈmɑːroʊ woʊnt ɪt/</div>
                    <div class="text-gray-700">明天会下雨，不是吗？</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">间接疑问句</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">礼貌询问</div>
                    <div class="keyword text-lg mb-1">Could you tell me where the station is?</div>
                    <div class="text-sm text-gray-600 mb-1">/kʊd ju tel mi wer ðə ˈsteɪʃən ɪz/</div>
                    <div class="text-gray-700">你能告诉我车站在哪里吗？</div>
                </div>

                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">委婉询问</div>
                    <div class="keyword text-lg mb-1">Do you know what time it is?</div>
                    <div class="text-sm text-gray-600 mb-1">/du ju noʊ wʌt taɪm ɪt ɪz/</div>
                    <div class="text-gray-700">你知道现在几点了吗？</div>
                </div>
            </div>
        </section>

        <!-- 感叹号 (Exclamation Mark) 部分 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">感叹号 (Exclamation Mark) - 情感的表达</h2>
            <p class="text-gray-700 mb-6">感叹号用于表达强烈的情感，如惊讶、兴奋、愤怒、喜悦等。它能够增强语句的感情色彩，使表达更加生动有力。</p>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">表达惊讶</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">意外惊讶</div>
                    <div class="keyword text-lg mb-1">What a surprise!</div>
                    <div class="text-sm text-gray-600 mb-1">/wʌt ə sərˈpraɪz/</div>
                    <div class="text-gray-700">真是个惊喜！</div>
                </div>

                <div class="card bg-red-50 border border-red-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">震惊</div>
                    <div class="keyword text-lg mb-1">I can't believe it!</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ kænt bɪˈliːv ɪt/</div>
                    <div class="text-gray-700">我不敢相信！</div>
                </div>

                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">惊叹</div>
                    <div class="keyword text-lg mb-1">How amazing!</div>
                    <div class="text-sm text-gray-600 mb-1">/haʊ əˈmeɪzɪŋ/</div>
                    <div class="text-gray-700">太令人惊叹了！</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">不可思议</div>
                    <div class="keyword text-lg mb-1">Incredible!</div>
                    <div class="text-sm text-gray-600 mb-1">/ɪnˈkredəbəl/</div>
                    <div class="text-gray-700">不可思议！</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">吃惊</div>
                    <div class="keyword text-lg mb-1">Oh my goodness!</div>
                    <div class="text-sm text-gray-600 mb-1">/oʊ maɪ ˈɡʊdnəs/</div>
                    <div class="text-gray-700">我的天哪！</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">惊呼</div>
                    <div class="keyword text-lg mb-1">Wow!</div>
                    <div class="text-sm text-gray-600 mb-1">/waʊ/</div>
                    <div class="text-gray-700">哇！</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">表达兴奋和喜悦</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">极度兴奋</div>
                    <div class="keyword text-lg mb-1">I'm so excited!</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪm soʊ ɪkˈsaɪtɪd/</div>
                    <div class="text-gray-700">我太兴奋了！</div>
                </div>

                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">庆祝</div>
                    <div class="keyword text-lg mb-1">Congratulations!</div>
                    <div class="text-sm text-gray-600 mb-1">/kənˌɡrætʃəˈleɪʃənz/</div>
                    <div class="text-gray-700">恭喜！</div>
                </div>

                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">成功喜悦</div>
                    <div class="keyword text-lg mb-1">We did it!</div>
                    <div class="text-sm text-gray-600 mb-1">/wi dɪd ɪt/</div>
                    <div class="text-gray-700">我们做到了！</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">欢呼</div>
                    <div class="keyword text-lg mb-1">Hooray!</div>
                    <div class="text-sm text-gray-600 mb-1">/hʊˈreɪ/</div>
                    <div class="text-gray-700">万岁！</div>
                </div>

                <div class="card bg-red-50 border border-red-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">太棒了</div>
                    <div class="keyword text-lg mb-1">Fantastic!</div>
                    <div class="text-sm text-gray-600 mb-1">/fænˈtæstɪk/</div>
                    <div class="text-gray-700">太棒了！</div>
                </div>

                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">完美</div>
                    <div class="keyword text-lg mb-1">Perfect!</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈpɜːrfɪkt/</div>
                    <div class="text-gray-700">完美！</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">表达愤怒和不满</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">愤怒</div>
                    <div class="keyword text-lg mb-1">I'm so angry!</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪm soʊ ˈæŋɡri/</div>
                    <div class="text-gray-700">我太生气了！</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">受够了</div>
                    <div class="keyword text-lg mb-1">That's enough!</div>
                    <div class="text-sm text-gray-600 mb-1">/ðæts ɪˈnʌf/</div>
                    <div class="text-gray-700">够了！</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">停止</div>
                    <div class="keyword text-lg mb-1">Stop it!</div>
                    <div class="text-sm text-gray-600 mb-1">/stɑːp ɪt/</div>
                    <div class="text-gray-700">停下！</div>
                </div>

                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">不可能</div>
                    <div class="keyword text-lg mb-1">No way!</div>
                    <div class="text-sm text-gray-600 mb-1">/noʊ weɪ/</div>
                    <div class="text-gray-700">不可能！</div>
                </div>

                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">荒谬</div>
                    <div class="keyword text-lg mb-1">Ridiculous!</div>
                    <div class="text-sm text-gray-600 mb-1">/rɪˈdɪkjələs/</div>
                    <div class="text-gray-700">荒谬！</div>
                </div>

                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">离开</div>
                    <div class="keyword text-lg mb-1">Get out!</div>
                    <div class="text-sm text-gray-600 mb-1">/ɡet aʊt/</div>
                    <div class="text-gray-700">出去！</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">命令和请求</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">紧急命令</div>
                    <div class="keyword text-lg mb-1">Hurry up!</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈhɜːri ʌp/</div>
                    <div class="text-gray-700">快点！</div>
                </div>

                <div class="card bg-red-50 border border-red-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">小心</div>
                    <div class="keyword text-lg mb-1">Be careful!</div>
                    <div class="text-sm text-gray-600 mb-1">/bi ˈkerfəl/</div>
                    <div class="text-gray-700">小心！</div>
                </div>

                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">注意</div>
                    <div class="keyword text-lg mb-1">Watch out!</div>
                    <div class="text-sm text-gray-600 mb-1">/wɑːtʃ aʊt/</div>
                    <div class="text-gray-700">当心！</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">帮助</div>
                    <div class="keyword text-lg mb-1">Help me!</div>
                    <div class="text-sm text-gray-600 mb-1">/help mi/</div>
                    <div class="text-gray-700">帮帮我！</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">等等</div>
                    <div class="keyword text-lg mb-1">Wait!</div>
                    <div class="text-sm text-gray-600 mb-1">/weɪt/</div>
                    <div class="text-gray-700">等等！</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">听着</div>
                    <div class="keyword text-lg mb-1">Listen!</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈlɪsən/</div>
                    <div class="text-gray-700">听着！</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">问候和告别</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">热情问候</div>
                    <div class="keyword text-lg mb-1">Hello there!</div>
                    <div class="text-sm text-gray-600 mb-1">/həˈloʊ ðer/</div>
                    <div class="text-gray-700">你好！</div>
                </div>

                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">早上好</div>
                    <div class="keyword text-lg mb-1">Good morning!</div>
                    <div class="text-sm text-gray-600 mb-1">/ɡʊd ˈmɔːrnɪŋ/</div>
                    <div class="text-gray-700">早上好！</div>
                </div>

                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">再见</div>
                    <div class="keyword text-lg mb-1">Goodbye!</div>
                    <div class="text-sm text-gray-600 mb-1">/ɡʊdˈbaɪ/</div>
                    <div class="text-gray-700">再见！</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">回头见</div>
                    <div class="keyword text-lg mb-1">See you later!</div>
                    <div class="text-sm text-gray-600 mb-1">/si ju ˈleɪtər/</div>
                    <div class="text-gray-700">回头见！</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">感叹词</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                <div class="card bg-red-50 border border-red-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">哎呀</div>
                    <div class="keyword text-lg mb-1">Oops!</div>
                    <div class="text-sm text-gray-600 mb-1">/ʊps/</div>
                    <div class="text-gray-700">哎呀！</div>
                </div>

                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">哦</div>
                    <div class="keyword text-lg mb-1">Oh!</div>
                    <div class="text-sm text-gray-600 mb-1">/oʊ/</div>
                    <div class="text-gray-700">哦！</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">啊</div>
                    <div class="keyword text-lg mb-1">Ah!</div>
                    <div class="text-sm text-gray-600 mb-1">/ɑː/</div>
                    <div class="text-gray-700">啊！</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">嗯</div>
                    <div class="keyword text-lg mb-1">Hmm!</div>
                    <div class="text-sm text-gray-600 mb-1">/hm/</div>
                    <div class="text-gray-700">嗯！</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">嘘</div>
                    <div class="keyword text-lg mb-1">Shh!</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃ/</div>
                    <div class="text-gray-700">嘘！</div>
                </div>

                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">呃</div>
                    <div class="keyword text-lg mb-1">Ugh!</div>
                    <div class="text-sm text-gray-600 mb-1">/ʌɡ/</div>
                    <div class="text-gray-700">呃！</div>
                </div>

                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">哇哦</div>
                    <div class="keyword text-lg mb-1">Whoa!</div>
                    <div class="text-sm text-gray-600 mb-1">/woʊ/</div>
                    <div class="text-gray-700">哇哦！</div>
                </div>

                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">耶</div>
                    <div class="keyword text-lg mb-1">Yeah!</div>
                    <div class="text-sm text-gray-600 mb-1">/jeə/</div>
                    <div class="text-gray-700">耶！</div>
                </div>
            </div>
        </section>

        <!-- 标点符号组合使用 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">标点符号的组合使用</h2>
            <p class="text-gray-700 mb-6">在实际写作中，不同的标点符号经常需要组合使用，以准确表达复杂的语义和情感。掌握标点符号的组合规则对于提高英语写作水平至关重要。</p>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">逗号与问号的组合</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">列举式疑问</div>
                    <div class="keyword text-lg mb-1">Do you like apples, oranges, or bananas?</div>
                    <div class="text-sm text-gray-600 mb-1">/du ju laɪk ˈæpəlz ˈɔːrɪndʒɪz ɔːr bəˈnænəz/</div>
                    <div class="text-gray-700">你喜欢苹果、橙子还是香蕉？</div>
                </div>

                <div class="card bg-red-50 border border-red-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">复杂疑问句</div>
                    <div class="keyword text-lg mb-1">When you finish your work, will you come with us?</div>
                    <div class="text-sm text-gray-600 mb-1">/wen ju ˈfɪnɪʃ jʊr wɜːrk wɪl ju kʌm wɪð ʌs/</div>
                    <div class="text-gray-700">当你完成工作后，你会和我们一起来吗？</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">逗号与感叹号的组合</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">强调称呼</div>
                    <div class="keyword text-lg mb-1">Come here, quickly!</div>
                    <div class="text-sm text-gray-600 mb-1">/kʌm hɪr ˈkwɪkli/</div>
                    <div class="text-gray-700">快过来！</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">情感强调</div>
                    <div class="keyword text-lg mb-1">Oh my, what a beautiful day!</div>
                    <div class="text-sm text-gray-600 mb-1">/oʊ maɪ wʌt ə ˈbjuːtɪfəl deɪ/</div>
                    <div class="text-gray-700">哦，多么美好的一天！</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">句号在复杂句中的使用</h3>
            <div class="grid grid-cols-1 gap-4 mb-6">
                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">多重从句</div>
                    <div class="keyword text-lg mb-1">Although it was raining heavily, we decided to go out because we had important business to attend to.</div>
                    <div class="text-sm text-gray-600 mb-1">/ɔːlˈðoʊ ɪt wʌz ˈreɪnɪŋ ˈhevɪli wi dɪˈsaɪdɪd tu ɡoʊ aʊt bɪˈkɔːz wi hæd ɪmˈpɔːrtənt ˈbɪznəs tu əˈtend tu/</div>
                    <div class="text-gray-700">尽管雨下得很大，我们还是决定出去，因为我们有重要的事情要处理。</div>
                </div>
            </div>
        </section>

        <!-- 标点符号的高级应用 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">标点符号的高级应用</h2>
            <p class="text-gray-700 mb-6">掌握标点符号的高级用法能够让你的英语表达更加精确和地道。这些用法在正式写作和日常交流中都非常重要。</p>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">句号的特殊用法</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">学位缩写</div>
                    <div class="keyword text-lg mb-1">Ph.D.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˌpiː eɪtʃ ˈdiː/</div>
                    <div class="text-gray-700">哲学博士</div>
                </div>

                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">公司缩写</div>
                    <div class="keyword text-lg mb-1">Inc.</div>
                    <div class="text-sm text-gray-600 mb-1">/ɪŋk/</div>
                    <div class="text-gray-700">股份有限公司</div>
                </div>

                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">时间缩写</div>
                    <div class="keyword text-lg mb-1">a.m.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˌeɪ ˈem/</div>
                    <div class="text-gray-700">上午</div>
                </div>

                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">下午时间</div>
                    <div class="keyword text-lg mb-1">p.m.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˌpiː ˈem/</div>
                    <div class="text-gray-700">下午</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">地址缩写</div>
                    <div class="keyword text-lg mb-1">St.</div>
                    <div class="text-sm text-gray-600 mb-1">/striːt/</div>
                    <div class="text-gray-700">街道</div>
                </div>

                <div class="card bg-red-50 border border-red-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">大道缩写</div>
                    <div class="keyword text-lg mb-1">Ave.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈævəˌnu/</div>
                    <div class="text-gray-700">大道</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">逗号的高级用法</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">同位语分隔</div>
                    <div class="keyword text-lg mb-1">My friend, a talented musician, plays the piano beautifully.</div>
                    <div class="text-sm text-gray-600 mb-1">/maɪ frend ə ˈtæləntɪd mjuˈzɪʃən pleɪz ðə piˈænoʊ ˈbjuːtɪfəli/</div>
                    <div class="text-gray-700">我的朋友，一位有才华的音乐家，钢琴弹得很美。</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">分词短语分隔</div>
                    <div class="keyword text-lg mb-1">Walking slowly, she enjoyed the beautiful scenery.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈwɔːkɪŋ ˈsloʊli ʃi ɪnˈdʒɔɪd ðə ˈbjuːtɪfəl ˈsinəri/</div>
                    <div class="text-gray-700">慢慢地走着，她欣赏着美丽的风景。</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">介词短语分隔</div>
                    <div class="keyword text-lg mb-1">In the morning, I like to drink coffee.</div>
                    <div class="text-sm text-gray-600 mb-1">/ɪn ðə ˈmɔːrnɪŋ aɪ laɪk tu drɪŋk ˈkɔːfi/</div>
                    <div class="text-gray-700">在早上，我喜欢喝咖啡。</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">对比结构</div>
                    <div class="keyword text-lg mb-1">She is intelligent, not just beautiful.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi ɪz ɪnˈtelɪdʒənt nɑːt dʒʌst ˈbjuːtɪfəl/</div>
                    <div class="text-gray-700">她很聪明，不仅仅是美丽。</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">问号的特殊情况</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">修辞疑问句</div>
                    <div class="keyword text-lg mb-1">Isn't life wonderful?</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈɪznt laɪf ˈwʌndərfəl/</div>
                    <div class="text-gray-700">生活不是很美好吗？</div>
                </div>

                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">疑问语调陈述句</div>
                    <div class="keyword text-lg mb-1">You're coming with us?</div>
                    <div class="text-sm text-gray-600 mb-1">/jʊr ˈkʌmɪŋ wɪð ʌs/</div>
                    <div class="text-gray-700">你要和我们一起来？</div>
                </div>

                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">表示不确定</div>
                    <div class="keyword text-lg mb-1">He said he would come at 3 o'clock?</div>
                    <div class="text-sm text-gray-600 mb-1">/hi sed hi wʊd kʌm æt θri əˈklɑːk/</div>
                    <div class="text-gray-700">他说他会在3点来？</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">惊讶疑问</div>
                    <div class="keyword text-lg mb-1">You won the lottery?</div>
                    <div class="text-sm text-gray-600 mb-1">/ju wʌn ðə ˈlɑːtəri/</div>
                    <div class="text-gray-700">你中彩票了？</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">感叹号的语气强度</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-red-50 border border-red-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">轻微感叹</div>
                    <div class="keyword text-lg mb-1">That's nice!</div>
                    <div class="text-sm text-gray-600 mb-1">/ðæts naɪs/</div>
                    <div class="text-gray-700">那很好！</div>
                </div>

                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">中等感叹</div>
                    <div class="keyword text-lg mb-1">What a great idea!</div>
                    <div class="text-sm text-gray-600 mb-1">/wʌt ə ɡreɪt aɪˈdiə/</div>
                    <div class="text-gray-700">多么好的想法！</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">强烈感叹</div>
                    <div class="keyword text-lg mb-1">Absolutely amazing!</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈæbsəˌlutli əˈmeɪzɪŋ/</div>
                    <div class="text-gray-700">绝对令人惊叹！</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">极度感叹</div>
                    <div class="keyword text-lg mb-1">Unbelievable!</div>
                    <div class="text-sm text-gray-600 mb-1">/ˌʌnbɪˈliːvəbəl/</div>
                    <div class="text-gray-700">难以置信！</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">震惊感叹</div>
                    <div class="keyword text-lg mb-1">Mind-blowing!</div>
                    <div class="text-sm text-gray-600 mb-1">/maɪnd ˈbloʊɪŋ/</div>
                    <div class="text-gray-700">令人震惊！</div>
                </div>

                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">极致感叹</div>
                    <div class="keyword text-lg mb-1">Out of this world!</div>
                    <div class="text-sm text-gray-600 mb-1">/aʊt ʌv ðɪs wɜːrld/</div>
                    <div class="text-gray-700">超凡脱俗！</div>
                </div>
            </div>
        </section>

        <!-- 实际应用场景 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">标点符号在不同场景中的应用</h2>
            <p class="text-gray-700 mb-6">不同的交流场景需要使用不同的标点符号组合。掌握这些应用场景能够让你的英语表达更加得体和准确。</p>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">商务邮件中的标点使用</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">正式开头</div>
                    <div class="keyword text-lg mb-1">Dear Mr. Johnson,</div>
                    <div class="text-sm text-gray-600 mb-1">/dɪr ˈmɪstər ˈdʒɑːnsən/</div>
                    <div class="text-gray-700">亲爱的约翰逊先生，</div>
                </div>

                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">礼貌结尾</div>
                    <div class="keyword text-lg mb-1">Best regards,</div>
                    <div class="text-sm text-gray-600 mb-1">/best rɪˈɡɑːrdz/</div>
                    <div class="text-gray-700">此致敬礼，</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">请求确认</div>
                    <div class="keyword text-lg mb-1">Could you please confirm your attendance?</div>
                    <div class="text-sm text-gray-600 mb-1">/kʊd ju pliːz kənˈfɜːrm jʊr əˈtendəns/</div>
                    <div class="text-gray-700">您能确认一下您的出席吗？</div>
                </div>

                <div class="card bg-red-50 border border-red-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">表达感谢</div>
                    <div class="keyword text-lg mb-1">Thank you for your prompt response.</div>
                    <div class="text-sm text-gray-600 mb-1">/θæŋk ju fɔːr jʊr prɑːmpt rɪˈspɑːns/</div>
                    <div class="text-gray-700">感谢您的及时回复。</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">日常对话中的标点表达</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">表示惊讶</div>
                    <div class="keyword text-lg mb-1">Really? That's amazing!</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈriːəli ðæts əˈmeɪzɪŋ/</div>
                    <div class="text-gray-700">真的吗？太令人惊讶了！</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">表示同意</div>
                    <div class="keyword text-lg mb-1">Exactly! You're absolutely right.</div>
                    <div class="text-sm text-gray-600 mb-1">/ɪɡˈzæktli jʊr ˈæbsəˌlutli raɪt/</div>
                    <div class="text-gray-700">完全正确！你说得对。</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">表示疑虑</div>
                    <div class="keyword text-lg mb-1">Are you sure about that?</div>
                    <div class="text-sm text-gray-600 mb-1">/ɑːr ju ʃʊr əˈbaʊt ðæt/</div>
                    <div class="text-gray-700">你确定吗？</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">表示兴奋</div>
                    <div class="keyword text-lg mb-1">I can't wait! This is so exciting!</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ kænt weɪt ðɪs ɪz soʊ ɪkˈsaɪtɪŋ/</div>
                    <div class="text-gray-700">我等不及了！这太令人兴奋了！</div>
                </div>

                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">表示关心</div>
                    <div class="keyword text-lg mb-1">How are you feeling today?</div>
                    <div class="text-sm text-gray-600 mb-1">/haʊ ɑːr ju ˈfiːlɪŋ təˈdeɪ/</div>
                    <div class="text-gray-700">你今天感觉怎么样？</div>
                </div>

                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">表示道歉</div>
                    <div class="keyword text-lg mb-1">I'm so sorry! Please forgive me.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪm soʊ ˈsɑːri pliːz fərˈɡɪv mi/</div>
                    <div class="text-gray-700">我很抱歉！请原谅我。</div>
                </div>
            </div>
        </section>

        <!-- 标点符号的常见错误 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">标点符号的常见错误与纠正</h2>
            <p class="text-gray-700 mb-6">了解并避免标点符号的常见错误是提高英语写作水平的重要步骤。以下是一些最常见的错误及其正确用法。</p>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">句号使用错误</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-red-50 border border-red-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">错误：句子片段</div>
                    <div class="keyword text-lg mb-1">Because I was tired.</div>
                    <div class="text-sm text-gray-600 mb-1">/bɪˈkɔːz aɪ wʌz ˈtaɪərd/</div>
                    <div class="text-gray-700">因为我累了。（不完整）</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">正确：完整句子</div>
                    <div class="keyword text-lg mb-1">I went home because I was tired.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ went hoʊm bɪˈkɔːz aɪ wʌz ˈtaɪərd/</div>
                    <div class="text-gray-700">我回家了，因为我累了。</div>
                </div>

                <div class="card bg-red-50 border border-red-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">错误：连写句</div>
                    <div class="keyword text-lg mb-1">I like coffee I drink it every morning.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ laɪk ˈkɔːfi aɪ drɪŋk ɪt ˈevri ˈmɔːrnɪŋ/</div>
                    <div class="text-gray-700">我喜欢咖啡我每天早上都喝。（错误）</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">正确：分开句子</div>
                    <div class="keyword text-lg mb-1">I like coffee. I drink it every morning.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ laɪk ˈkɔːfi aɪ drɪŋk ɪt ˈevri ˈmɔːrnɪŋ/</div>
                    <div class="text-gray-700">我喜欢咖啡。我每天早上都喝。</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">逗号使用错误</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-red-50 border border-red-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">错误：逗号连接</div>
                    <div class="keyword text-lg mb-1">I went to the store, I bought some milk.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ went tu ðə stɔːr aɪ bɔːt sʌm mɪlk/</div>
                    <div class="text-gray-700">我去了商店，我买了一些牛奶。（错误）</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">正确：添加连词</div>
                    <div class="keyword text-lg mb-1">I went to the store, and I bought some milk.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ went tu ðə stɔːr ænd aɪ bɔːt sʌm mɪlk/</div>
                    <div class="text-gray-700">我去了商店，买了一些牛奶。</div>
                </div>

                <div class="card bg-red-50 border border-red-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">错误：缺少逗号</div>
                    <div class="keyword text-lg mb-1">When I arrived everyone was waiting.</div>
                    <div class="text-sm text-gray-600 mb-1">/wen aɪ əˈraɪvd ˈevriˌwʌn wʌz ˈweɪtɪŋ/</div>
                    <div class="text-gray-700">当我到达时每个人都在等待。（错误）</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">正确：添加逗号</div>
                    <div class="keyword text-lg mb-1">When I arrived, everyone was waiting.</div>
                    <div class="text-sm text-gray-600 mb-1">/wen aɪ əˈraɪvd ˈevriˌwʌn wʌz ˈweɪtɪŋ/</div>
                    <div class="text-gray-700">当我到达时，每个人都在等待。</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">问号使用错误</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-red-50 border border-red-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">错误：间接疑问句</div>
                    <div class="keyword text-lg mb-1">I wonder what time it is?</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ ˈwʌndər wʌt taɪm ɪt ɪz/</div>
                    <div class="text-gray-700">我想知道现在几点了？（错误）</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">正确：使用句号</div>
                    <div class="keyword text-lg mb-1">I wonder what time it is.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ ˈwʌndər wʌt taɪm ɪt ɪz/</div>
                    <div class="text-gray-700">我想知道现在几点了。</div>
                </div>

                <div class="card bg-red-50 border border-red-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">错误：陈述句用问号</div>
                    <div class="keyword text-lg mb-1">She told me she would come?</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi toʊld mi ʃi wʊd kʌm/</div>
                    <div class="text-gray-700">她告诉我她会来？（错误）</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">正确：使用句号</div>
                    <div class="keyword text-lg mb-1">She told me she would come.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi toʊld mi ʃi wʊd kʌm/</div>
                    <div class="text-gray-700">她告诉我她会来。</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">感叹号使用错误</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-red-50 border border-red-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">错误：过度使用</div>
                    <div class="keyword text-lg mb-1">I went to the store! I bought some milk! It was expensive!</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ went tu ðə stɔːr aɪ bɔːt sʌm mɪlk ɪt wʌz ɪkˈspensɪv/</div>
                    <div class="text-gray-700">我去了商店！我买了一些牛奶！很贵！（过度）</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">正确：适度使用</div>
                    <div class="keyword text-lg mb-1">I went to the store and bought some milk. It was so expensive!</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ went tu ðə stɔːr ænd bɔːt sʌm mɪlk ɪt wʌz soʊ ɪkˈspensɪv/</div>
                    <div class="text-gray-700">我去商店买了一些牛奶。太贵了！</div>
                </div>
            </div>
        </section>

        <!-- 标点符号的文体差异 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">不同文体中的标点符号使用</h2>
            <p class="text-gray-700 mb-6">不同的写作文体对标点符号的使用有不同的要求和习惯。掌握这些差异能够让你的写作更加专业和得体。</p>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">学术写作中的标点</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">正式陈述</div>
                    <div class="keyword text-lg mb-1">The research indicates that climate change affects global temperatures.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə rɪˈsɜːrtʃ ˈɪndɪˌkeɪts ðæt ˈklaɪmət tʃeɪndʒ əˈfekts ˈɡloʊbəl ˈtemprətʃərz/</div>
                    <div class="text-gray-700">研究表明气候变化影响全球温度。</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">复杂句式</div>
                    <div class="keyword text-lg mb-1">Although the data is limited, the findings suggest significant correlations.</div>
                    <div class="text-sm text-gray-600 mb-1">/ɔːlˈðoʊ ðə ˈdeɪtə ɪz ˈlɪmɪtɪd ðə ˈfaɪndɪŋz səˈdʒest sɪɡˈnɪfɪkənt ˌkɔːrəˈleɪʃənz/</div>
                    <div class="text-gray-700">尽管数据有限，但研究结果表明存在显著相关性。</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">创意写作中的标点</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">情感表达</div>
                    <div class="keyword text-lg mb-1">The wind whispered through the trees... so peaceful, so serene.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə wɪnd ˈwɪspərd θru ðə triːz soʊ ˈpiːsfəl soʊ səˈriːn/</div>
                    <div class="text-gray-700">风在树间轻语...如此宁静，如此安详。</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">戏剧效果</div>
                    <div class="keyword text-lg mb-1">Suddenly, everything changed! The world would never be the same.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈsʌdənli ˈevriθɪŋ tʃeɪndʒd ðə wɜːrld wʊd ˈnevər bi ðə seɪm/</div>
                    <div class="text-gray-700">突然，一切都改变了！世界再也不会是原来的样子。</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">新闻写作中的标点</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">客观报道</div>
                    <div class="keyword text-lg mb-1">The mayor announced new policies during yesterday's press conference.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈmeɪər əˈnaʊnst nu ˈpɑːləsiz ˈdʊrɪŋ ˈjestərˌdeɪz pres ˈkɑːnfərəns/</div>
                    <div class="text-gray-700">市长在昨天的新闻发布会上宣布了新政策。</div>
                </div>

                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">引用报道</div>
                    <div class="keyword text-lg mb-1">"We are committed to improving public services," the mayor stated.</div>
                    <div class="text-sm text-gray-600 mb-1">/wi ɑːr kəˈmɪtɪd tu ɪmˈpruvɪŋ ˈpʌblɪk ˈsɜːrvəsəz ðə ˈmeɪər ˈsteɪtɪd/</div>
                    <div class="text-gray-700">"我们致力于改善公共服务，"市长表示。</div>
                </div>
            </div>
        </section>

        <!-- 标点符号的语音表达 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">标点符号在口语中的体现</h2>
            <p class="text-gray-700 mb-6">虽然标点符号主要用于书面语，但它们在口语中也有相应的表达方式。理解这种对应关系有助于提高英语的整体表达能力。</p>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">语调与标点的对应</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">句号：降调</div>
                    <div class="keyword text-lg mb-1">I'm going home.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪm ˈɡoʊɪŋ hoʊm ↘/</div>
                    <div class="text-gray-700">我要回家了。（降调）</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">问号：升调</div>
                    <div class="keyword text-lg mb-1">Are you coming?</div>
                    <div class="text-sm text-gray-600 mb-1">/ɑːr ju ˈkʌmɪŋ ↗/</div>
                    <div class="text-gray-700">你来吗？（升调）</div>
                </div>

                <div class="card bg-red-50 border border-red-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">感叹号：强调</div>
                    <div class="keyword text-lg mb-1">What a surprise!</div>
                    <div class="text-sm text-gray-600 mb-1">/wʌt ə sərˈpraɪz ↗↘/</div>
                    <div class="text-gray-700">真是个惊喜！（强调语调）</div>
                </div>

                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">逗号：短暂停顿</div>
                    <div class="keyword text-lg mb-1">When I arrived, | everyone was there.</div>
                    <div class="text-sm text-gray-600 mb-1">/wen aɪ əˈraɪvd | ˈevriˌwʌn wʌz ðer/</div>
                    <div class="text-gray-700">当我到达时，（停顿）每个人都在那里。</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">列举：平调</div>
                    <div class="keyword text-lg mb-1">I need apples, → bananas, → and oranges.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ niːd ˈæpəlz → bəˈnænəz → ænd ˈɔːrɪndʒɪz/</div>
                    <div class="text-gray-700">我需要苹果，香蕉，和橙子。</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">反意疑问：升调</div>
                    <div class="keyword text-lg mb-1">You're coming, aren't you? ↗</div>
                    <div class="text-sm text-gray-600 mb-1">/jʊr ˈkʌmɪŋ ˈɑːrnt ju ↗/</div>
                    <div class="text-gray-700">你会来的，不是吗？（升调）</div>
                </div>
            </div>
        </section>

        <!-- 标点符号的高级组合 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">标点符号的高级组合技巧</h2>
            <p class="text-gray-700 mb-6">熟练掌握标点符号的组合使用能够让你的英语表达更加丰富和准确。这些技巧在高级英语写作中尤为重要。</p>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">复杂句式中的标点组合</h3>
            <div class="grid grid-cols-1 gap-4 mb-6">
                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">多重从句组合</div>
                    <div class="keyword text-lg mb-1">When the meeting ended, which was later than expected, everyone was tired, but we still had work to finish.</div>
                    <div class="text-sm text-gray-600 mb-1">/wen ðə ˈmiːtɪŋ ˈendəd wɪtʃ wʌz ˈleɪtər ðæn ɪkˈspektəd ˈevriˌwʌn wʌz ˈtaɪərd bʌt wi stɪl hæd wɜːrk tu ˈfɪnɪʃ/</div>
                    <div class="text-gray-700">当会议结束时，比预期的要晚，每个人都很累，但我们仍有工作要完成。</div>
                </div>

                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">并列与从属结合</div>
                    <div class="keyword text-lg mb-1">Although the weather was bad, we decided to go hiking, and surprisingly, we had a great time.</div>
                    <div class="text-sm text-gray-600 mb-1">/ɔːlˈðoʊ ðə ˈweðər wʌz bæd wi dɪˈsaɪdɪd tu ɡoʊ ˈhaɪkɪŋ ænd sərˈpraɪzɪŋli wi hæd ə ɡreɪt taɪm/</div>
                    <div class="text-gray-700">尽管天气不好，我们还是决定去远足，令人惊讶的是，我们玩得很开心。</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">情感层次的标点表达</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">递进情感</div>
                    <div class="keyword text-lg mb-1">I was surprised, then shocked, and finally amazed!</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ wʌz sərˈpraɪzd ðen ʃɑːkt ænd ˈfaɪnəli əˈmeɪzd/</div>
                    <div class="text-gray-700">我先是惊讶，然后震惊，最后惊叹不已！</div>
                </div>

                <div class="card bg-red-50 border border-red-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">对比情感</div>
                    <div class="keyword text-lg mb-1">Was I happy? No, I was ecstatic!</div>
                    <div class="text-sm text-gray-600 mb-1">/wʌz aɪ ˈhæpi noʊ aɪ wʌz ɪkˈstætɪk/</div>
                    <div class="text-gray-700">我高兴吗？不，我欣喜若狂！</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">描述性写作中的标点</h3>
            <div class="grid grid-cols-1 gap-4 mb-6">
                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">环境描述</div>
                    <div class="keyword text-lg mb-1">The garden was beautiful: roses bloomed everywhere, birds sang sweetly, and the air smelled of jasmine.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈɡɑːrdən wʌz ˈbjuːtɪfəl ˈroʊzəz blumd ˈevriˌwer bɜːrdz sæŋ ˈswitli ænd ðə er smeld ʌv ˈdʒæzmən/</div>
                    <div class="text-gray-700">花园很美：玫瑰到处盛开，鸟儿甜美地歌唱，空气中弥漫着茉莉花香。</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">人物描述</div>
                    <div class="keyword text-lg mb-1">She was elegant, graceful, and intelligent—everything I admired in a person.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi wʌz ˈelɪɡənt ˈɡreɪsfəl ænd ɪnˈtelɪdʒənt ˈevriθɪŋ aɪ ədˈmaɪərd ɪn ə ˈpɜːrsən/</div>
                    <div class="text-gray-700">她优雅、端庄、聪明——我在一个人身上所钦佩的一切。</div>
                </div>
            </div>
        </section>

        <!-- 标点符号在数字和时间中的应用 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">标点符号在数字和时间表达中的应用</h2>
            <p class="text-gray-700 mb-6">数字、时间、日期等特殊表达形式有其独特的标点符号使用规则。掌握这些规则对于准确表达信息非常重要。</p>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">时间表达</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">具体时间</div>
                    <div class="keyword text-lg mb-1">3:30 p.m.</div>
                    <div class="text-sm text-gray-600 mb-1">/θri ˈθɜːrti ˌpiː ˈem/</div>
                    <div class="text-gray-700">下午3点30分</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">整点时间</div>
                    <div class="keyword text-lg mb-1">9:00 a.m.</div>
                    <div class="text-sm text-gray-600 mb-1">/naɪn əˈklɑːk ˌeɪ ˈem/</div>
                    <div class="text-gray-700">上午9点整</div>
                </div>

                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">时间范围</div>
                    <div class="keyword text-lg mb-1">9:00 a.m. - 5:00 p.m.</div>
                    <div class="text-sm text-gray-600 mb-1">/naɪn tu faɪv/</div>
                    <div class="text-gray-700">上午9点到下午5点</div>
                </div>

                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">24小时制</div>
                    <div class="keyword text-lg mb-1">15:30</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈfɪfˌtin ˈθɜːrti/</div>
                    <div class="text-gray-700">15点30分</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">日期表达</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">完整日期</div>
                    <div class="keyword text-lg mb-1">January 15, 2024</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈdʒænjuˌeri ˈfɪfˌtin ˈtwenti ˈtwenti fɔːr/</div>
                    <div class="text-gray-700">2024年1月15日</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">简化日期</div>
                    <div class="keyword text-lg mb-1">Jan. 15, 2024</div>
                    <div class="text-sm text-gray-600 mb-1">/dʒæn ˈfɪfˌtin ˈtwenti ˈtwenti fɔːr/</div>
                    <div class="text-gray-700">2024年1月15日</div>
                </div>

                <div class="card bg-red-50 border border-red-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">数字日期</div>
                    <div class="keyword text-lg mb-1">01/15/2024</div>
                    <div class="text-sm text-gray-600 mb-1">/wʌn ˈfɪfˌtin ˈtwenti ˈtwenti fɔːr/</div>
                    <div class="text-gray-700">2024年1月15日</div>
                </div>

                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">年份范围</div>
                    <div class="keyword text-lg mb-1">2020-2024</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈtwenti ˈtwenti tu ˈtwenti ˈtwenti fɔːr/</div>
                    <div class="text-gray-700">2020年到2024年</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">世纪表达</div>
                    <div class="keyword text-lg mb-1">21st century</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈtwenti fɜːrst ˈsentʃəri/</div>
                    <div class="text-gray-700">21世纪</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">年代表达</div>
                    <div class="keyword text-lg mb-1">the 1990s</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈnaɪnˌtin ˈnaɪntiz/</div>
                    <div class="text-gray-700">20世纪90年代</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">数字表达</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">大数字</div>
                    <div class="keyword text-lg mb-1">1,000,000</div>
                    <div class="text-sm text-gray-600 mb-1">/wʌn ˈmɪljən/</div>
                    <div class="text-gray-700">一百万</div>
                </div>

                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">小数</div>
                    <div class="keyword text-lg mb-1">3.14</div>
                    <div class="text-sm text-gray-600 mb-1">/θri pɔɪnt wʌn fɔːr/</div>
                    <div class="text-gray-700">三点一四</div>
                </div>

                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">百分比</div>
                    <div class="keyword text-lg mb-1">85%</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈeɪti faɪv pərˈsent/</div>
                    <div class="text-gray-700">百分之八十五</div>
                </div>

                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">温度</div>
                    <div class="keyword text-lg mb-1">25°C</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈtwenti faɪv dɪˈɡriːz ˈselsɪəs/</div>
                    <div class="text-gray-700">摄氏25度</div>
                </div>
            </div>
        </section>

        <!-- 标点符号的国际化差异 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">标点符号的地域差异</h2>
            <p class="text-gray-700 mb-6">虽然英语标点符号的基本规则是通用的，但在不同的英语国家和地区，某些标点符号的使用习惯可能略有不同。</p>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">引号的使用差异</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">双引号</div>
                    <div class="keyword text-lg mb-1">"Hello," she said.</div>
                    <div class="text-sm text-gray-600 mb-1">/həˈloʊ ʃi sed/</div>
                    <div class="text-gray-700">"你好，"她说。</div>
                </div>

                <div class="card bg-red-50 border border-red-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">单引号</div>
                    <div class="keyword text-lg mb-1">'Hello,' she said.</div>
                    <div class="text-sm text-gray-600 mb-1">/həˈloʊ ʃi sed/</div>
                    <div class="text-gray-700">'你好，'她说。</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">标点符号的现代变化</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">电子邮件风格</div>
                    <div class="keyword text-lg mb-1">Hi John,</div>
                    <div class="text-sm text-gray-600 mb-1">/haɪ dʒɑːn/</div>
                    <div class="text-gray-700">嗨，约翰，</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">非正式结尾</div>
                    <div class="keyword text-lg mb-1">Thanks!</div>
                    <div class="text-sm text-gray-600 mb-1">/θæŋks/</div>
                    <div class="text-gray-700">谢谢！</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">简化表达</div>
                    <div class="keyword text-lg mb-1">See you soon</div>
                    <div class="text-sm text-gray-600 mb-1">/si ju sun/</div>
                    <div class="text-gray-700">很快见到你</div>
                </div>
            </div>
        </section>

        <!-- 标点符号的记忆技巧 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">标点符号的记忆和应用技巧</h2>
            <p class="text-gray-700 mb-6">掌握一些实用的记忆技巧和应用方法，能够帮助你更好地运用标点符号，提高英语写作的准确性和流畅性。</p>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">记忆口诀</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">句号记忆</div>
                    <div class="keyword text-lg mb-1">Period ends the thought complete.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈpɪriəd endz ðə θɔːt kəmˈplit/</div>
                    <div class="text-gray-700">句号结束完整思想。</div>
                </div>

                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">逗号记忆</div>
                    <div class="keyword text-lg mb-1">Comma gives a little pause.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈkɑːmə ɡɪvz ə ˈlɪtəl pɔːz/</div>
                    <div class="text-gray-700">逗号给出小停顿。</div>
                </div>

                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">问号记忆</div>
                    <div class="keyword text-lg mb-1">Question mark asks what's true.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈkwestʃən mɑːrk æsks wʌts tru/</div>
                    <div class="text-gray-700">问号询问真相。</div>
                </div>

                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">感叹号记忆</div>
                    <div class="keyword text-lg mb-1">Exclamation shows strong feeling!</div>
                    <div class="text-sm text-gray-600 mb-1">/ˌekskləˈmeɪʃən ʃoʊz strɔːŋ ˈfilɪŋ/</div>
                    <div class="text-gray-700">感叹号表示强烈情感！</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">实用检查方法</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">朗读检查法</div>
                    <div class="keyword text-lg mb-1">Read aloud to check punctuation flow.</div>
                    <div class="text-sm text-gray-600 mb-1">/riːd əˈlaʊd tu tʃek ˌpʌŋktʃuˈeɪʃən floʊ/</div>
                    <div class="text-gray-700">大声朗读检查标点流畅性。</div>
                </div>

                <div class="card bg-red-50 border border-red-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">逐句分析法</div>
                    <div class="keyword text-lg mb-1">Check each sentence for completeness.</div>
                    <div class="text-sm text-gray-600 mb-1">/tʃek itʃ ˈsentəns fɔːr kəmˈplitnəs/</div>
                    <div class="text-gray-700">检查每个句子的完整性。</div>
                </div>
            </div>
        </section>

        <!-- 标点符号在不同语境中的细微差别 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">标点符号的语境敏感性</h2>
            <p class="text-gray-700 mb-6">同样的标点符号在不同的语境中可能传达不同的含义和语气。理解这些细微差别对于准确表达意思至关重要。</p>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">问号的语气变化</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">纯粹疑问</div>
                    <div class="keyword text-lg mb-1">What time is it?</div>
                    <div class="text-sm text-gray-600 mb-1">/wʌt taɪm ɪz ɪt/</div>
                    <div class="text-gray-700">现在几点了？</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">怀疑质疑</div>
                    <div class="keyword text-lg mb-1">You really think that's true?</div>
                    <div class="text-sm text-gray-600 mb-1">/ju ˈriːəli θɪŋk ðæts tru/</div>
                    <div class="text-gray-700">你真的认为那是真的吗？</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">惊讶疑问</div>
                    <div class="keyword text-lg mb-1">You won the lottery?</div>
                    <div class="text-sm text-gray-600 mb-1">/ju wʌn ðə ˈlɑːtəri/</div>
                    <div class="text-gray-700">你中彩票了？</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">礼貌请求</div>
                    <div class="keyword text-lg mb-1">Could you help me with this?</div>
                    <div class="text-sm text-gray-600 mb-1">/kʊd ju help mi wɪð ðɪs/</div>
                    <div class="text-gray-700">你能帮我做这个吗？</div>
                </div>

                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">反讽疑问</div>
                    <div class="keyword text-lg mb-1">Oh, really?</div>
                    <div class="text-sm text-gray-600 mb-1">/oʊ ˈriːəli/</div>
                    <div class="text-gray-700">哦，真的吗？</div>
                </div>

                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">确认疑问</div>
                    <div class="keyword text-lg mb-1">You're coming with us?</div>
                    <div class="text-sm text-gray-600 mb-1">/jʊr ˈkʌmɪŋ wɪð ʌs/</div>
                    <div class="text-gray-700">你要和我们一起来？</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">感叹号的强度层次</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">轻微感叹</div>
                    <div class="keyword text-lg mb-1">That's nice!</div>
                    <div class="text-sm text-gray-600 mb-1">/ðæts naɪs/</div>
                    <div class="text-gray-700">那很好！</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">中等感叹</div>
                    <div class="keyword text-lg mb-1">That's wonderful!</div>
                    <div class="text-sm text-gray-600 mb-1">/ðæts ˈwʌndərfəl/</div>
                    <div class="text-gray-700">那太棒了！</div>
                </div>

                <div class="card bg-red-50 border border-red-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">强烈感叹</div>
                    <div class="keyword text-lg mb-1">That's incredible!</div>
                    <div class="text-sm text-gray-600 mb-1">/ðæts ɪnˈkredəbəl/</div>
                    <div class="text-gray-700">那太不可思议了！</div>
                </div>

                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">极度感叹</div>
                    <div class="keyword text-lg mb-1">That's absolutely amazing!</div>
                    <div class="text-sm text-gray-600 mb-1">/ðæts ˈæbsəˌlutli əˈmeɪzɪŋ/</div>
                    <div class="text-gray-700">那绝对令人惊叹！</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">震惊感叹</div>
                    <div class="keyword text-lg mb-1">I can't believe it!</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ kænt bɪˈliːv ɪt/</div>
                    <div class="text-gray-700">我不敢相信！</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">极致感叹</div>
                    <div class="keyword text-lg mb-1">Mind-blowing!</div>
                    <div class="text-sm text-gray-600 mb-1">/maɪnd ˈbloʊɪŋ/</div>
                    <div class="text-gray-700">令人震撼！</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">逗号的微妙作用</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">强调对比</div>
                    <div class="keyword text-lg mb-1">She is smart, not just pretty.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi ɪz smɑːrt nɑːt dʒʌst ˈprɪti/</div>
                    <div class="text-gray-700">她很聪明，不仅仅是漂亮。</div>
                </div>

                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">表示递进</div>
                    <div class="keyword text-lg mb-1">He studied hard, worked diligently, and achieved success.</div>
                    <div class="text-sm text-gray-600 mb-1">/hi ˈstʌdid hɑːrd wɜːrkt ˈdɪlədʒəntli ænd əˈtʃivd səkˈses/</div>
                    <div class="text-gray-700">他努力学习，勤奋工作，并取得了成功。</div>
                </div>

                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">表示让步</div>
                    <div class="keyword text-lg mb-1">Although tired, she continued working.</div>
                    <div class="text-sm text-gray-600 mb-1">/ɔːlˈðoʊ ˈtaɪərd ʃi kənˈtɪnjud ˈwɜːrkɪŋ/</div>
                    <div class="text-gray-700">尽管疲惫，她继续工作。</div>
                </div>

                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">表示原因</div>
                    <div class="keyword text-lg mb-1">Being late, he missed the meeting.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈbiɪŋ leɪt hi mɪst ðə ˈmiːtɪŋ/</div>
                    <div class="text-gray-700">由于迟到，他错过了会议。</div>
                </div>
            </div>
        </section>

        <!-- 标点符号的现代应用趋势 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">标点符号的现代应用趋势</h2>
            <p class="text-gray-700 mb-6">随着数字化交流的普及，标点符号的使用也在发生变化。了解这些现代趋势有助于更好地适应当代英语交流环境。</p>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">电子邮件中的标点</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">主题行</div>
                    <div class="keyword text-lg mb-1">Meeting Tomorrow - Please Confirm</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈmiːtɪŋ təˈmɑːroʊ pliːz kənˈfɜːrm/</div>
                    <div class="text-gray-700">明天的会议 - 请确认</div>
                </div>

                <div class="card bg-red-50 border border-red-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">简洁开头</div>
                    <div class="keyword text-lg mb-1">Hi Sarah,</div>
                    <div class="text-sm text-gray-600 mb-1">/haɪ ˈserə/</div>
                    <div class="text-gray-700">嗨，莎拉，</div>
                </div>

                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">友好结尾</div>
                    <div class="keyword text-lg mb-1">Best,</div>
                    <div class="text-sm text-gray-600 mb-1">/best/</div>
                    <div class="text-gray-700">祝好，</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">感谢表达</div>
                    <div class="keyword text-lg mb-1">Thanks for your time!</div>
                    <div class="text-sm text-gray-600 mb-1">/θæŋks fɔːr jʊr taɪm/</div>
                    <div class="text-gray-700">感谢您的时间！</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">社交媒体中的标点</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">简化表达</div>
                    <div class="keyword text-lg mb-1">Great day today</div>
                    <div class="text-sm text-gray-600 mb-1">/ɡreɪt deɪ təˈdeɪ/</div>
                    <div class="text-gray-700">今天很棒</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">多重感叹</div>
                    <div class="keyword text-lg mb-1">So excited!!!</div>
                    <div class="text-sm text-gray-600 mb-1">/soʊ ɪkˈsaɪtɪd/</div>
                    <div class="text-gray-700">太兴奋了！！！</div>
                </div>

                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">疑问表达</div>
                    <div class="keyword text-lg mb-1">Really???</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈriːəli/</div>
                    <div class="text-gray-700">真的吗？？？</div>
                </div>

                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">省略表达</div>
                    <div class="keyword text-lg mb-1">Can't wait...</div>
                    <div class="text-sm text-gray-600 mb-1">/kænt weɪt/</div>
                    <div class="text-gray-700">等不及了...</div>
                </div>

                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">标签使用</div>
                    <div class="keyword text-lg mb-1">#amazing #day</div>
                    <div class="text-sm text-gray-600 mb-1">/əˈmeɪzɪŋ deɪ/</div>
                    <div class="text-gray-700">#令人惊叹 #一天</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">表情符号</div>
                    <div class="keyword text-lg mb-1">Great news! 😊</div>
                    <div class="text-sm text-gray-600 mb-1">/ɡreɪt nuz/</div>
                    <div class="text-gray-700">好消息！😊</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">即时消息中的标点</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-red-50 border border-red-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">快速回复</div>
                    <div class="keyword text-lg mb-1">ok</div>
                    <div class="text-sm text-gray-600 mb-1">/oʊˈkeɪ/</div>
                    <div class="text-gray-700">好的</div>
                </div>

                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">确认回复</div>
                    <div class="keyword text-lg mb-1">got it!</div>
                    <div class="text-sm text-gray-600 mb-1">/ɡɑːt ɪt/</div>
                    <div class="text-gray-700">明白了！</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">疑问回复</div>
                    <div class="keyword text-lg mb-1">really?</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈriːəli/</div>
                    <div class="text-gray-700">真的吗？</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">感谢回复</div>
                    <div class="keyword text-lg mb-1">thanks!</div>
                    <div class="text-sm text-gray-600 mb-1">/θæŋks/</div>
                    <div class="text-gray-700">谢谢！</div>
                </div>
            </div>
        </section>

        <!-- 标点符号的文学应用 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">标点符号在文学作品中的艺术运用</h2>
            <p class="text-gray-700 mb-6">在文学创作中，标点符号不仅是语法工具，更是表达情感、营造氛围、控制节奏的艺术手段。</p>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">营造悬念和紧张感</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">悬念营造</div>
                    <div class="keyword text-lg mb-1">The door slowly opened... Who was there?</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə dɔːr ˈsloʊli ˈoʊpənd hu wʌz ðer/</div>
                    <div class="text-gray-700">门慢慢打开了...谁在那里？</div>
                </div>

                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">紧张气氛</div>
                    <div class="keyword text-lg mb-1">Footsteps. Getting closer. Closer!</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈfʊtˌsteps ˈɡetɪŋ ˈkloʊsər ˈkloʊsər/</div>
                    <div class="text-gray-700">脚步声。越来越近。更近了！</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">表达内心独白</h3>
            <div class="grid grid-cols-1 gap-4 mb-6">
                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">思考过程</div>
                    <div class="keyword text-lg mb-1">Should I go? Maybe not... But what if I miss something important?</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃʊd aɪ ɡoʊ ˈmeɪbi nɑːt bʌt wʌt ɪf aɪ mɪs ˈsʌmθɪŋ ɪmˈpɔːrtənt/</div>
                    <div class="text-gray-700">我应该去吗？也许不应该...但如果我错过了重要的事情怎么办？</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">描绘情感变化</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">情感递进</div>
                    <div class="keyword text-lg mb-1">She was happy. No, she was ecstatic!</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi wʌz ˈhæpi noʊ ʃi wʌz ɪkˈstætɪk/</div>
                    <div class="text-gray-700">她很高兴。不，她欣喜若狂！</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">情感转折</div>
                    <div class="keyword text-lg mb-1">The news was good... or was it?</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə nuz wʌz ɡʊd ɔːr wʌz ɪt/</div>
                    <div class="text-gray-700">这个消息是好的...还是不好？</div>
                </div>
            </div>
        </section>

        <!-- 标点符号的跨文化理解 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">标点符号的跨文化理解</h2>
            <p class="text-gray-700 mb-6">不同文化背景的人对标点符号的理解和使用可能存在差异。了解这些差异有助于更好的国际交流。</p>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">礼貌程度的表达</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-red-50 border border-red-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">非常礼貌</div>
                    <div class="keyword text-lg mb-1">Would you mind helping me, please?</div>
                    <div class="text-sm text-gray-600 mb-1">/wʊd ju maɪnd ˈhelpɪŋ mi pliːz/</div>
                    <div class="text-gray-700">您介意帮助我吗，请？</div>
                </div>

                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">一般礼貌</div>
                    <div class="keyword text-lg mb-1">Could you help me?</div>
                    <div class="text-sm text-gray-600 mb-1">/kʊd ju help mi/</div>
                    <div class="text-gray-700">你能帮我吗？</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">直接请求</div>
                    <div class="keyword text-lg mb-1">Help me.</div>
                    <div class="text-sm text-gray-600 mb-1">/help mi/</div>
                    <div class="text-gray-700">帮我。</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">紧急请求</div>
                    <div class="keyword text-lg mb-1">Help me!</div>
                    <div class="text-sm text-gray-600 mb-1">/help mi/</div>
                    <div class="text-gray-700">帮我！</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">感谢表达</div>
                    <div class="keyword text-lg mb-1">Thank you so much!</div>
                    <div class="text-sm text-gray-600 mb-1">/θæŋk ju soʊ mʌtʃ/</div>
                    <div class="text-gray-700">非常感谢！</div>
                </div>

                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">道歉表达</div>
                    <div class="keyword text-lg mb-1">I'm terribly sorry!</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪm ˈterəbli ˈsɑːri/</div>
                    <div class="text-gray-700">我非常抱歉！</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">情感强度的文化差异</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">含蓄表达</div>
                    <div class="keyword text-lg mb-1">That's quite good.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðæts kwaɪt ɡʊd/</div>
                    <div class="text-gray-700">那相当不错。</div>
                </div>

                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">直接表达</div>
                    <div class="keyword text-lg mb-1">That's excellent!</div>
                    <div class="text-sm text-gray-600 mb-1">/ðæts ˈeksələnt/</div>
                    <div class="text-gray-700">那太棒了！</div>
                </div>
            </div>
        </section>

        <!-- 标点符号的实际练习场景 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">标点符号的实际应用场景</h2>
            <p class="text-gray-700 mb-6">通过具体的应用场景来理解和掌握标点符号的使用，能够更好地将理论知识转化为实际技能。</p>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">工作场景中的标点应用</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">会议安排</div>
                    <div class="keyword text-lg mb-1">Meeting scheduled for Monday, 2:00 p.m., Conference Room A.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈmiːtɪŋ ˈskedʒuld fɔːr ˈmʌndeɪ tu əˈklɑːk ˌpiː ˈem ˈkɑːnfərəns rum eɪ/</div>
                    <div class="text-gray-700">会议安排在周一下午2点，会议室A。</div>
                </div>

                <div class="card bg-red-50 border border-red-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">项目更新</div>
                    <div class="keyword text-lg mb-1">Project status: 75% complete, on schedule, within budget.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈprɑːdʒekt ˈsteɪtəs ˈsevənti faɪv pərˈsent kəmˈplit ɑːn ˈskedʒul wɪˈðɪn ˈbʌdʒət/</div>
                    <div class="text-gray-700">项目状态：75%完成，按计划进行，在预算内。</div>
                </div>

                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">紧急通知</div>
                    <div class="keyword text-lg mb-1">Urgent! System maintenance tonight from 11 p.m. to 3 a.m.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈɜːrdʒənt ˈsɪstəm ˈmeɪntənəns təˈnaɪt frʌm ɪˈlevən ˌpiː ˈem tu θri ˌeɪ ˈem/</div>
                    <div class="text-gray-700">紧急！今晚11点到凌晨3点系统维护。</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">成果汇报</div>
                    <div class="keyword text-lg mb-1">Excellent results! Sales increased by 25%, customer satisfaction improved.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈeksələnt rɪˈzʌlts seɪlz ɪnˈkriːst baɪ ˈtwenti faɪv pərˈsent ˈkʌstəmər ˌsætɪsˈfækʃən ɪmˈpruvd/</div>
                    <div class="text-gray-700">优秀的结果！销售增长了25%，客户满意度提高了。</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">学术写作中的标点应用</h3>
            <div class="grid grid-cols-1 gap-4 mb-6">
                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">研究结论</div>
                    <div class="keyword text-lg mb-1">The study demonstrates that regular exercise improves cognitive function, enhances memory, and reduces stress levels.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈstʌdi ˈdemənˌstreɪts ðæt ˈreɡjələr ˈeksərˌsaɪz ɪmˈpruvz ˈkɑːɡnətɪv ˈfʌŋkʃən ɪnˈhænsəz ˈmeməri ænd rɪˈdusəz stres ˈlevəlz/</div>
                    <div class="text-gray-700">研究表明，定期锻炼能改善认知功能，增强记忆力，并降低压力水平。</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">数据分析</div>
                    <div class="keyword text-lg mb-1">The data reveals significant correlations: temperature affects plant growth (r=0.85), rainfall influences yield (r=0.72).</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈdeɪtə rɪˈvilz sɪɡˈnɪfɪkənt ˌkɔːrəˈleɪʃənz ˈtemprətʃər əˈfekts plænt ɡroʊθ ˈreɪnˌfɔːl ˈɪnfluənsəz jild/</div>
                    <div class="text-gray-700">数据显示显著相关性：温度影响植物生长(r=0.85)，降雨影响产量(r=0.72)。</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">日常交流中的标点应用</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">约会安排</div>
                    <div class="keyword text-lg mb-1">Dinner at 7? Italian restaurant on Main Street.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈdɪnər æt ˈsevən ɪˈtæljən ˈrestərɑːnt ɑːn meɪn striːt/</div>
                    <div class="text-gray-700">7点吃晚饭？主街上的意大利餐厅。</div>
                </div>

                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">购物清单</div>
                    <div class="keyword text-lg mb-1">Need: milk, bread, eggs, and cheese.</div>
                    <div class="text-sm text-gray-600 mb-1">/niːd mɪlk bred eɡz ænd tʃiːz/</div>
                    <div class="text-gray-700">需要：牛奶、面包、鸡蛋和奶酪。</div>
                </div>

                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">天气评论</div>
                    <div class="keyword text-lg mb-1">Beautiful day today! Perfect for a walk.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈbjuːtɪfəl deɪ təˈdeɪ ˈpɜːrfɪkt fɔːr ə wɔːk/</div>
                    <div class="text-gray-700">今天天气真好！很适合散步。</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">旅行计划</div>
                    <div class="keyword text-lg mb-1">Trip itinerary: Paris (3 days), Rome (4 days), Barcelona (3 days).</div>
                    <div class="text-sm text-gray-600 mb-1">/trɪp aɪˈtɪnəˌreri ˈperɪs θri deɪz roʊm fɔːr deɪz ˌbɑːrsəˈloʊnə θri deɪz/</div>
                    <div class="text-gray-700">旅行行程：巴黎(3天)，罗马(4天)，巴塞罗那(3天)。</div>
                </div>

                <div class="card bg-red-50 border border-red-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">健康提醒</div>
                    <div class="keyword text-lg mb-1">Remember: drink water, exercise daily, get enough sleep!</div>
                    <div class="text-sm text-gray-600 mb-1">/rɪˈmembər drɪŋk ˈwɔːtər ˈeksərˌsaɪz ˈdeɪli ɡet ɪˈnʌf slip/</div>
                    <div class="text-gray-700">记住：喝水，每天锻炼，充足睡眠！</div>
                </div>

                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">节日祝福</div>
                    <div class="keyword text-lg mb-1">Happy Birthday! Hope you have a wonderful day.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈhæpi ˈbɜːrθˌdeɪ hoʊp ju hæv ə ˈwʌndərfəl deɪ/</div>
                    <div class="text-gray-700">生日快乐！希望你度过美好的一天。</div>
                </div>
            </div>
        </section>

        <!-- 标点符号的高级技巧总结 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">标点符号掌握的关键要点</h2>
            <p class="text-gray-700 mb-6">掌握标点符号需要理解其基本功能、语法规则和语境应用。以下是一些关键要点和实用技巧。</p>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">句号的掌握要点</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">完整性原则</div>
                    <div class="keyword text-lg mb-1">Every complete thought needs a period.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈevri kəmˈplit θɔːt niːdz ə ˈpɪriəd/</div>
                    <div class="text-gray-700">每个完整的思想都需要句号。</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">缩写规则</div>
                    <div class="keyword text-lg mb-1">Abbreviations usually end with periods.</div>
                    <div class="text-sm text-gray-600 mb-1">/əˌbriviˈeɪʃənz ˈjuːʒuəli end wɪð ˈpɪriədz/</div>
                    <div class="text-gray-700">缩写通常以句号结尾。</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">逗号的掌握要点</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">分隔功能</div>
                    <div class="keyword text-lg mb-1">Commas separate elements in a series.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈkɑːməz ˈsepəˌreɪt ˈeləmənts ɪn ə ˈsɪriz/</div>
                    <div class="text-gray-700">逗号分隔系列中的元素。</div>
                </div>

                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">连接功能</div>
                    <div class="keyword text-lg mb-1">Use commas before coordinating conjunctions.</div>
                    <div class="text-sm text-gray-600 mb-1">/juz ˈkɑːməz bɪˈfɔːr koʊˈɔːrdəˌneɪtɪŋ kənˈdʒʌŋkʃənz/</div>
                    <div class="text-gray-700">在并列连词前使用逗号。</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">问号的掌握要点</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">疑问识别</div>
                    <div class="keyword text-lg mb-1">Questions always end with question marks.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈkwestʃənz ˈɔːlˌweɪz end wɪð ˈkwestʃən mɑːrks/</div>
                    <div class="text-gray-700">疑问句总是以问号结尾。</div>
                </div>

                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">语调判断</div>
                    <div class="keyword text-lg mb-1">Rising intonation indicates questions.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈraɪzɪŋ ˌɪntəˈneɪʃən ˈɪndəˌkeɪts ˈkwestʃənz/</div>
                    <div class="text-gray-700">上升语调表示疑问。</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">感叹号的掌握要点</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">情感表达</div>
                    <div class="keyword text-lg mb-1">Exclamation marks show strong emotions.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˌekskləˈmeɪʃən mɑːrks ʃoʊ strɔːŋ ɪˈmoʊʃənz/</div>
                    <div class="text-gray-700">感叹号表示强烈情感。</div>
                </div>

                <div class="card bg-red-50 border border-red-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">适度使用</div>
                    <div class="keyword text-lg mb-1">Use exclamation marks sparingly for impact.</div>
                    <div class="text-sm text-gray-600 mb-1">/juz ˌekskləˈmeɪʃən mɑːrks ˈsperɪŋli fɔːr ˈɪmpækt/</div>
                    <div class="text-gray-700">适度使用感叹号以产生影响。</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-4 text-gray-800">综合应用技巧</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">朗读检验</div>
                    <div class="keyword text-lg mb-1">Read aloud to check punctuation.</div>
                    <div class="text-sm text-gray-600 mb-1">/riːd əˈlaʊd tu tʃek ˌpʌŋktʃuˈeɪʃən/</div>
                    <div class="text-gray-700">大声朗读检查标点。</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">语境考虑</div>
                    <div class="keyword text-lg mb-1">Consider context when punctuating.</div>
                    <div class="text-sm text-gray-600 mb-1">/kənˈsɪdər ˈkɑːntekst wen ˈpʌŋktʃuˌeɪtɪŋ/</div>
                    <div class="text-gray-700">标点时考虑语境。</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">多练习</div>
                    <div class="keyword text-lg mb-1">Practice makes punctuation perfect.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈpræktəs meɪks ˌpʌŋktʃuˈeɪʃən ˈpɜːrfɪkt/</div>
                    <div class="text-gray-700">练习使标点完美。</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">规则理解</div>
                    <div class="keyword text-lg mb-1">Understand rules before breaking them.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˌʌndərˈstænd rulz bɪˈfɔːr ˈbreɪkɪŋ ðem/</div>
                    <div class="text-gray-700">打破规则前先理解规则。</div>
                </div>

                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">一致性</div>
                    <div class="keyword text-lg mb-1">Be consistent in your punctuation style.</div>
                    <div class="text-sm text-gray-600 mb-1">/bi kənˈsɪstənt ɪn jʊr ˌpʌŋktʃuˈeɪʃən staɪl/</div>
                    <div class="text-gray-700">在标点风格上保持一致。</div>
                </div>

                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4">
                    <div class="text-sm text-gray-500 mb-1">清晰表达</div>
                    <div class="keyword text-lg mb-1">Punctuation should clarify, not confuse.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˌpʌŋktʃuˈeɪʃən ʃʊd ˈklærəˌfaɪ nɑːt kənˈfjuz/</div>
                    <div class="text-gray-700">标点应该澄清而不是混淆。</div>
                </div>
            </div>
        </section>
    </div>
</body>
</html>
