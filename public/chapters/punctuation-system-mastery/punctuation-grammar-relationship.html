<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>标点与语法</title>
    <script src="/static/tailwindcss-3.4.17.js"></script>
    <style>
        .keyword {
            color: #3d74ed;
            font-weight: bold;
        }
    </style>
</head>
<body class="bg-white">
    <div class="p-6">
        <!-- 句号的语法作用 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">句号的语法作用</h2>
            <p class="text-gray-700 mb-4">句号是最基本的句子终止符号，它标志着一个完整陈述句的结束。句号的使用直接影响句子的完整性和语义的清晰度。</p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">完整陈述句</div>
                    <div class="keyword text-lg mb-1">She reads books every day.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi riːdz bʊks ˈɛvri deɪ/</div>
                    <div class="text-gray-700">她每天读书。</div>
                </div>
                
                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">复合句结束</div>
                    <div class="keyword text-lg mb-1">I went to the store, and I bought some milk.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ wɛnt tu ðə stɔr ænd aɪ bɔt sʌm mɪlk/</div>
                    <div class="text-gray-700">我去了商店，买了一些牛奶。</div>
                </div>
                
                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">缩写后的句号</div>
                    <div class="keyword text-lg mb-1">Dr. Smith works at the hospital.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈdɑktər smɪθ wɜrks æt ðə ˈhɑspɪtəl/</div>
                    <div class="text-gray-700">史密斯医生在医院工作。</div>
                </div>
                
                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">间接引语结束</div>
                    <div class="keyword text-lg mb-1">He said that he would come tomorrow.</div>
                    <div class="text-sm text-gray-600 mb-1">/hi sɛd ðæt hi wʊd kʌm təˈmɑroʊ/</div>
                    <div class="text-gray-700">他说他明天会来。</div>
                </div>
            </div>
        </section>

        <!-- 逗号的语法功能 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">逗号的语法功能</h2>
            <p class="text-gray-700 mb-4">逗号是最复杂的标点符号之一，它在句子结构中起着分隔、连接和澄清的作用。逗号的正确使用能够避免歧义，使句子结构更加清晰。</p>
            
            <h3 class="text-xl font-semibold mb-3 text-gray-800">并列成分分隔</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">并列名词</div>
                    <div class="keyword text-lg mb-1">I bought apples, oranges, and bananas.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ bɔt ˈæpəlz ˈɔrɪndʒɪz ænd bəˈnænəz/</div>
                    <div class="text-gray-700">我买了苹果、橙子和香蕉。</div>
                </div>
                
                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">并列形容词</div>
                    <div class="keyword text-lg mb-1">She is smart, kind, and beautiful.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi ɪz smɑrt kaɪnd ænd ˈbjutəfəl/</div>
                    <div class="text-gray-700">她聪明、善良又美丽。</div>
                </div>
                
                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">并列动词</div>
                    <div class="keyword text-lg mb-1">He runs, jumps, and swims every day.</div>
                    <div class="text-sm text-gray-600 mb-1">/hi rʌnz dʒʌmps ænd swɪmz ˈɛvri deɪ/</div>
                    <div class="text-gray-700">他每天跑步、跳跃和游泳。</div>
                </div>
            </div>
            
            <h3 class="text-xl font-semibold mb-3 text-gray-800">从句分隔</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">时间状语从句</div>
                    <div class="keyword text-lg mb-1">When it rains, I stay inside.</div>
                    <div class="text-sm text-gray-600 mb-1">/wɛn ɪt reɪnz aɪ steɪ ɪnˈsaɪd/</div>
                    <div class="text-gray-700">下雨时，我待在室内。</div>
                </div>
                
                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">条件状语从句</div>
                    <div class="keyword text-lg mb-1">If you study hard, you will succeed.</div>
                    <div class="text-sm text-gray-600 mb-1">/ɪf ju ˈstʌdi hɑrd ju wɪl səkˈsid/</div>
                    <div class="text-gray-700">如果你努力学习，你会成功。</div>
                </div>
                
                <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">非限制性定语从句</div>
                    <div class="keyword text-lg mb-1">My brother, who lives in New York, is a doctor.</div>
                    <div class="text-sm text-gray-600 mb-1">/maɪ ˈbrʌðər hu lɪvz ɪn nu jɔrk ɪz ə ˈdɑktər/</div>
                    <div class="text-gray-700">我住在纽约的哥哥是医生。</div>
                </div>
                
                <div class="card bg-lime-50 border border-lime-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">插入语</div>
                    <div class="keyword text-lg mb-1">The weather, unfortunately, is terrible today.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈwɛðər ʌnˈfɔrtʃənətli ɪz ˈtɛrəbəl təˈdeɪ/</div>
                    <div class="text-gray-700">不幸的是，今天天气很糟糕。</div>
                </div>
            </div>
        </section>

        <!-- 问号的语法意义 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">问号的语法意义</h2>
            <p class="text-gray-700 mb-4">问号不仅标志着疑问句的结束，更重要的是它改变了句子的语调和语义功能，将陈述转化为询问。</p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-violet-50 border border-violet-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">一般疑问句</div>
                    <div class="keyword text-lg mb-1">Are you coming to the party?</div>
                    <div class="text-sm text-gray-600 mb-1">/ɑr ju ˈkʌmɪŋ tu ðə ˈpɑrti/</div>
                    <div class="text-gray-700">你要来参加聚会吗？</div>
                </div>
                
                <div class="card bg-rose-50 border border-rose-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">特殊疑问句</div>
                    <div class="keyword text-lg mb-1">What time does the movie start?</div>
                    <div class="text-sm text-gray-600 mb-1">/wʌt taɪm dʌz ðə ˈmuvi stɑrt/</div>
                    <div class="text-gray-700">电影什么时候开始？</div>
                </div>
                
                <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">选择疑问句</div>
                    <div class="keyword text-lg mb-1">Do you prefer tea or coffee?</div>
                    <div class="text-sm text-gray-600 mb-1">/du ju prɪˈfɜr ti ɔr ˈkɔfi/</div>
                    <div class="text-gray-700">你喜欢茶还是咖啡？</div>
                </div>
                
                <div class="card bg-amber-50 border border-amber-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">反意疑问句</div>
                    <div class="keyword text-lg mb-1">You like music, don't you?</div>
                    <div class="text-sm text-gray-600 mb-1">/ju laɪk ˈmjuzɪk doʊnt ju/</div>
                    <div class="text-gray-700">你喜欢音乐，不是吗？</div>
                </div>
            </div>
        </section>

        <!-- 感叹号的语法表达 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">感叹号的语法表达</h2>
            <p class="text-gray-700 mb-4">感叹号表达强烈的情感或语气，它改变了句子的语调强度，使普通陈述句变成感叹句，增强表达效果。</p>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">惊讶感叹</div>
                    <div class="keyword text-lg mb-1">What a beautiful day!</div>
                    <div class="text-sm text-gray-600 mb-1">/wʌt ə ˈbjutəfəl deɪ/</div>
                    <div class="text-gray-700">多么美好的一天！</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">命令语气</div>
                    <div class="keyword text-lg mb-1">Stop right there!</div>
                    <div class="text-sm text-gray-600 mb-1">/stɑp raɪt ðɛr/</div>
                    <div class="text-gray-700">就在那里停下！</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">强烈情感</div>
                    <div class="keyword text-lg mb-1">I can't believe it!</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ kænt bɪˈliv ɪt/</div>
                    <div class="text-gray-700">我简直不敢相信！</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">祝愿表达</div>
                    <div class="keyword text-lg mb-1">Good luck!</div>
                    <div class="text-sm text-gray-600 mb-1">/gʊd lʌk/</div>
                    <div class="text-gray-700">祝你好运！</div>
                </div>

                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">警告语气</div>
                    <div class="keyword text-lg mb-1">Watch out!</div>
                    <div class="text-sm text-gray-600 mb-1">/wɑtʃ aʊt/</div>
                    <div class="text-gray-700">小心！</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">赞美表达</div>
                    <div class="keyword text-lg mb-1">Excellent work!</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈɛksələnt wɜrk/</div>
                    <div class="text-gray-700">出色的工作！</div>
                </div>
            </div>
        </section>

        <!-- 分号的语法连接 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">分号的语法连接</h2>
            <p class="text-gray-700 mb-4">分号在语法上起着连接相关独立句子的作用，它比逗号更强，比句号更弱，用于表示句子间的紧密逻辑关系。</p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">并列关系</div>
                    <div class="keyword text-lg mb-1">She loves reading; he prefers watching movies.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi lʌvz ˈridɪŋ hi prɪˈfɜrz ˈwɑtʃɪŋ ˈmuviz/</div>
                    <div class="text-gray-700">她喜欢读书；他更喜欢看电影。</div>
                </div>

                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">因果关系</div>
                    <div class="keyword text-lg mb-1">It was raining heavily; therefore, we stayed home.</div>
                    <div class="text-sm text-gray-600 mb-1">/ɪt wʌz ˈreɪnɪŋ ˈhɛvəli ˈðɛrfɔr wi steɪd hoʊm/</div>
                    <div class="text-gray-700">雨下得很大；因此，我们待在家里。</div>
                </div>

                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">对比关系</div>
                    <div class="keyword text-lg mb-1">Summer is hot; winter is cold.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈsʌmər ɪz hɑt ˈwɪntər ɪz koʊld/</div>
                    <div class="text-gray-700">夏天炎热；冬天寒冷。</div>
                </div>

                <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">复杂列举</div>
                    <div class="keyword text-lg mb-1">We visited Paris, France; Rome, Italy; and Madrid, Spain.</div>
                    <div class="text-sm text-gray-600 mb-1">/wi ˈvɪzɪtəd ˈpɛrɪs fræns roʊm ˈɪtəli ænd məˈdrɪd speɪn/</div>
                    <div class="text-gray-700">我们访问了法国巴黎、意大利罗马和西班牙马德里。</div>
                </div>
            </div>
        </section>

        <!-- 冒号的语法引导 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">冒号的语法引导</h2>
            <p class="text-gray-700 mb-4">冒号在语法上起引导作用，它预示着后面将出现解释、列举、引用或总结，建立前后文的逻辑关系。</p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-violet-50 border border-violet-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">引导列举</div>
                    <div class="keyword text-lg mb-1">I need three things: milk, bread, and eggs.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ nid θri θɪŋz mɪlk brɛd ænd ɛgz/</div>
                    <div class="text-gray-700">我需要三样东西：牛奶、面包和鸡蛋。</div>
                </div>

                <div class="card bg-rose-50 border border-rose-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">引导解释</div>
                    <div class="keyword text-lg mb-1">The reason is simple: he forgot to set his alarm.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈrizən ɪz ˈsɪmpəl hi fərˈgɑt tu sɛt hɪz əˈlɑrm/</div>
                    <div class="text-gray-700">原因很简单：他忘记设置闹钟了。</div>
                </div>

                <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">引导引用</div>
                    <div class="keyword text-lg mb-1">She said: "I will be there at five."</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi sɛd aɪ wɪl bi ðɛr æt faɪv/</div>
                    <div class="text-gray-700">她说："我五点会到那里。"</div>
                </div>

                <div class="card bg-amber-50 border border-amber-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">时间表达</div>
                    <div class="keyword text-lg mb-1">The meeting starts at 2:30 PM.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈmitɪŋ stɑrts æt tu ˈθɜrti pi ɛm/</div>
                    <div class="text-gray-700">会议下午2:30开始。</div>
                </div>
            </div>
        </section>

        <!-- 引号的语法界定 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">引号的语法界定</h2>
            <p class="text-gray-700 mb-4">引号在语法中起着界定作用，它明确标示直接引语、特殊用法的词汇，以及需要特别强调的内容，改变词语的语法功能。</p>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">直接引语</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">简单引语</div>
                    <div class="keyword text-lg mb-1">He said, "I am tired."</div>
                    <div class="text-sm text-gray-600 mb-1">/hi sɛd aɪ æm ˈtaɪərd/</div>
                    <div class="text-gray-700">他说："我累了。"</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">疑问引语</div>
                    <div class="keyword text-lg mb-1">She asked, "Where are you going?"</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi æskt wɛr ɑr ju ˈgoʊɪŋ/</div>
                    <div class="text-gray-700">她问："你要去哪里？"</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">感叹引语</div>
                    <div class="keyword text-lg mb-1">They shouted, "Help us!"</div>
                    <div class="text-sm text-gray-600 mb-1">/ðeɪ ˈʃaʊtəd hɛlp ʌs/</div>
                    <div class="text-gray-700">他们喊道："救救我们！"</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">中断引语</div>
                    <div class="keyword text-lg mb-1">"I think," he said, "we should leave now."</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ θɪŋk hi sɛd wi ʃʊd liv naʊ/</div>
                    <div class="text-gray-700">"我认为，"他说，"我们现在应该离开。"</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">特殊用法标记</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">讽刺用法</div>
                    <div class="keyword text-lg mb-1">That was a "great" idea.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðæt wʌz ə greɪt aɪˈdiə/</div>
                    <div class="text-gray-700">那真是个"好"主意。</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">术语标记</div>
                    <div class="keyword text-lg mb-1">The word "love" has many meanings.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə wɜrd lʌv hæz ˈmɛni ˈminɪŋz/</div>
                    <div class="text-gray-700">"爱"这个词有很多含义。</div>
                </div>

                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">标题引用</div>
                    <div class="keyword text-lg mb-1">I read "To Kill a Mockingbird" last week.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ rɛd tu kɪl ə ˈmɑkɪŋˌbɜrd læst wik/</div>
                    <div class="text-gray-700">我上周读了《杀死一只知更鸟》。</div>
                </div>
            </div>
        </section>

        <!-- 破折号的语法功能 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">破折号的语法功能</h2>
            <p class="text-gray-700 mb-4">破折号在语法中具有多重功能，它可以表示突然的转折、解释说明、强调或中断，比逗号更强烈，比句号更灵活。</p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">突然转折</div>
                    <div class="keyword text-lg mb-1">I was going to call you—but then I forgot.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ wʌz ˈgoʊɪŋ tu kɔl ju bʌt ðɛn aɪ fərˈgɑt/</div>
                    <div class="text-gray-700">我本来要给你打电话——但后来忘了。</div>
                </div>

                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">解释说明</div>
                    <div class="keyword text-lg mb-1">My favorite color—blue—reminds me of the ocean.</div>
                    <div class="text-sm text-gray-600 mb-1">/maɪ ˈfeɪvərɪt ˈkʌlər blu rɪˈmaɪndz mi ʌv ði ˈoʊʃən/</div>
                    <div class="text-gray-700">我最喜欢的颜色——蓝色——让我想起大海。</div>
                </div>

                <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">强调内容</div>
                    <div class="keyword text-lg mb-1">There's only one thing I want—peace.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðɛrz ˈoʊnli wʌn θɪŋ aɪ wɑnt pis/</div>
                    <div class="text-gray-700">我只想要一样东西——和平。</div>
                </div>

                <div class="card bg-lime-50 border border-lime-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">对话中断</div>
                    <div class="keyword text-lg mb-1">"I think we should—" "No, wait!"</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ θɪŋk wi ʃʊd noʊ weɪt/</div>
                    <div class="text-gray-700">"我认为我们应该——""不，等等！"</div>
                </div>
            </div>
        </section>

        <!-- 省略号的语法暗示 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">省略号的语法暗示</h2>
            <p class="text-gray-700 mb-4">省略号在语法中表示未完成的思想、犹豫、沉默或省略，它改变了句子的完整性，创造出特殊的语法效果和语义暗示。</p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-violet-50 border border-violet-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">思想中断</div>
                    <div class="keyword text-lg mb-1">I was thinking that maybe...</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ wʌz ˈθɪŋkɪŋ ðæt ˈmeɪbi/</div>
                    <div class="text-gray-700">我在想也许...</div>
                </div>

                <div class="card bg-rose-50 border border-rose-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">犹豫表达</div>
                    <div class="keyword text-lg mb-1">Well... I'm not sure about that.</div>
                    <div class="text-sm text-gray-600 mb-1">/wɛl aɪm nɑt ʃʊr əˈbaʊt ðæt/</div>
                    <div class="text-gray-700">嗯...我对此不太确定。</div>
                </div>

                <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">内容省略</div>
                    <div class="keyword text-lg mb-1">The list includes apples, oranges...</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə lɪst ɪnˈkludz ˈæpəlz ˈɔrɪndʒɪz/</div>
                    <div class="text-gray-700">清单包括苹果、橙子...</div>
                </div>

                <div class="card bg-amber-50 border border-amber-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">沉默暗示</div>
                    <div class="keyword text-lg mb-1">After the news, there was silence...</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈæftər ðə nuz ðɛr wʌz ˈsaɪləns/</div>
                    <div class="text-gray-700">听到消息后，一片沉默...</div>
                </div>
            </div>
        </section>

        <!-- 括号的语法补充 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">括号的语法补充</h2>
            <p class="text-gray-700 mb-4">括号在语法中提供补充信息，它们插入额外的解释、说明或细节，而不影响主句的语法结构和完整性。</p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">解释说明</div>
                    <div class="keyword text-lg mb-1">The CEO (Chief Executive Officer) will speak today.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə si i oʊ tʃif ɪgˈzɛkjətɪv ˈɔfəsər wɪl spik təˈdeɪ/</div>
                    <div class="text-gray-700">首席执行官（CEO）今天将发言。</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">附加信息</div>
                    <div class="keyword text-lg mb-1">She moved to Paris (her dream city) last year.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi muvd tu ˈpɛrɪs hər drim ˈsɪti læst jɪr/</div>
                    <div class="text-gray-700">她去年搬到了巴黎（她梦想中的城市）。</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">数字标注</div>
                    <div class="keyword text-lg mb-1">The temperature reached 35°C (95°F) yesterday.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈtɛmpərətʃər ritʃt ˈθɜrti faɪv dɪˈgriz ˈsɛlsiəs ˈjɛstərˌdeɪ/</div>
                    <div class="text-gray-700">昨天气温达到了35°C（95°F）。</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">引用来源</div>
                    <div class="keyword text-lg mb-1">The study (Smith, 2023) shows interesting results.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈstʌdi smɪθ tu ˈθaʊzənd ˈtwɛnti θri ʃoʊz ˈɪntrəstɪŋ rɪˈzʌlts/</div>
                    <div class="text-gray-700">这项研究（Smith, 2023）显示了有趣的结果。</div>
                </div>
            </div>
        </section>

        <!-- 撇号的语法标记 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">撇号的语法标记</h2>
            <p class="text-gray-700 mb-4">撇号在语法中有两个主要功能：表示所有格关系和标示缩写形式，它改变了词汇的语法形态和意义。</p>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">所有格形式</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">单数所有格</div>
                    <div class="keyword text-lg mb-1">The cat's toy is missing.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə kæts tɔɪ ɪz ˈmɪsɪŋ/</div>
                    <div class="text-gray-700">猫的玩具不见了。</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">复数所有格</div>
                    <div class="keyword text-lg mb-1">The students' books are on the table.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈstudənts bʊks ɑr ɑn ðə ˈteɪbəl/</div>
                    <div class="text-gray-700">学生们的书在桌子上。</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">不规则复数所有格</div>
                    <div class="keyword text-lg mb-1">The children's playground is new.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈtʃɪldrənz ˈpleɪˌgraʊnd ɪz nu/</div>
                    <div class="text-gray-700">孩子们的游乐场是新的。</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">复合所有格</div>
                    <div class="keyword text-lg mb-1">My sister-in-law's car is blue.</div>
                    <div class="text-sm text-gray-600 mb-1">/maɪ ˈsɪstər ɪn lɔz kɑr ɪz blu/</div>
                    <div class="text-gray-700">我嫂子的车是蓝色的。</div>
                </div>

                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">时间所有格</div>
                    <div class="keyword text-lg mb-1">Today's weather is perfect.</div>
                    <div class="text-sm text-gray-600 mb-1">/təˈdeɪz ˈwɛðər ɪz ˈpɜrfɪkt/</div>
                    <div class="text-gray-700">今天的天气很完美。</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">距离所有格</div>
                    <div class="keyword text-lg mb-1">It's a stone's throw away.</div>
                    <div class="text-sm text-gray-600 mb-1">/ɪts ə stoʊnz θroʊ əˈweɪ/</div>
                    <div class="text-gray-700">就在一箭之遥。</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">缩写形式</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">be动词缩写</div>
                    <div class="keyword text-lg mb-1">She's a teacher.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃiz ə ˈtitʃər/</div>
                    <div class="text-gray-700">她是一名教师。</div>
                </div>

                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">have动词缩写</div>
                    <div class="keyword text-lg mb-1">I've finished my homework.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪv ˈfɪnɪʃt maɪ ˈhoʊmˌwɜrk/</div>
                    <div class="text-gray-700">我已经完成了作业。</div>
                </div>

                <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">will动词缩写</div>
                    <div class="keyword text-lg mb-1">We'll see you tomorrow.</div>
                    <div class="text-sm text-gray-600 mb-1">/wil si ju təˈmɑroʊ/</div>
                    <div class="text-gray-700">我们明天见。</div>
                </div>

                <div class="card bg-lime-50 border border-lime-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">否定缩写</div>
                    <div class="keyword text-lg mb-1">I can't come today.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ kænt kʌm təˈdeɪ/</div>
                    <div class="text-gray-700">我今天不能来。</div>
                </div>

                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">would缩写</div>
                    <div class="keyword text-lg mb-1">I'd like some coffee.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪd laɪk sʌm ˈkɔfi/</div>
                    <div class="text-gray-700">我想要一些咖啡。</div>
                </div>

                <div class="card bg-rose-50 border border-rose-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">let us缩写</div>
                    <div class="keyword text-lg mb-1">Let's go to the park.</div>
                    <div class="text-sm text-gray-600 mb-1">/lɛts goʊ tu ðə pɑrk/</div>
                    <div class="text-gray-700">我们去公园吧。</div>
                </div>
            </div>
        </section>

        <!-- 连字符的语法连接 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">连字符的语法连接</h2>
            <p class="text-gray-700 mb-4">连字符在语法中起连接作用，它将两个或多个词连接成复合词，或在行末进行单词分割，改变词汇的语法结构和意义。</p>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">复合形容词</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">数字复合词</div>
                    <div class="keyword text-lg mb-1">She is twenty-five years old.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi ɪz ˈtwɛnti faɪv jɪrz oʊld/</div>
                    <div class="text-gray-700">她二十五岁。</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">颜色复合词</div>
                    <div class="keyword text-lg mb-1">He wore a dark-blue shirt.</div>
                    <div class="text-sm text-gray-600 mb-1">/hi wɔr ə dɑrk blu ʃɜrt/</div>
                    <div class="text-gray-700">他穿了一件深蓝色的衬衫。</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">时间复合词</div>
                    <div class="keyword text-lg mb-1">It's a full-time job.</div>
                    <div class="text-sm text-gray-600 mb-1">/ɪts ə fʊl taɪm dʒɑb/</div>
                    <div class="text-gray-700">这是一份全职工作。</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">性质复合词</div>
                    <div class="keyword text-lg mb-1">She has a well-known reputation.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi hæz ə wɛl noʊn ˌrɛpjəˈteɪʃən/</div>
                    <div class="text-gray-700">她有很好的声誉。</div>
                </div>

                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">方向复合词</div>
                    <div class="keyword text-lg mb-1">The north-east wind is cold.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə nɔrθ ist wɪnd ɪz koʊld/</div>
                    <div class="text-gray-700">东北风很冷。</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">状态复合词</div>
                    <div class="keyword text-lg mb-1">He is self-confident.</div>
                    <div class="text-sm text-gray-600 mb-1">/hi ɪz sɛlf ˈkɑnfədənt/</div>
                    <div class="text-gray-700">他很自信。</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">复合名词</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">家庭关系</div>
                    <div class="keyword text-lg mb-1">My mother-in-law is visiting.</div>
                    <div class="text-sm text-gray-600 mb-1">/maɪ ˈmʌðər ɪn lɔ ɪz ˈvɪzətɪŋ/</div>
                    <div class="text-gray-700">我岳母来访。</div>
                </div>

                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">职业名称</div>
                    <div class="keyword text-lg mb-1">The co-worker helped me.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə koʊ ˈwɜrkər hɛlpt mi/</div>
                    <div class="text-gray-700">同事帮助了我。</div>
                </div>

                <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">物品名称</div>
                    <div class="keyword text-lg mb-1">I need a check-up at the doctor's.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ nid ə tʃɛk ʌp æt ðə ˈdɑktərz/</div>
                    <div class="text-gray-700">我需要去医生那里检查。</div>
                </div>

                <div class="card bg-lime-50 border border-lime-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">地点名称</div>
                    <div class="keyword text-lg mb-1">The drive-through is convenient.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə draɪv θru ɪz kənˈvinjənt/</div>
                    <div class="text-gray-700">免下车服务很方便。</div>
                </div>

                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">时间概念</div>
                    <div class="keyword text-lg mb-1">The follow-up meeting is tomorrow.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈfɑloʊ ʌp ˈmitɪŋ ɪz təˈmɑroʊ/</div>
                    <div class="text-gray-700">后续会议在明天。</div>
                </div>

                <div class="card bg-rose-50 border border-rose-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">抽象概念</div>
                    <div class="keyword text-lg mb-1">Self-control is important.</div>
                    <div class="text-sm text-gray-600 mb-1">/sɛlf kənˈtroʊl ɪz ɪmˈpɔrtənt/</div>
                    <div class="text-gray-700">自制力很重要。</div>
                </div>
            </div>
        </section>

        <!-- 标点符号的语义影响 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">标点符号的语义影响</h2>
            <p class="text-gray-700 mb-4">相同的词汇在不同标点符号的作用下，会产生完全不同的语义效果。标点符号不仅影响语法结构，更直接改变句子的意思和语调。</p>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">语调变化对比</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-violet-50 border border-violet-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">陈述语调</div>
                    <div class="keyword text-lg mb-1">You are coming to the party.</div>
                    <div class="text-sm text-gray-600 mb-1">/ju ɑr ˈkʌmɪŋ tu ðə ˈpɑrti/</div>
                    <div class="text-gray-700">你要来参加聚会。</div>
                </div>

                <div class="card bg-rose-50 border border-rose-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">疑问语调</div>
                    <div class="keyword text-lg mb-1">You are coming to the party?</div>
                    <div class="text-sm text-gray-600 mb-1">/ju ɑr ˈkʌmɪŋ tu ðə ˈpɑrti/</div>
                    <div class="text-gray-700">你要来参加聚会？</div>
                </div>

                <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">感叹语调</div>
                    <div class="keyword text-lg mb-1">You are coming to the party!</div>
                    <div class="text-sm text-gray-600 mb-1">/ju ɑr ˈkʌmɪŋ tu ðə ˈpɑrti/</div>
                    <div class="text-gray-700">你要来参加聚会！</div>
                </div>

                <div class="card bg-amber-50 border border-amber-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">犹豫语调</div>
                    <div class="keyword text-lg mb-1">You are coming to the party...</div>
                    <div class="text-sm text-gray-600 mb-1">/ju ɑr ˈkʌmɪŋ tu ðə ˈpɑrti/</div>
                    <div class="text-gray-700">你要来参加聚会...</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">语义重点转移</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">正常语序</div>
                    <div class="keyword text-lg mb-1">I love you.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ lʌv ju/</div>
                    <div class="text-gray-700">我爱你。</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">强调主语</div>
                    <div class="keyword text-lg mb-1">I—I love you.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ aɪ lʌv ju/</div>
                    <div class="text-gray-700">我——我爱你。</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">强调动作</div>
                    <div class="keyword text-lg mb-1">I love—really love—you.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ lʌv ˈrili lʌv ju/</div>
                    <div class="text-gray-700">我爱——真的爱——你。</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">强调对象</div>
                    <div class="keyword text-lg mb-1">I love you—only you.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ lʌv ju ˈoʊnli ju/</div>
                    <div class="text-gray-700">我爱你——只爱你。</div>
                </div>
            </div>
        </section>

        <!-- 复杂句式中的标点运用 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">复杂句式中的标点运用</h2>
            <p class="text-gray-700 mb-4">在复杂的句子结构中，标点符号的正确使用变得更加重要，它们帮助理清句子的层次结构，避免歧义，确保语义的准确传达。</p>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">多重从句结构</h3>
            <div class="grid grid-cols-1 gap-4 mb-6">
                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">嵌套从句</div>
                    <div class="keyword text-lg mb-1">The book that she recommended, which I bought yesterday, is excellent.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə bʊk ðæt ʃi ˌrɛkəˈmɛndəd wɪtʃ aɪ bɔt ˈjɛstərˌdeɪ ɪz ˈɛksələnt/</div>
                    <div class="text-gray-700">她推荐的那本书，我昨天买的，非常棒。</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">条件与结果</div>
                    <div class="keyword text-lg mb-1">If you study hard, which I know you will, you'll pass the exam.</div>
                    <div class="text-sm text-gray-600 mb-1">/ɪf ju ˈstʌdi hɑrd wɪtʃ aɪ noʊ ju wɪl jul pæs ði ɪgˈzæm/</div>
                    <div class="text-gray-700">如果你努力学习，我知道你会的，你就会通过考试。</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">时间与原因</div>
                    <div class="keyword text-lg mb-1">When I arrived, because the traffic was heavy, the meeting had already started.</div>
                    <div class="text-sm text-gray-600 mb-1">/wɛn aɪ əˈraɪvd bɪˈkɔz ðə ˈtræfɪk wʌz ˈhɛvi ðə ˈmitɪŋ hæd ɔlˈrɛdi ˈstɑrtəd/</div>
                    <div class="text-gray-700">当我到达时，因为交通拥堵，会议已经开始了。</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">并列与转折结构</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">复杂并列</div>
                    <div class="keyword text-lg mb-1">She studied medicine, worked as a doctor, and later became a researcher.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi ˈstʌdid ˈmɛdəsən wɜrkt æz ə ˈdɑktər ænd ˈleɪtər bɪˈkeɪm ə rɪˈsɜrtʃər/</div>
                    <div class="text-gray-700">她学医，当医生，后来成为研究员。</div>
                </div>

                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">对比转折</div>
                    <div class="keyword text-lg mb-1">He is young; however, he is very experienced.</div>
                    <div class="text-sm text-gray-600 mb-1">/hi ɪz jʌŋ haʊˈɛvər hi ɪz ˈvɛri ɪkˈspɪriənst/</div>
                    <div class="text-gray-700">他很年轻；然而，他很有经验。</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">递进关系</div>
                    <div class="keyword text-lg mb-1">Not only did she finish the project, but she also exceeded expectations.</div>
                    <div class="text-sm text-gray-600 mb-1">/nɑt ˈoʊnli dɪd ʃi ˈfɪnɪʃ ðə ˈprɑdʒɛkt bʌt ʃi ˈɔlsoʊ ɪkˈsidəd ˌɛkspɛkˈteɪʃənz/</div>
                    <div class="text-gray-700">她不仅完成了项目，还超出了预期。</div>
                </div>

                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">因果链条</div>
                    <div class="keyword text-lg mb-1">It rained heavily; therefore, the roads flooded; consequently, traffic stopped.</div>
                    <div class="text-sm text-gray-600 mb-1">/ɪt reɪnd ˈhɛvəli ˈðɛrfɔr ðə roʊdz ˈflʌdəd ˈkɑnsəkwəntli ˈtræfɪk stɑpt/</div>
                    <div class="text-gray-700">雨下得很大；因此，道路被淹；结果，交通停止了。</div>
                </div>
            </div>
        </section>

        <!-- 标点符号的修辞效果 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">标点符号的修辞效果</h2>
            <p class="text-gray-700 mb-4">标点符号不仅具有语法功能，还能产生强烈的修辞效果，增强表达的感染力和说服力，创造独特的语言风格。</p>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">节奏控制</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">急促节奏</div>
                    <div class="keyword text-lg mb-1">Run! Hide! Now!</div>
                    <div class="text-sm text-gray-600 mb-1">/rʌn haɪd naʊ/</div>
                    <div class="text-gray-700">跑！躲起来！现在！</div>
                </div>

                <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">缓慢节奏</div>
                    <div class="keyword text-lg mb-1">Slowly... carefully... he opened the door.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈsloʊli ˈkɛrfəli hi ˈoʊpənd ðə dɔr/</div>
                    <div class="text-gray-700">慢慢地...小心地...他打开了门。</div>
                </div>

                <div class="card bg-lime-50 border border-lime-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">停顿强调</div>
                    <div class="keyword text-lg mb-1">The answer is—no.</div>
                    <div class="text-sm text-gray-600 mb-1">/ði ˈænsər ɪz noʊ/</div>
                    <div class="text-gray-700">答案是——不。</div>
                </div>

                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">连续动作</div>
                    <div class="keyword text-lg mb-1">He jumped, ran, and escaped.</div>
                    <div class="text-sm text-gray-600 mb-1">/hi dʒʌmpt ræn ænd ɪˈskeɪpt/</div>
                    <div class="text-gray-700">他跳起来，跑了，逃脱了。</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">情感渲染</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-rose-50 border border-rose-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">惊喜表达</div>
                    <div class="keyword text-lg mb-1">Surprise! Happy birthday!</div>
                    <div class="text-sm text-gray-600 mb-1">/sərˈpraɪz ˈhæpi ˈbɜrθˌdeɪ/</div>
                    <div class="text-gray-700">惊喜！生日快乐！</div>
                </div>

                <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">悲伤情绪</div>
                    <div class="keyword text-lg mb-1">He's gone... forever...</div>
                    <div class="text-sm text-gray-600 mb-1">/hiz gɔn fərˈɛvər/</div>
                    <div class="text-gray-700">他走了...永远...</div>
                </div>

                <div class="card bg-amber-50 border border-amber-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">愤怒表达</div>
                    <div class="keyword text-lg mb-1">How dare you!</div>
                    <div class="text-sm text-gray-600 mb-1">/haʊ dɛr ju/</div>
                    <div class="text-gray-700">你怎么敢！</div>
                </div>

                <div class="card bg-violet-50 border border-violet-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">疑惑不解</div>
                    <div class="keyword text-lg mb-1">What...? How...? Why...?</div>
                    <div class="text-sm text-gray-600 mb-1">/wʌt haʊ waɪ/</div>
                    <div class="text-gray-700">什么...？怎么...？为什么...？</div>
                </div>

                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">期待心情</div>
                    <div class="keyword text-lg mb-1">Tomorrow will be the day—finally!</div>
                    <div class="text-sm text-gray-600 mb-1">/təˈmɑroʊ wɪl bi ðə deɪ ˈfaɪnəli/</div>
                    <div class="text-gray-700">明天就是那一天——终于！</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">温柔语调</div>
                    <div class="keyword text-lg mb-1">Sleep well, my dear.</div>
                    <div class="text-sm text-gray-600 mb-1">/slip wɛl maɪ dɪr/</div>
                    <div class="text-gray-700">睡个好觉，亲爱的。</div>
                </div>
            </div>
        </section>

        <!-- 标点符号的语法歧义消除 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">标点符号的语法歧义消除</h2>
            <p class="text-gray-700 mb-4">标点符号在消除语法歧义方面发挥着关键作用，正确的标点使用能够明确句子的结构关系，避免误解。</p>

            <div class="grid grid-cols-1 gap-4 mb-6">
                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">修饰关系歧义</div>
                    <div class="keyword text-lg mb-1">The man who was walking quickly crossed the street.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə mæn hu wʌz ˈwɔkɪŋ ˈkwɪkli krɔst ðə strit/</div>
                    <div class="text-gray-700">那个走得很快的人过了马路。</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">修饰关系明确</div>
                    <div class="keyword text-lg mb-1">The man, who was walking, quickly crossed the street.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə mæn hu wʌz ˈwɔkɪŋ ˈkwɪkli krɔst ðə strit/</div>
                    <div class="text-gray-700">那个正在走路的人，快速地过了马路。</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">并列歧义</div>
                    <div class="keyword text-lg mb-1">I invited my parents John and Mary.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ ɪnˈvaɪtəd maɪ ˈpɛrənts dʒɑn ænd ˈmɛri/</div>
                    <div class="text-gray-700">我邀请了我的父母约翰和玛丽。</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">并列明确</div>
                    <div class="keyword text-lg mb-1">I invited my parents, John and Mary.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ ɪnˈvaɪtəd maɪ ˈpɛrənts dʒɑn ænd ˈmɛri/</div>
                    <div class="text-gray-700">我邀请了我的父母，约翰和玛丽。</div>
                </div>
            </div>
        </section>

        <!-- 标点符号在不同文体中的应用 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">标点符号在不同文体中的应用</h2>
            <p class="text-gray-700 mb-4">不同的文体对标点符号的使用有不同的要求和特点，掌握这些差异有助于在各种语境中正确运用标点符号。</p>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">正式文体</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">学术写作</div>
                    <div class="keyword text-lg mb-1">The research indicates that climate change affects biodiversity.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə rɪˈsɜrtʃ ˈɪndəˌkeɪts ðæt ˈklaɪmət tʃeɪndʒ əˈfɛkts ˌbaɪoʊdaɪˈvɜrsəti/</div>
                    <div class="text-gray-700">研究表明气候变化影响生物多样性。</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">商务信函</div>
                    <div class="keyword text-lg mb-1">We are pleased to inform you that your application has been approved.</div>
                    <div class="text-sm text-gray-600 mb-1">/wi ɑr plizd tu ɪnˈfɔrm ju ðæt jʊr ˌæpləˈkeɪʃən hæz bin əˈpruvd/</div>
                    <div class="text-gray-700">我们很高兴通知您，您的申请已获批准。</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">法律文件</div>
                    <div class="keyword text-lg mb-1">The contract shall be effective from January 1, 2024.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈkɑntrækt ʃæl bi ɪˈfɛktɪv frʌm ˈdʒænjuˌɛri fɜrst tu ˈθaʊzənd ˈtwɛnti fɔr/</div>
                    <div class="text-gray-700">合同自2024年1月1日起生效。</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">新闻报道</div>
                    <div class="keyword text-lg mb-1">The president announced new policies during yesterday's conference.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈprɛzədənt əˈnaʊnst nu ˈpɑləsiz ˈdʊrɪŋ ˈjɛstərˌdeɪz ˈkɑnfərəns/</div>
                    <div class="text-gray-700">总统在昨天的会议上宣布了新政策。</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">非正式文体</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">日常对话</div>
                    <div class="keyword text-lg mb-1">Hey! How's it going?</div>
                    <div class="text-sm text-gray-600 mb-1">/heɪ haʊz ɪt ˈgoʊɪŋ/</div>
                    <div class="text-gray-700">嘿！怎么样？</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">个人日记</div>
                    <div class="keyword text-lg mb-1">Today was amazing... I can't believe what happened!</div>
                    <div class="text-sm text-gray-600 mb-1">/təˈdeɪ wʌz əˈmeɪzɪŋ aɪ kænt bɪˈliv wʌt ˈhæpənd/</div>
                    <div class="text-gray-700">今天太棒了...我简直不敢相信发生了什么！</div>
                </div>

                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">社交媒体</div>
                    <div class="keyword text-lg mb-1">Just finished my workout! Feeling great 💪</div>
                    <div class="text-sm text-gray-600 mb-1">/dʒʌst ˈfɪnɪʃt maɪ ˈwɜrkaʊt ˈfilɪŋ greɪt/</div>
                    <div class="text-gray-700">刚完成锻炼！感觉很棒💪</div>
                </div>

                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">朋友聊天</div>
                    <div class="keyword text-lg mb-1">Wanna grab some coffee later?</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈwɑnə græb sʌm ˈkɔfi ˈleɪtər/</div>
                    <div class="text-gray-700">待会儿想喝杯咖啡吗？</div>
                </div>
            </div>
        </section>

        <!-- 标点符号的语法演变 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">标点符号的语法演变</h2>
            <p class="text-gray-700 mb-4">标点符号的使用在历史发展中不断演变，现代英语中的标点规则反映了语言使用的变化和发展趋势。</p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">传统用法</div>
                    <div class="keyword text-lg mb-1">Dear Sir or Madam:</div>
                    <div class="text-sm text-gray-600 mb-1">/dɪr sɜr ɔr ˈmædəm/</div>
                    <div class="text-gray-700">尊敬的先生或女士：</div>
                </div>

                <div class="card bg-lime-50 border border-lime-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">现代用法</div>
                    <div class="keyword text-lg mb-1">Dear Sir or Madam,</div>
                    <div class="text-sm text-gray-600 mb-1">/dɪr sɜr ɔr ˈmædəm/</div>
                    <div class="text-gray-700">尊敬的先生或女士，</div>
                </div>

                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">数字表达传统</div>
                    <div class="keyword text-lg mb-1">It costs $1,000.00.</div>
                    <div class="text-sm text-gray-600 mb-1">/ɪt kɔsts wʌn ˈθaʊzənd ˈdɑlərz/</div>
                    <div class="text-gray-700">它花费1,000.00美元。</div>
                </div>

                <div class="card bg-rose-50 border border-rose-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">数字表达现代</div>
                    <div class="keyword text-lg mb-1">It costs $1000.</div>
                    <div class="text-sm text-gray-600 mb-1">/ɪt kɔsts wʌn ˈθaʊzənd ˈdɑlərz/</div>
                    <div class="text-gray-700">它花费1000美元。</div>
                </div>
            </div>
        </section>

        <!-- 标点符号的国际差异 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">标点符号的国际差异</h2>
            <p class="text-gray-700 mb-4">不同英语国家在标点符号使用上存在细微差异，了解这些差异有助于在国际交流中准确表达。</p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">引号使用差异</div>
                    <div class="keyword text-lg mb-1">He said, "I'm coming."</div>
                    <div class="text-sm text-gray-600 mb-1">/hi sɛd aɪm ˈkʌmɪŋ/</div>
                    <div class="text-gray-700">他说："我要来了。"</div>
                </div>

                <div class="card bg-amber-50 border border-amber-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">引号使用差异</div>
                    <div class="keyword text-lg mb-1">He said, 'I'm coming.'</div>
                    <div class="text-sm text-gray-600 mb-1">/hi sɛd aɪm ˈkʌmɪŋ/</div>
                    <div class="text-gray-700">他说：'我要来了。'</div>
                </div>

                <div class="card bg-violet-50 border border-violet-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">日期格式差异</div>
                    <div class="keyword text-lg mb-1">The meeting is on March 15, 2024.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈmitɪŋ ɪz ɑn mɑrtʃ ˈfɪfˈtinθ tu ˈθaʊzənd ˈtwɛnti fɔr/</div>
                    <div class="text-gray-700">会议在2024年3月15日。</div>
                </div>

                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">日期格式差异</div>
                    <div class="keyword text-lg mb-1">The meeting is on 15 March 2024.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈmitɪŋ ɪz ɑn ˈfɪfˈtinθ mɑrtʃ tu ˈθaʊzənd ˈtwɛnti fɔr/</div>
                    <div class="text-gray-700">会议在2024年3月15日。</div>
                </div>
            </div>
        </section>

        <!-- 标点符号的语法总结 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">标点符号的语法总结</h2>
            <p class="text-gray-700 mb-4">标点符号是英语语法体系中不可或缺的组成部分，它们通过调节句子结构、明确语义关系、控制语调节奏，实现准确而生动的语言表达。</p>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">结构功能</div>
                    <div class="keyword text-lg mb-1">Punctuation organizes sentence structure.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˌpʌŋktʃuˈeɪʃən ˈɔrgəˌnaɪzəz ˈsɛntəns ˈstrʌktʃər/</div>
                    <div class="text-gray-700">标点符号组织句子结构。</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">语义功能</div>
                    <div class="keyword text-lg mb-1">Punctuation clarifies meaning.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˌpʌŋktʃuˈeɪʃən ˈklɛrəˌfaɪz ˈminɪŋ/</div>
                    <div class="text-gray-700">标点符号澄清意义。</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">语调功能</div>
                    <div class="keyword text-lg mb-1">Punctuation controls rhythm and tone.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˌpʌŋktʃuˈeɪʃən kənˈtroʊlz ˈrɪðəm ænd toʊn/</div>
                    <div class="text-gray-700">标点符号控制节奏和语调。</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">修辞功能</div>
                    <div class="keyword text-lg mb-1">Punctuation enhances expression.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˌpʌŋktʃuˈeɪʃən ɪnˈhænsəz ɪkˈsprɛʃən/</div>
                    <div class="text-gray-700">标点符号增强表达效果。</div>
                </div>

                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">交际功能</div>
                    <div class="keyword text-lg mb-1">Punctuation facilitates communication.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˌpʌŋktʃuˈeɪʃən fəˈsɪləˌteɪts kəˌmjunəˈkeɪʃən/</div>
                    <div class="text-gray-700">标点符号促进交流。</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">文体功能</div>
                    <div class="keyword text-lg mb-1">Punctuation reflects writing style.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˌpʌŋktʃuˈeɪʃən rɪˈflɛkts ˈraɪtɪŋ staɪl/</div>
                    <div class="text-gray-700">标点符号反映写作风格。</div>
                </div>
            </div>
        </section>

        <!-- 标点符号的实际应用练习 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">标点符号的实际应用</h2>
            <p class="text-gray-700 mb-4">通过实际的句子示例，我们可以更好地理解标点符号在不同语境中的具体应用和语法作用。</p>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">商务写作中的标点应用</h3>
            <div class="grid grid-cols-1 gap-4 mb-6">
                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">会议邀请</div>
                    <div class="keyword text-lg mb-1">Dear colleagues, please join us for the quarterly meeting on Friday, March 15th, at 2:00 PM in Conference Room A.</div>
                    <div class="text-sm text-gray-600 mb-1">/dɪr ˈkɑligz pliz dʒɔɪn ʌs fɔr ðə ˈkwɔrtərli ˈmitɪŋ ɑn ˈfraɪdeɪ mɑrtʃ ˈfɪfˈtinθ æt tu oʊ ˈklɑk pi ɛm ɪn ˈkɑnfərəns rum eɪ/</div>
                    <div class="text-gray-700">亲爱的同事们，请参加我们3月15日星期五下午2点在A会议室举行的季度会议。</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">项目报告</div>
                    <div class="keyword text-lg mb-1">The project has three phases: planning, implementation, and evaluation; each phase requires careful attention to detail.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈprɑdʒɛkt hæz θri ˈfeɪzəz ˈplænɪŋ ˌɪmpləmənˈteɪʃən ænd ɪˌvæljuˈeɪʃən itʃ feɪz rɪˈkwaɪərz ˈkɛrfəl əˈtɛnʃən tu ˈditeɪl/</div>
                    <div class="text-gray-700">该项目有三个阶段：规划、实施和评估；每个阶段都需要仔细关注细节。</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">客户回复</div>
                    <div class="keyword text-lg mb-1">Thank you for your inquiry; however, we are unable to process your request at this time.</div>
                    <div class="text-sm text-gray-600 mb-1">/θæŋk ju fɔr jʊr ɪnˈkwaɪri haʊˈɛvər wi ɑr ʌnˈeɪbəl tu ˈprɑsɛs jʊr rɪˈkwɛst æt ðɪs taɪm/</div>
                    <div class="text-gray-700">感谢您的询问；但是，我们目前无法处理您的请求。</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">合同条款</div>
                    <div class="keyword text-lg mb-1">The agreement shall remain in effect until December 31, 2024, unless terminated earlier by either party.</div>
                    <div class="text-sm text-gray-600 mb-1">/ði əˈgrimənt ʃæl rɪˈmeɪn ɪn ɪˈfɛkt ənˈtɪl dɪˈsɛmbər ˈθɜrti fɜrst tu ˈθaʊzənd ˈtwɛnti fɔr ənˈlɛs ˈtɜrməˌneɪtəd ˈɜrliər baɪ ˈiðər ˈpɑrti/</div>
                    <div class="text-gray-700">协议将持续有效至2024年12月31日，除非任何一方提前终止。</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">学术写作中的标点应用</h3>
            <div class="grid grid-cols-1 gap-4 mb-6">
                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">研究引用</div>
                    <div class="keyword text-lg mb-1">According to Smith (2023), "Climate change poses significant challenges to global agriculture."</div>
                    <div class="text-sm text-gray-600 mb-1">/əˈkɔrdɪŋ tu smɪθ tu ˈθaʊzənd ˈtwɛnti θri ˈklaɪmət tʃeɪndʒ ˈpoʊzəz sɪgˈnɪfəkənt ˈtʃælɪndʒəz tu ˈgloʊbəl ˈægrɪˌkʌltʃər/</div>
                    <div class="text-gray-700">根据史密斯（2023）的说法，"气候变化对全球农业构成重大挑战。"</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">数据分析</div>
                    <div class="keyword text-lg mb-1">The results show three key findings: first, temperature increased by 2°C; second, rainfall decreased by 15%; third, crop yields fell by 20%.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə rɪˈzʌlts ʃoʊ θri ki ˈfaɪndɪŋz fɜrst ˈtɛmpərətʃər ɪnˈkrist baɪ tu dɪˈgriz ˈsɛlsiəs ˈsɛkənd ˈreɪnˌfɔl dɪˈkrist baɪ ˈfɪfˈtin pərˈsɛnt θɜrd krɑp jildz fɛl baɪ ˈtwɛnti pərˈsɛnt/</div>
                    <div class="text-gray-700">结果显示三个关键发现：首先，温度上升了2°C；其次，降雨量减少了15%；第三，作物产量下降了20%。</div>
                </div>

                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">假设陈述</div>
                    <div class="keyword text-lg mb-1">If the hypothesis is correct—and preliminary data suggests it is—then we can expect significant changes in the ecosystem.</div>
                    <div class="text-sm text-gray-600 mb-1">/ɪf ðə haɪˈpɑθəsəs ɪz kəˈrɛkt ænd prɪˈlɪməˌnɛri ˈdeɪtə səgˈdʒɛsts ɪt ɪz ðɛn wi kæn ɪkˈspɛkt sɪgˈnɪfəkənt ˈtʃeɪndʒəz ɪn ði ˈikosɪstəm/</div>
                    <div class="text-gray-700">如果假设是正确的——初步数据表明确实如此——那么我们可以预期生态系统会发生重大变化。</div>
                </div>

                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">结论总结</div>
                    <div class="keyword text-lg mb-1">In conclusion, the study demonstrates that environmental factors significantly impact agricultural productivity; therefore, immediate action is required.</div>
                    <div class="text-sm text-gray-600 mb-1">/ɪn kənˈkluʒən ðə ˈstʌdi ˈdɛmənˌstreɪts ðæt ɪnˌvaɪrənˈmɛntəl ˈfæktərz sɪgˈnɪfəkəntli ˈɪmpækt ˌægrɪˈkʌltʃərəl ˌproʊdʌkˈtɪvəti ˈðɛrfɔr ɪˈmidiət ˈækʃən ɪz rɪˈkwaɪərd/</div>
                    <div class="text-gray-700">总之，研究表明环境因素显著影响农业生产力；因此，需要立即采取行动。</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">创意写作中的标点应用</h3>
            <div class="grid grid-cols-1 gap-4 mb-6">
                <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">场景描述</div>
                    <div class="keyword text-lg mb-1">The old house stood silent in the moonlight—windows dark, doors locked, secrets hidden within its walls.</div>
                    <div class="text-sm text-gray-600 mb-1">/ði oʊld haʊs stʊd ˈsaɪlənt ɪn ðə ˈmunˌlaɪt ˈwɪndoʊz dɑrk dɔrz lɑkt ˈsikrəts ˈhɪdən wɪˈðɪn ɪts wɔlz/</div>
                    <div class="text-gray-700">老房子在月光下静静地矗立着——窗户漆黑，门紧锁，秘密隐藏在墙壁之中。</div>
                </div>

                <div class="card bg-lime-50 border border-lime-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">对话描写</div>
                    <div class="keyword text-lg mb-1">"Are you sure about this?" she whispered. "Absolutely," he replied, though his voice trembled slightly.</div>
                    <div class="text-sm text-gray-600 mb-1">/ɑr ju ʃʊr əˈbaʊt ðɪs ʃi ˈwɪspərd ˌæbsəˈlutli hi rɪˈplaɪd ðoʊ hɪz vɔɪs ˈtrɛmbəld ˈslaɪtli/</div>
                    <div class="text-gray-700">"你确定吗？"她低声说。"绝对确定，"他回答，尽管他的声音微微颤抖。</div>
                </div>

                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">情感表达</div>
                    <div class="keyword text-lg mb-1">Love... such a simple word, yet so complex in its meaning, so powerful in its impact.</div>
                    <div class="text-sm text-gray-600 mb-1">/lʌv sʌtʃ ə ˈsɪmpəl wɜrd jɛt soʊ ˈkɑmplɛks ɪn ɪts ˈminɪŋ soʊ ˈpaʊərfəl ɪn ɪts ˈɪmpækt/</div>
                    <div class="text-gray-700">爱...如此简单的一个词，却在意义上如此复杂，在影响上如此强大。</div>
                </div>

                <div class="card bg-rose-50 border border-rose-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">悬念营造</div>
                    <div class="keyword text-lg mb-1">The footsteps grew closer... closer... until they stopped right outside her door.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈfʊtˌstɛps gru ˈkloʊsər ˈkloʊsər ənˈtɪl ðeɪ stɑpt raɪt ˌaʊtˈsaɪd hər dɔr/</div>
                    <div class="text-gray-700">脚步声越来越近...越来越近...直到在她门外停下。</div>
                </div>
            </div>
        </section>

        <!-- 标点符号的语法细节 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">标点符号的语法细节</h2>
            <p class="text-gray-700 mb-4">掌握标点符号的细节用法对于准确表达至关重要，这些细节往往决定了句子的准确性和专业性。</p>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">逗号的特殊用法</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">地址分隔</div>
                    <div class="keyword text-lg mb-1">She lives in Boston, Massachusetts, USA.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi lɪvz ɪn ˈbɔstən ˌmæsəˈtʃusəts ju ɛs eɪ/</div>
                    <div class="text-gray-700">她住在美国马萨诸塞州波士顿。</div>
                </div>

                <div class="card bg-amber-50 border border-amber-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">称谓分隔</div>
                    <div class="keyword text-lg mb-1">John Smith, PhD, will give the lecture.</div>
                    <div class="text-sm text-gray-600 mb-1">/dʒɑn smɪθ pi eɪtʃ di wɪl gɪv ðə ˈlɛktʃər/</div>
                    <div class="text-gray-700">约翰·史密斯博士将进行讲座。</div>
                </div>

                <div class="card bg-violet-50 border border-violet-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">数字分隔</div>
                    <div class="keyword text-lg mb-1">The population is 1,234,567 people.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˌpɑpjəˈleɪʃən ɪz wʌn ˈmɪljən tu ˈhʌndrəd ˈθɜrti fɔr ˈθaʊzənd faɪv ˈhʌndrəd ˈsɪksti ˈsɛvən ˈpipəl/</div>
                    <div class="text-gray-700">人口是1,234,567人。</div>
                </div>

                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">对比分隔</div>
                    <div class="keyword text-lg mb-1">The more you practice, the better you become.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə mɔr ju ˈpræktəs ðə ˈbɛtər ju bɪˈkʌm/</div>
                    <div class="text-gray-700">你练习得越多，就变得越好。</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">分号的高级用法</h3>
            <div class="grid grid-cols-1 gap-4 mb-6">
                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">复杂列表分隔</div>
                    <div class="keyword text-lg mb-1">The conference attendees included Dr. Smith, Harvard University; Prof. Johnson, MIT; and Ms. Brown, Stanford University.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈkɑnfərəns əˈtɛndiz ɪnˈkludəd ˈdɑktər smɪθ ˈhɑrvərd ˌjunəˈvɜrsəti prəˈfɛsər ˈdʒɑnsən ɛm aɪ ti ænd mɪz braʊn ˈstænfərd ˌjunəˈvɜrsəti/</div>
                    <div class="text-gray-700">会议参与者包括哈佛大学的史密斯博士；麻省理工学院的约翰逊教授；以及斯坦福大学的布朗女士。</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">连接副词分隔</div>
                    <div class="keyword text-lg mb-1">The weather was terrible; nevertheless, we decided to continue our journey.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈwɛðər wʌz ˈtɛrəbəl ˌnɛvərðəˈlɛs wi dɪˈsaɪdəd tu kənˈtɪnju ˈaʊər ˈdʒɜrni/</div>
                    <div class="text-gray-700">天气很糟糕；尽管如此，我们决定继续我们的旅程。</div>
                </div>
            </div>
        </section>

        <!-- 标点符号的现代趋势 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">标点符号的现代趋势</h2>
            <p class="text-gray-700 mb-4">随着数字化时代的到来，标点符号的使用出现了新的趋势和变化，这些变化反映了现代交流方式的演变。</p>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">数字化交流中的标点</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">电子邮件格式</div>
                    <div class="keyword text-lg mb-1">Subject: Meeting Reminder - Tomorrow at 3 PM</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈsʌbdʒɪkt ˈmitɪŋ rɪˈmaɪndər təˈmɑroʊ æt θri pi ɛm/</div>
                    <div class="text-gray-700">主题：会议提醒 - 明天下午3点</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">网址标点</div>
                    <div class="keyword text-lg mb-1">Visit our website: www.example.com/products</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈvɪzət ˈaʊər ˈwɛbˌsaɪt dʌbəlju dʌbəlju dʌbəlju ɪgˈzæmpəl kɑm ˈprɑdʌkts/</div>
                    <div class="text-gray-700">访问我们的网站：www.example.com/products</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">社交媒体标签</div>
                    <div class="keyword text-lg mb-1">Great meeting today! #teamwork #success</div>
                    <div class="text-sm text-gray-600 mb-1">/greɪt ˈmitɪŋ təˈdeɪ timˌwɜrk səkˈsɛs/</div>
                    <div class="text-gray-700">今天的会议很棒！#团队合作 #成功</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">即时消息</div>
                    <div class="keyword text-lg mb-1">Running late... be there in 10 mins!</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈrʌnɪŋ leɪt bi ðɛr ɪn tɛn ˈmɪnəts/</div>
                    <div class="text-gray-700">迟到了...10分钟后到！</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">表情符号与标点的结合</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">情感强化</div>
                    <div class="keyword text-lg mb-1">Congratulations! 🎉</div>
                    <div class="text-sm text-gray-600 mb-1">/kənˌgrætʃəˈleɪʃənz/</div>
                    <div class="text-gray-700">恭喜！🎉</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">疑问表达</div>
                    <div class="keyword text-lg mb-1">Are you serious? 🤔</div>
                    <div class="text-sm text-gray-600 mb-1">/ɑr ju ˈsɪriəs/</div>
                    <div class="text-gray-700">你是认真的吗？🤔</div>
                </div>

                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">友好问候</div>
                    <div class="keyword text-lg mb-1">Good morning! ☀️</div>
                    <div class="text-sm text-gray-600 mb-1">/gʊd ˈmɔrnɪŋ/</div>
                    <div class="text-gray-700">早上好！☀️</div>
                </div>

                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">感谢表达</div>
                    <div class="keyword text-lg mb-1">Thank you so much! ❤️</div>
                    <div class="text-sm text-gray-600 mb-1">/θæŋk ju soʊ mʌtʃ/</div>
                    <div class="text-gray-700">非常感谢！❤️</div>
                </div>

                <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">惊讶反应</div>
                    <div class="keyword text-lg mb-1">No way! 😱</div>
                    <div class="text-sm text-gray-600 mb-1">/noʊ weɪ/</div>
                    <div class="text-gray-700">不可能！😱</div>
                </div>

                <div class="card bg-lime-50 border border-lime-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">开心表达</div>
                    <div class="keyword text-lg mb-1">See you soon! 😊</div>
                    <div class="text-sm text-gray-600 mb-1">/si ju sun/</div>
                    <div class="text-gray-700">很快见到你！😊</div>
                </div>
            </div>
        </section>

        <!-- 标点符号的跨文化理解 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">标点符号的跨文化理解</h2>
            <p class="text-gray-700 mb-4">在全球化的今天，理解不同文化背景下标点符号的使用差异，有助于更好地进行国际交流和沟通。</p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">正式程度差异</div>
                    <div class="keyword text-lg mb-1">Dear Mr. Johnson: (formal) vs Dear Mr. Johnson, (less formal)</div>
                    <div class="text-sm text-gray-600 mb-1">/dɪr ˈmɪstər ˈdʒɑnsən ˈfɔrməl vɜrsəs dɪr ˈmɪstər ˈdʒɑnsən lɛs ˈfɔrməl/</div>
                    <div class="text-gray-700">尊敬的约翰逊先生：（正式）vs 尊敬的约翰逊先生，（较不正式）</div>
                </div>

                <div class="card bg-rose-50 border border-rose-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">语气强度差异</div>
                    <div class="keyword text-lg mb-1">Please consider this request. vs Please consider this request!</div>
                    <div class="text-sm text-gray-600 mb-1">/pliz kənˈsɪdər ðɪs rɪˈkwɛst vɜrsəs pliz kənˈsɪdər ðɪs rɪˈkwɛst/</div>
                    <div class="text-gray-700">请考虑这个请求。vs 请考虑这个请求！</div>
                </div>

                <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">时间表达差异</div>
                    <div class="keyword text-lg mb-1">3:30 PM (12-hour) vs 15:30 (24-hour)</div>
                    <div class="text-sm text-gray-600 mb-1">/θri ˈθɜrti pi ɛm twɛlv ˈaʊər vɜrsəs ˈfɪfˈtin ˈθɜrti ˈtwɛnti fɔr ˈaʊər/</div>
                    <div class="text-gray-700">下午3:30（12小时制）vs 15:30（24小时制）</div>
                </div>

                <div class="card bg-amber-50 border border-amber-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">货币表达差异</div>
                    <div class="keyword text-lg mb-1">$1,000.50 vs £1.000,50</div>
                    <div class="text-sm text-gray-600 mb-1">/wʌn ˈθaʊzənd ˈdɑlərz ænd ˈfɪfti sɛnts vɜrsəs wʌn ˈθaʊzənd paʊndz ænd ˈfɪfti pɛns/</div>
                    <div class="text-gray-700">1,000.50美元 vs 1.000,50英镑</div>
                </div>
            </div>
        </section>

        <!-- 标点符号的语法规范 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">标点符号的语法规范</h2>
            <p class="text-gray-700 mb-4">遵循标准的标点符号语法规范，是确保文本专业性和可读性的重要保证。</p>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">常见错误与纠正</h3>
            <div class="grid grid-cols-1 gap-4 mb-6">
                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">逗号拼接错误</div>
                    <div class="keyword text-lg mb-1">错误：I went to the store, I bought some milk.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ wɛnt tu ðə stɔr aɪ bɔt sʌm mɪlk/</div>
                    <div class="text-gray-700">错误：我去了商店，我买了一些牛奶。</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">正确表达</div>
                    <div class="keyword text-lg mb-1">正确：I went to the store, and I bought some milk.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ wɛnt tu ðə stɔr ænd aɪ bɔt sʌm mɪlk/</div>
                    <div class="text-gray-700">正确：我去了商店，买了一些牛奶。</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">撇号误用</div>
                    <div class="keyword text-lg mb-1">错误：The cat's are playing. 正确：The cats are playing.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə kæts ɑr ˈpleɪɪŋ/</div>
                    <div class="text-gray-700">错误：猫的在玩。正确：猫们在玩。</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">引号位置错误</div>
                    <div class="keyword text-lg mb-1">错误：He said "hello". 正确：He said, "Hello."</div>
                    <div class="text-sm text-gray-600 mb-1">/hi sɛd həˈloʊ/</div>
                    <div class="text-gray-700">错误：他说"你好"。正确：他说："你好。"</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">专业写作标准</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">学术引用格式</div>
                    <div class="keyword text-lg mb-1">According to Smith (2023), "Research shows significant results" (p. 45).</div>
                    <div class="text-sm text-gray-600 mb-1">/əˈkɔrdɪŋ tu smɪθ tu ˈθaʊzənd ˈtwɛnti θri rɪˈsɜrtʃ ʃoʊz sɪgˈnɪfəkənt rɪˈzʌlts peɪdʒ ˈfɔrti faɪv/</div>
                    <div class="text-gray-700">根据史密斯（2023）的说法，"研究显示了显著结果"（第45页）。</div>
                </div>

                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">法律文件格式</div>
                    <div class="keyword text-lg mb-1">Section 3.1: Terms and Conditions; Section 3.2: Payment Terms.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈsɛkʃən θri pɔɪnt wʌn tɜrmz ænd kənˈdɪʃənz ˈsɛkʃən θri pɔɪnt tu ˈpeɪmənt tɜrmz/</div>
                    <div class="text-gray-700">第3.1节：条款和条件；第3.2节：付款条件。</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">技术文档格式</div>
                    <div class="keyword text-lg mb-1">Step 1: Install the software; Step 2: Configure settings; Step 3: Test functionality.</div>
                    <div class="text-sm text-gray-600 mb-1">/stɛp wʌn ɪnˈstɔl ðə ˈsɔftˌwɛr stɛp tu kənˈfɪgjər ˈsɛtɪŋz stɛp θri tɛst ˌfʌŋkʃəˈnæləti/</div>
                    <div class="text-gray-700">步骤1：安装软件；步骤2：配置设置；步骤3：测试功能。</div>
                </div>

                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">新闻报道格式</div>
                    <div class="keyword text-lg mb-1">LONDON, March 15 — The Prime Minister announced new policies today.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈlʌndən mɑrtʃ ˈfɪfˈtinθ ðə praɪm ˈmɪnəstər əˈnaʊnst nu ˈpɑləsiz təˈdeɪ/</div>
                    <div class="text-gray-700">伦敦，3月15日——首相今天宣布了新政策。</div>
                </div>
            </div>
        </section>

        <!-- 标点符号的未来发展 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">标点符号的未来发展</h2>
            <p class="text-gray-700 mb-4">随着技术的发展和交流方式的变化，标点符号的使用也在不断演变，新的符号和用法正在出现。</p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">新兴符号</div>
                    <div class="keyword text-lg mb-1">The @ symbol in email addresses: <EMAIL></div>
                    <div class="text-sm text-gray-600 mb-1">/ðə æt ˈsɪmbəl ɪn ˈimeɪl əˈdrɛsəz ˈjuzər æt doʊˈmeɪn kɑm/</div>
                    <div class="text-gray-700">电子邮件地址中的@符号：<EMAIL></div>
                </div>

                <div class="card bg-lime-50 border border-lime-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">网络标记</div>
                    <div class="keyword text-lg mb-1">Hashtags for categorization: #education #technology</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈhæʃˌtægz fɔr ˌkætəgərəˈzeɪʃən ˌɛdʒəˈkeɪʃən tɛkˈnɑlədʒi/</div>
                    <div class="text-gray-700">用于分类的标签：#教育 #技术</div>
                </div>

                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">编程符号</div>
                    <div class="keyword text-lg mb-1">Code syntax: if (condition) { execute(); }</div>
                    <div class="text-sm text-gray-600 mb-1">/koʊd ˈsɪnˌtæks ɪf kənˈdɪʃən ˈɛksəˌkjut/</div>
                    <div class="text-gray-700">代码语法：if (condition) { execute(); }</div>
                </div>

                <div class="card bg-rose-50 border border-rose-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">多媒体标记</div>
                    <div class="keyword text-lg mb-1">Multimedia references: [Video: 2:30] [Audio: 1:45]</div>
                    <div class="text-sm text-gray-600 mb-1">/ˌmʌltiˈmidiə ˈrɛfərənsəz ˈvɪdioʊ tu ˈθɜrti ˈɔdioʊ wʌn ˈfɔrti faɪv/</div>
                    <div class="text-gray-700">多媒体引用：[视频：2:30] [音频：1:45]</div>
                </div>
            </div>
        </section>

        <!-- 标点符号的语法练习应用 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">标点符号的语法练习应用</h2>
            <p class="text-gray-700 mb-4">通过实际的语法练习，可以更好地掌握标点符号在不同语境中的正确使用方法。</p>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">复杂句式标点练习</h3>
            <div class="grid grid-cols-1 gap-4 mb-6">
                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">多重从句标点</div>
                    <div class="keyword text-lg mb-1">The book, which was written by a famous author, became a bestseller; however, the movie adaptation, despite having great actors, failed at the box office.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə bʊk wɪtʃ wʌz ˈrɪtən baɪ ə ˈfeɪməs ˈɔθər bɪˈkeɪm ə ˈbɛstˌsɛlər haʊˈɛvər ðə ˈmuvi ˌædæpˈteɪʃən dɪˈspaɪt ˈhævɪŋ greɪt ˈæktərz feɪld æt ðə bɑks ˈɔfəs/</div>
                    <div class="text-gray-700">这本由著名作家写的书成为了畅销书；然而，尽管有优秀的演员，电影改编版在票房上失败了。</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">并列与转折结合</div>
                    <div class="keyword text-lg mb-1">She studied hard for the exam, prepared all the materials, and reviewed every chapter; nevertheless, she felt nervous on the test day.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi ˈstʌdid hɑrd fɔr ði ɪgˈzæm prɪˈpɛrd ɔl ðə məˈtɪriəlz ænd rɪˈvjud ˈɛvri ˈtʃæptər ˌnɛvərðəˈlɛs ʃi fɛlt ˈnɜrvəs ɑn ðə tɛst deɪ/</div>
                    <div class="text-gray-700">她为考试努力学习，准备了所有材料，复习了每一章；尽管如此，她在考试当天还是感到紧张。</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">引用与解释结合</div>
                    <div class="keyword text-lg mb-1">The CEO announced: "We will expand our operations to three new markets—Asia, Europe, and South America—by the end of this year."</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə si i oʊ əˈnaʊnst wi wɪl ɪkˈspænd ˈaʊər ˌɑpəˈreɪʃənz tu θri nu ˈmɑrkəts ˈeɪʒə ˈjʊrəp ænd saʊθ əˈmɛrəkə baɪ ði ɛnd ʌv ðɪs jɪr/</div>
                    <div class="text-gray-700">首席执行官宣布："我们将在今年年底前将业务扩展到三个新市场——亚洲、欧洲和南美洲。"</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">条件与结果链</div>
                    <div class="keyword text-lg mb-1">If the weather improves—which the forecast suggests it will—we can proceed with the outdoor event; otherwise, we'll move it indoors.</div>
                    <div class="text-sm text-gray-600 mb-1">/ɪf ðə ˈwɛðər ɪmˈpruvz wɪtʃ ðə ˈfɔrˌkæst səgˈdʒɛsts ɪt wɪl wi kæn prəˈsid wɪð ði ˈaʊtˌdɔr ɪˈvɛnt ˈʌðərˌwaɪz wil muv ɪt ɪnˈdɔrz/</div>
                    <div class="text-gray-700">如果天气好转——天气预报表明会好转——我们可以继续进行户外活动；否则，我们将转移到室内。</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">专业文档标点应用</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">技术规范文档</div>
                    <div class="keyword text-lg mb-1">System Requirements: OS: Windows 10+; RAM: 8GB minimum, 16GB recommended; Storage: 500GB available space.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈsɪstəm rɪˈkwaɪrmənts oʊ ɛs ˈwɪndoʊz tɛn plʌs ræm eɪt ˈdʒɪgəˌbaɪts ˈmɪnəməm ˈsɪksˈtin ˈdʒɪgəˌbaɪts ˌrɛkəˈmɛndəd ˈstɔrɪdʒ faɪv ˈhʌndrəd ˈdʒɪgəˌbaɪts əˈveɪləbəl speɪs/</div>
                    <div class="text-gray-700">系统要求：操作系统：Windows 10+；内存：最少8GB，推荐16GB；存储：500GB可用空间。</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">医学报告格式</div>
                    <div class="keyword text-lg mb-1">Patient: John Doe; Age: 45; Diagnosis: Type 2 Diabetes; Treatment: Metformin 500mg twice daily.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈpeɪʃənt dʒɑn doʊ eɪdʒ ˈfɔrti faɪv daɪəgˈnoʊsəs taɪp tu ˌdaɪəˈbitiz ˈtritmənt ˈmɛtˌfɔrmɪn faɪv ˈhʌndrəd ˈmɪləˌgræmz twaɪs ˈdeɪli/</div>
                    <div class="text-gray-700">患者：约翰·多伊；年龄：45岁；诊断：2型糖尿病；治疗：二甲双胍500毫克，每日两次。</div>
                </div>

                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">财务报表格式</div>
                    <div class="keyword text-lg mb-1">Q3 Results: Revenue: $2.5M (↑15%); Expenses: $1.8M (↓5%); Net Profit: $700K (↑45%).</div>
                    <div class="text-sm text-gray-600 mb-1">/kju θri rɪˈzʌlts ˈrɛvəˌnu tu pɔɪnt faɪv ˈmɪljən ʌp ˈfɪfˈtin pərˈsɛnt ɪkˈspɛnsəz wʌn pɔɪnt eɪt ˈmɪljən daʊn faɪv pərˈsɛnt nɛt ˈprɑfət ˈsɛvən ˈhʌndrəd ˈθaʊzənd ʌp ˈfɔrti faɪv pərˈsɛnt/</div>
                    <div class="text-gray-700">第三季度结果：收入：250万美元（↑15%）；支出：180万美元（↓5%）；净利润：70万美元（↑45%）。</div>
                </div>

                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">法律条款格式</div>
                    <div class="keyword text-lg mb-1">Article 5.1: Termination Clause; Article 5.2: Notice Period (30 days minimum); Article 5.3: Compensation Terms.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈɑrtəkəl faɪv pɔɪnt wʌn ˌtɜrməˈneɪʃən klɔz ˈɑrtəkəl faɪv pɔɪnt tu ˈnoʊtəs ˈpɪriəd ˈθɜrti deɪz ˈmɪnəməm ˈɑrtəkəl faɪv pɔɪnt θri ˌkɑmpənˈseɪʃən tɜrmz/</div>
                    <div class="text-gray-700">第5.1条：终止条款；第5.2条：通知期（最少30天）；第5.3条：补偿条款。</div>
                </div>
            </div>
        </section>

        <!-- 标点符号的语法总结与应用指南 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">标点符号的语法总结与应用指南</h2>
            <p class="text-gray-700 mb-4">掌握标点符号的语法规则和应用技巧，是提高英语写作水平和交流效果的关键要素。</p>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">核心语法原则</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">清晰性原则</div>
                    <div class="keyword text-lg mb-1">Punctuation must clarify meaning, not confuse it.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˌpʌŋktʃuˈeɪʃən mʌst ˈklɛrəˌfaɪ ˈminɪŋ nɑt kənˈfjuz ɪt/</div>
                    <div class="text-gray-700">标点符号必须澄清意义，而不是混淆意义。</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">一致性原则</div>
                    <div class="keyword text-lg mb-1">Maintain consistent punctuation style throughout the text.</div>
                    <div class="text-sm text-gray-600 mb-1">/meɪnˈteɪn kənˈsɪstənt ˌpʌŋktʃuˈeɪʃən staɪl θruˈaʊt ðə tɛkst/</div>
                    <div class="text-gray-700">在整个文本中保持一致的标点符号风格。</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">适度性原则</div>
                    <div class="keyword text-lg mb-1">Use punctuation appropriately, neither too much nor too little.</div>
                    <div class="text-sm text-gray-600 mb-1">/juz ˌpʌŋktʃuˈeɪʃən əˈproʊpriətli ˈniðər tu mʌtʃ nɔr tu ˈlɪtəl/</div>
                    <div class="text-gray-700">适当使用标点符号，既不过多也不过少。</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">功能性原则</div>
                    <div class="keyword text-lg mb-1">Each punctuation mark should serve a specific grammatical function.</div>
                    <div class="text-sm text-gray-600 mb-1">/itʃ ˌpʌŋktʃuˈeɪʃən mɑrk ʃʊd sɜrv ə spəˈsɪfɪk grəˈmætəkəl ˈfʌŋkʃən/</div>
                    <div class="text-gray-700">每个标点符号都应该服务于特定的语法功能。</div>
                </div>

                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">语境适应原则</div>
                    <div class="keyword text-lg mb-1">Punctuation should match the formality level of the text.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˌpʌŋktʃuˈeɪʃən ʃʊd mætʃ ðə fɔrˈmæləti ˈlɛvəl ʌv ðə tɛkst/</div>
                    <div class="text-gray-700">标点符号应该与文本的正式程度相匹配。</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">读者友好原则</div>
                    <div class="keyword text-lg mb-1">Punctuation should enhance readability and comprehension.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˌpʌŋktʃuˈeɪʃən ʃʊd ɪnˈhæns ˌridəˈbɪləti ænd ˌkɑmprɪˈhɛnʃən/</div>
                    <div class="text-gray-700">标点符号应该增强可读性和理解性。</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">实用应用技巧</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">写作前规划</div>
                    <div class="keyword text-lg mb-1">Plan your sentence structure before adding punctuation.</div>
                    <div class="text-sm text-gray-600 mb-1">/plæn jʊr ˈsɛntəns ˈstrʌktʃər bɪˈfɔr ˈædɪŋ ˌpʌŋktʃuˈeɪʃən/</div>
                    <div class="text-gray-700">在添加标点符号之前规划句子结构。</div>
                </div>

                <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">朗读检查</div>
                    <div class="keyword text-lg mb-1">Read your text aloud to check punctuation flow.</div>
                    <div class="text-sm text-gray-600 mb-1">/rid jʊr tɛkst əˈlaʊd tu tʃɛk ˌpʌŋktʃuˈeɪʃən floʊ/</div>
                    <div class="text-gray-700">大声朗读文本以检查标点符号的流畅性。</div>
                </div>

                <div class="card bg-lime-50 border border-lime-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">逐步修改</div>
                    <div class="keyword text-lg mb-1">Review and refine punctuation in multiple editing passes.</div>
                    <div class="text-sm text-gray-600 mb-1">/rɪˈvju ænd rɪˈfaɪn ˌpʌŋktʃuˈeɪʃən ɪn ˈmʌltəpəl ˈɛdətɪŋ ˈpæsəz/</div>
                    <div class="text-gray-700">在多次编辑过程中审查和完善标点符号。</div>
                </div>

                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">参考标准</div>
                    <div class="keyword text-lg mb-1">Consult style guides for specific punctuation rules.</div>
                    <div class="text-sm text-gray-600 mb-1">/kənˈsʌlt staɪl gaɪdz fɔr spəˈsɪfɪk ˌpʌŋktʃuˈeɪʃən rulz/</div>
                    <div class="text-gray-700">查阅风格指南了解特定的标点符号规则。</div>
                </div>
            </div>
        </section>

        <!-- 标点符号语法学习的最终总结 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">标点符号语法学习的最终总结</h2>
            <p class="text-gray-700 mb-4">标点符号是英语语法体系中的重要组成部分，它们不仅影响句子的结构和意义，更是有效交流的关键工具。通过系统学习和实践应用，我们可以掌握标点符号的精髓，提升语言表达的准确性和效果。</p>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                <div class="card bg-gradient-to-br from-blue-50 to-blue-100 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">语法结构</div>
                    <div class="keyword text-lg mb-1">Structure sentences clearly</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈstrʌktʃər ˈsɛntənsəz ˈklɪrli/</div>
                    <div class="text-gray-700">清晰地构建句子结构</div>
                </div>

                <div class="card bg-gradient-to-br from-green-50 to-green-100 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">语义表达</div>
                    <div class="keyword text-lg mb-1">Express meaning precisely</div>
                    <div class="text-sm text-gray-600 mb-1">/ɪkˈsprɛs ˈminɪŋ prɪˈsaɪsli/</div>
                    <div class="text-gray-700">精确地表达意义</div>
                </div>

                <div class="card bg-gradient-to-br from-yellow-50 to-yellow-100 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">语调控制</div>
                    <div class="keyword text-lg mb-1">Control rhythm and tone</div>
                    <div class="text-sm text-gray-600 mb-1">/kənˈtroʊl ˈrɪðəm ænd toʊn/</div>
                    <div class="text-gray-700">控制节奏和语调</div>
                </div>

                <div class="card bg-gradient-to-br from-purple-50 to-purple-100 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">交流效果</div>
                    <div class="keyword text-lg mb-1">Enhance communication</div>
                    <div class="text-sm text-gray-600 mb-1">/ɪnˈhæns kəˌmjunəˈkeɪʃən/</div>
                    <div class="text-gray-700">增强交流效果</div>
                </div>
            </div>

            <div class="bg-gray-50 border border-gray-200 rounded-lg p-6 text-center">
                <p class="text-lg text-gray-800 mb-2">
                    <span class="keyword">Mastering punctuation is mastering the art of clear communication.</span>
                </p>
                <p class="text-sm text-gray-600 mb-1">/ˈmæstərɪŋ ˌpʌŋktʃuˈeɪʃən ɪz ˈmæstərɪŋ ði ɑrt ʌv klɪr kəˌmjunəˈkeɪʃən/</p>
                <p class="text-gray-700">掌握标点符号就是掌握清晰交流的艺术。</p>
            </div>
        </section>

        <!-- 标点符号的高级语法应用 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">标点符号的高级语法应用</h2>
            <p class="text-gray-700 mb-4">在高级英语写作中，标点符号的精确使用能够体现作者的语言功底和专业水平。</p>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">学术写作中的精确标点</h3>
            <div class="grid grid-cols-1 gap-4 mb-6">
                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">研究方法描述</div>
                    <div class="keyword text-lg mb-1">The study employed a mixed-methods approach: quantitative analysis (n=500) for statistical significance; qualitative interviews (n=25) for deeper insights; and longitudinal observation (12 months) for behavioral patterns.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈstʌdi ɪmˈplɔɪd ə mɪkst ˈmɛθədz əˈproʊtʃ ˈkwɑntəˌteɪtɪv əˈnæləsəs ɛn ikwəlz faɪv ˈhʌndrəd fɔr stəˈtɪstəkəl sɪgˈnɪfəkəns ˈkwɑləˌteɪtɪv ˈɪntərˌvjuz ɛn ikwəlz ˈtwɛnti faɪv fɔr ˈdipər ˈɪnˌsaɪts ænd ˌlɑndʒəˈtudənəl ˌɑbzərˈveɪʃən twɛlv mʌnθs fɔr bɪˈheɪvjərəl ˈpætərnz/</div>
                    <div class="text-gray-700">该研究采用了混合方法：定量分析（n=500）用于统计显著性；定性访谈（n=25）用于深入洞察；纵向观察（12个月）用于行为模式。</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">文献综述格式</div>
                    <div class="keyword text-lg mb-1">Previous research has established three key findings: first, environmental factors significantly impact learning outcomes (Johnson, 2022; Smith & Brown, 2023); second, technological integration enhances student engagement (Davis et al., 2021); third, personalized learning approaches improve retention rates (Wilson, 2020).</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈpriviəs rɪˈsɜrtʃ hæz ɪˈstæblɪʃt θri ki ˈfaɪndɪŋz fɜrst ɪnˌvaɪrənˈmɛntəl ˈfæktərz sɪgˈnɪfəkəntli ˈɪmpækt ˈlɜrnɪŋ ˈaʊtˌkʌmz ˈdʒɑnsən tu ˈθaʊzənd ˈtwɛnti tu smɪθ ænd braʊn tu ˈθaʊzənd ˈtwɛnti θri ˈsɛkənd ˌtɛknəˈlɑdʒəkəl ˌɪntəˈgreɪʃən ɪnˈhænsəz ˈstudənt ɪnˈgeɪdʒmənt ˈdeɪvəs ɛt æl tu ˈθaʊzənd ˈtwɛnti wʌn θɜrd ˈpɜrsənəˌlaɪzd ˈlɜrnɪŋ əˈproʊtʃəz ɪmˈpruv rɪˈtɛnʃən reɪts ˈwɪlsən tu ˈθaʊzənd ˈtwɛnti/</div>
                    <div class="text-gray-700">先前的研究确立了三个关键发现：首先，环境因素显著影响学习成果（Johnson, 2022; Smith & Brown, 2023）；其次，技术整合增强学生参与度（Davis et al., 2021）；第三，个性化学习方法提高保持率（Wilson, 2020）。</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">数据分析报告</div>
                    <div class="keyword text-lg mb-1">The statistical analysis revealed significant correlations: academic performance and study time (r=0.78, p<0.001); student satisfaction and instructor feedback (r=0.65, p<0.01); course completion and initial motivation (r=0.72, p<0.001).</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə stəˈtɪstəkəl əˈnæləsəs rɪˈvild sɪgˈnɪfəkənt ˌkɔrəˈleɪʃənz ˌækəˈdɛmɪk pərˈfɔrməns ænd ˈstʌdi taɪm ɑr ikwəlz ˈziroʊ pɔɪnt ˈsɛvənti eɪt pi lɛs ðæn ˈziroʊ pɔɪnt ˈziroʊ ˈziroʊ wʌn ˈstudənt ˌsætəsˈfækʃən ænd ɪnˈstrʌktər ˈfidˌbæk ɑr ikwəlz ˈziroʊ pɔɪnt ˈsɪksti faɪv pi lɛs ðæn ˈziroʊ pɔɪnt ˈziroʊ wʌn kɔrs kəmˈpliʃən ænd ɪˈnɪʃəl ˌmoʊtəˈveɪʃən ɑr ikwəlz ˈziroʊ pɔɪnt ˈsɛvənti tu pi lɛs ðæn ˈziroʊ pɔɪnt ˈziroʊ ˈziroʊ wʌn/</div>
                    <div class="text-gray-700">统计分析显示了显著的相关性：学术表现与学习时间（r=0.78, p<0.001）；学生满意度与教师反馈（r=0.65, p<0.01）；课程完成与初始动机（r=0.72, p<0.001）。</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">理论框架阐述</div>
                    <div class="keyword text-lg mb-1">The theoretical framework integrates three complementary theories: Constructivist Learning Theory (Piaget, 1952)—emphasizing active knowledge construction; Social Learning Theory (Bandura, 1977)—highlighting observational learning; and Self-Determination Theory (Deci & Ryan, 1985)—focusing on intrinsic motivation.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˌθiəˈrɛtəkəl ˈfreɪmˌwɜrk ˈɪntəˌgreɪts θri ˌkɑmpləˈmɛntəri ˈθiəriz kənˈstrʌktəvəst ˈlɜrnɪŋ ˈθiəri piəˈʒeɪ naɪnˈtin ˈfɪfti tu ˈɛmfəˌsaɪzɪŋ ˈæktɪv ˈnɑlɪdʒ kənˈstrʌkʃən ˈsoʊʃəl ˈlɜrnɪŋ ˈθiəri bænˈdʊrə naɪnˈtin ˈsɛvənti ˈsɛvən ˈhaɪˌlaɪtɪŋ ˌɑbzərˈveɪʃənəl ˈlɜrnɪŋ ænd sɛlf dɪˌtɜrməˈneɪʃən ˈθiəri ˈdɛsi ænd ˈraɪən naɪnˈtin ˈeɪti faɪv ˈfoʊkəsɪŋ ɑn ɪnˈtrɪnzɪk ˌmoʊtəˈveɪʃən/</div>
                    <div class="text-gray-700">理论框架整合了三个互补理论：建构主义学习理论（Piaget, 1952）——强调主动知识建构；社会学习理论（Bandura, 1977）——突出观察学习；自我决定理论（Deci & Ryan, 1985）——关注内在动机。</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">商务写作中的专业标点</h3>
            <div class="grid grid-cols-1 gap-4 mb-6">
                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">战略规划文档</div>
                    <div class="keyword text-lg mb-1">Our five-year strategic plan encompasses four key areas: market expansion (targeting 15% growth annually); product innovation (launching 3-5 new products per year); operational efficiency (reducing costs by 20%); and talent development (investing $2M in employee training).</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈaʊər faɪv jɪr strəˈtidʒɪk plæn ɪnˈkʌmpəsəz fɔr ki ˈɛriəz ˈmɑrkət ɪkˈspænʃən ˈtɑrgətɪŋ ˈfɪfˈtin pərˈsɛnt groʊθ ˈænjuəli ˈprɑdʌkt ˌɪnəˈveɪʃən ˈlɔntʃɪŋ θri tu faɪv nu ˈprɑdʌkts pər jɪr ˌɑpəˈreɪʃənəl ɪˈfɪʃənsi rɪˈdusɪŋ kɔsts baɪ ˈtwɛnti pərˈsɛnt ænd ˈtælənt dɪˈvɛləpmənt ɪnˈvɛstɪŋ tu ˈmɪljən ˈdɑlərz ɪn ɪmˈplɔɪi ˈtreɪnɪŋ/</div>
                    <div class="text-gray-700">我们的五年战略计划涵盖四个关键领域：市场扩张（目标年增长15%）；产品创新（每年推出3-5个新产品）；运营效率（降低成本20%）；人才发展（投资200万美元用于员工培训）。</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">财务分析报告</div>
                    <div class="keyword text-lg mb-1">Q4 financial performance exceeded expectations: revenue increased 18% year-over-year ($45.2M vs. $38.3M); gross margin improved to 42% (up from 38%); operating expenses decreased 5% ($12.1M vs. $12.7M); net income rose 35% ($8.9M vs. $6.6M).</div>
                    <div class="text-sm text-gray-600 mb-1">/kju fɔr faɪˈnænʃəl pərˈfɔrməns ɪkˈsidəd ˌɛkspɛkˈteɪʃənz ˈrɛvəˌnu ɪnˈkrist ˈeɪˈtin pərˈsɛnt jɪr ˈoʊvər jɪr ˈfɔrti faɪv pɔɪnt tu ˈmɪljən vɜrsəs ˈθɜrti eɪt pɔɪnt θri ˈmɪljən groʊs ˈmɑrdʒən ɪmˈpruvd tu ˈfɔrti tu pərˈsɛnt ʌp frʌm ˈθɜrti eɪt pərˈsɛnt ˈɑpəˌreɪtɪŋ ɪkˈspɛnsəz dɪˈkrist faɪv pərˈsɛnt twɛlv pɔɪnt wʌn ˈmɪljən vɜrsəs twɛlv pɔɪnt ˈsɛvən ˈmɪljən nɛt ˈɪnˌkʌm roʊz ˈθɜrti faɪv pərˈsɛnt eɪt pɔɪnt naɪn ˈmɪljən vɜrsəs sɪks pɔɪnt sɪks ˈmɪljən/</div>
                    <div class="text-gray-700">第四季度财务表现超出预期：收入同比增长18%（4520万美元 vs. 3830万美元）；毛利率提升至42%（从38%上升）；运营费用下降5%（1210万美元 vs. 1270万美元）；净收入增长35%（890万美元 vs. 660万美元）。</div>
                </div>

                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">项目提案书</div>
                    <div class="keyword text-lg mb-1">The proposed digital transformation initiative requires significant investment: Phase 1 (Infrastructure Setup): $2.5M over 6 months; Phase 2 (System Integration): $1.8M over 4 months; Phase 3 (Training & Deployment): $1.2M over 3 months; Total Project Cost: $5.5M over 13 months.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə prəˈpoʊzd ˈdɪdʒətəl ˌtrænsfərˈmeɪʃən ɪˈnɪʃətɪv rɪˈkwaɪərz sɪgˈnɪfəkənt ɪnˈvɛstmənt feɪz wʌn ˈɪnfrəˌstrʌktʃər ˈsɛtˌʌp tu pɔɪnt faɪv ˈmɪljən ˈoʊvər sɪks mʌnθs feɪz tu ˈsɪstəm ˌɪntəˈgreɪʃən wʌn pɔɪnt eɪt ˈmɪljən ˈoʊvər fɔr mʌnθs feɪz θri ˈtreɪnɪŋ ænd dɪˈplɔɪmənt wʌn pɔɪnt tu ˈmɪljən ˈoʊvər θri mʌnθs ˈtoʊtəl ˈprɑdʒɛkt kɔst faɪv pɔɪnt faɪv ˈmɪljən ˈoʊvər ˈθɜrˈtin mʌnθs/</div>
                    <div class="text-gray-700">拟议的数字化转型计划需要大量投资：第一阶段（基础设施建设）：6个月内250万美元；第二阶段（系统集成）：4个月内180万美元；第三阶段（培训与部署）：3个月内120万美元；项目总成本：13个月内550万美元。</div>
                </div>

                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">合同条款详述</div>
                    <div class="keyword text-lg mb-1">The service agreement includes comprehensive terms: Scope of Work (detailed in Appendix A); Performance Metrics (minimum 99.5% uptime, <2-second response time); Payment Schedule (30% upfront, 50% at milestone completion, 20% upon final delivery); Termination Clause (30-day notice required from either party).</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈsɜrvəs əˈgrimənt ɪnˈkludz ˌkɑmprɪˈhɛnsɪv tɜrmz skoʊp ʌv wɜrk ˈditeɪld ɪn əˈpɛndɪks eɪ pərˈfɔrməns ˈmɛtrɪks ˈmɪnəməm ˈnaɪnti naɪn pɔɪnt faɪv pərˈsɛnt ˈʌpˌtaɪm lɛs ðæn tu ˈsɛkənd rɪˈspɑns taɪm ˈpeɪmənt ˈskɛdʒul ˈθɜrti pərˈsɛnt ˈʌpˌfrʌnt ˈfɪfti pərˈsɛnt æt ˈmaɪlˌstoʊn kəmˈpliʃən ˈtwɛnti pərˈsɛnt əˈpɑn ˈfaɪnəl dɪˈlɪvəri ˌtɜrməˈneɪʃən klɔz ˈθɜrti deɪ ˈnoʊtəs rɪˈkwaɪərd frʌm ˈiðər ˈpɑrti/</div>
                    <div class="text-gray-700">服务协议包括全面条款：工作范围（详见附录A）；性能指标（最低99.5%正常运行时间，<2秒响应时间）；付款计划（30%预付，50%里程碑完成时，20%最终交付时）；终止条款（任何一方需提前30天通知）。</div>
                </div>
            </div>
        </section>

        <!-- 标点符号的创意表达技巧 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">标点符号的创意表达技巧</h2>
            <p class="text-gray-700 mb-4">在创意写作中，标点符号不仅是语法工具，更是表达情感、营造氛围、创造节奏的艺术手段。</p>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">文学写作中的标点艺术</h3>
            <div class="grid grid-cols-1 gap-4 mb-6">
                <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">悬疑氛围营造</div>
                    <div class="keyword text-lg mb-1">The door creaked open... slowly... deliberately... revealing nothing but darkness beyond—a darkness that seemed to breathe, to watch, to wait.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə dɔr krikt ˈoʊpən ˈsloʊli dɪˈlɪbərətli rɪˈvilɪŋ ˈnʌθɪŋ bʌt ˈdɑrknəs bɪˈjɑnd ə ˈdɑrknəs ðæt simd tu brið tu wɑtʃ tu weɪt/</div>
                    <div class="text-gray-700">门吱呀一声打开了...缓慢地...故意地...除了黑暗什么也没有显露——一种似乎在呼吸、在观察、在等待的黑暗。</div>
                </div>

                <div class="card bg-lime-50 border border-lime-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">情感波动描写</div>
                    <div class="keyword text-lg mb-1">She felt everything at once: joy (overwhelming, unexpected); fear (deep, primal); hope (fragile, precious)—emotions colliding like waves against the shore of her consciousness.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi fɛlt ˈɛvriθɪŋ æt wʌns dʒɔɪ ˌoʊvərˈwɛlmɪŋ ˌʌnɪkˈspɛktəd fɪr dip ˈpraɪməl hoʊp ˈfrædʒəl ˈprɛʃəs ɪˈmoʊʃənz kəˈlaɪdɪŋ laɪk weɪvz əˈgɛnst ðə ʃɔr ʌv hər ˈkɑnʃəsnəs/</div>
                    <div class="text-gray-700">她同时感受到了一切：喜悦（压倒性的、意外的）；恐惧（深深的、原始的）；希望（脆弱的、珍贵的）——情感像海浪一样撞击着她意识的海岸。</div>
                </div>

                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">时间流逝表现</div>
                    <div class="keyword text-lg mb-1">Minutes passed... then hours... days blurred into weeks... seasons changed... and still she waited—patient, hopeful, unwavering in her faith.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈmɪnəts pæst ðɛn ˈaʊərz deɪz blɜrd ˈɪntu wiks ˈsizənz tʃeɪndʒd ænd stɪl ʃi ˈweɪtəd ˈpeɪʃənt ˈhoʊpfəl ʌnˈweɪvərɪŋ ɪn hər feɪθ/</div>
                    <div class="text-gray-700">分钟过去了...然后是小时...日子模糊成周...季节变化...她仍在等待——耐心、充满希望、对信念坚定不移。</div>
                </div>

                <div class="card bg-rose-50 border border-rose-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">对话节奏控制</div>
                    <div class="keyword text-lg mb-1">"I need to tell you something," she whispered. "Something important." A pause. "Something that will change everything." Another pause, longer this time. "I love you."</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ nid tu tɛl ju ˈsʌmθɪŋ ʃi ˈwɪspərd ˈsʌmθɪŋ ɪmˈpɔrtənt ə pɔz ˈsʌmθɪŋ ðæt wɪl tʃeɪndʒ ˈɛvriθɪŋ əˈnʌðər pɔz ˈlɔŋgər ðɪs taɪm aɪ lʌv ju/</div>
                    <div class="text-gray-700">"我需要告诉你一些事情，"她低声说。"一些重要的事情。"停顿。"一些会改变一切的事情。"又一次停顿，这次更长。"我爱你。"</div>
                </div>
            </div>
        </section>

        <!-- 标点符号的技术写作应用 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">标点符号的技术写作应用</h2>
            <p class="text-gray-700 mb-4">在技术文档和说明书中，标点符号的准确使用对于确保信息的清晰传达和用户的正确理解至关重要。</p>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">软件文档标点规范</h3>
            <div class="grid grid-cols-1 gap-4 mb-6">
                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">API文档格式</div>
                    <div class="keyword text-lg mb-1">GET /api/users/{id}: Retrieves user information; Parameters: id (integer, required), include (string, optional); Response: 200 (success), 404 (not found), 500 (server error).</div>
                    <div class="text-sm text-gray-600 mb-1">/gɛt ˈeɪ pi aɪ ˈjuzərz aɪ di rɪˈtrivz ˈjuzər ˌɪnfərˈmeɪʃən pəˈræmətərz aɪ di ˈɪntədʒər rɪˈkwaɪərd ɪnˈklud strɪŋ ˈɑpʃənəl rɪˈspɑns tu ˈhʌndrəd səkˈsɛs fɔr oʊ fɔr nɑt faʊnd faɪv ˈhʌndrəd ˈsɜrvər ˈɛrər/</div>
                    <div class="text-gray-700">GET /api/users/{id}：检索用户信息；参数：id（整数，必需），include（字符串，可选）；响应：200（成功），404（未找到），500（服务器错误）。</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">安装指南格式</div>
                    <div class="keyword text-lg mb-1">Installation Requirements: Node.js (v16.0+); npm (v8.0+); Git (v2.30+); Operating System: Windows 10/11, macOS 10.15+, or Ubuntu 20.04+.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˌɪnstəˈleɪʃən rɪˈkwaɪrmənts noʊd dʒeɪ ɛs ˈvɜrʒən ˈsɪksˈtin pɔɪnt ˈziroʊ plʌs ɛn pi ɛm ˈvɜrʒən eɪt pɔɪnt ˈziroʊ plʌs gɪt ˈvɜrʒən tu pɔɪnt ˈθɜrti plʌs ˈɑpəˌreɪtɪŋ ˈsɪstəm ˈwɪndoʊz tɛn ɪˈlɛvən ˈmæk oʊ ɛs tɛn pɔɪnt ˈfɪfˈtin plʌs ɔr uˈbuntu ˈtwɛnti pɔɪnt oʊ fɔr plʌs/</div>
                    <div class="text-gray-700">安装要求：Node.js（v16.0+）；npm（v8.0+）；Git（v2.30+）；操作系统：Windows 10/11、macOS 10.15+或Ubuntu 20.04+。</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">配置文件说明</div>
                    <div class="keyword text-lg mb-1">Configuration Options: server.port (default: 3000); database.host (required); database.port (default: 5432); logging.level (options: "debug", "info", "warn", "error").</div>
                    <div class="text-sm text-gray-600 mb-1">/kənˌfɪgjəˈreɪʃən ˈɑpʃənz ˈsɜrvər pɔrt dɪˈfɔlt θri ˈθaʊzənd ˈdeɪtəˌbeɪs hoʊst rɪˈkwaɪərd ˈdeɪtəˌbeɪs pɔrt dɪˈfɔlt faɪv ˈθaʊzənd fɔr ˈθɜrti tu ˈlɔgɪŋ ˈlɛvəl ˈɑpʃənz dɪˈbʌg ˈɪnfoʊ wɔrn ˈɛrər/</div>
                    <div class="text-gray-700">配置选项：server.port（默认：3000）；database.host（必需）；database.port（默认：5432）；logging.level（选项："debug"、"info"、"warn"、"error"）。</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">错误处理说明</div>
                    <div class="keyword text-lg mb-1">Error Handling: Try-catch blocks should wrap all async operations; Log errors with appropriate severity levels; Return meaningful error messages to users; Implement retry logic for transient failures (network timeouts, temporary service unavailability).</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈɛrər ˈhændlɪŋ traɪ kætʃ blɑks ʃʊd ræp ɔl ˈeɪsɪŋk ˌɑpəˈreɪʃənz lɔg ˈɛrərz wɪð əˈproʊpriət sɪˈvɛrəti ˈlɛvəlz rɪˈtɜrn ˈminɪŋfəl ˈɛrər ˈmɛsədʒəz tu ˈjuzərz ˈɪmpləmənt ˈritraɪ ˈlɑdʒɪk fɔr ˈtrænʃənt ˈfeɪljərz ˈnɛtˌwɜrk ˈtaɪˌmaʊts ˈtɛmpəˌrɛri ˈsɜrvəs ˌʌnəˌveɪləˈbɪləti/</div>
                    <div class="text-gray-700">错误处理：Try-catch块应包装所有异步操作；使用适当的严重级别记录错误；向用户返回有意义的错误消息；为瞬态故障实现重试逻辑（网络超时、临时服务不可用）。</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">科学论文标点应用</h3>
            <div class="grid grid-cols-1 gap-4 mb-6">
                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">实验方法描述</div>
                    <div class="keyword text-lg mb-1">Experimental Procedure: Sample preparation (24-hour incubation at 37°C); Treatment application (concentrations: 10μM, 50μM, 100μM); Data collection (measurements taken at 0h, 6h, 12h, 24h intervals); Statistical analysis (ANOVA, p<0.05 considered significant).</div>
                    <div class="text-sm text-gray-600 mb-1">/ɪkˌspɛrəˈmɛntəl prəˈsidʒər ˈsæmpəl ˌprɛpəˈreɪʃən ˈtwɛnti fɔr ˈaʊər ˌɪnkjəˈbeɪʃən æt ˈθɜrti ˈsɛvən dɪˈgriz ˈsɛlsiəs ˈtritmənt ˌæpləˈkeɪʃən ˌkɑnsənˈtreɪʃənz tɛn ˈmaɪkroʊ ˈmoʊlər ˈfɪfti ˈmaɪkroʊ ˈmoʊlər wʌn ˈhʌndrəd ˈmaɪkroʊ ˈmoʊlər ˈdeɪtə kəˈlɛkʃən ˈmɛʒərməns ˈteɪkən æt ˈziroʊ ˈaʊərz sɪks ˈaʊərz twɛlv ˈaʊərz ˈtwɛnti fɔr ˈaʊərz ˈɪntərvəlz stəˈtɪstəkəl əˈnæləsəs əˈnoʊvə pi lɛs ðæn ˈziroʊ pɔɪnt oʊ faɪv kənˈsɪdərd sɪgˈnɪfəkənt/</div>
                    <div class="text-gray-700">实验程序：样品制备（37°C下24小时培养）；处理应用（浓度：10μM、50μM、100μM）；数据收集（在0h、6h、12h、24h间隔进行测量）；统计分析（ANOVA，p<0.05被认为是显著的）。</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">结果报告格式</div>
                    <div class="keyword text-lg mb-1">Results Summary: Treatment Group A showed significant improvement (mean±SD: 85.3±4.2, n=30); Control Group exhibited baseline values (mean±SD: 62.1±3.8, n=30); Statistical significance was observed (t-test: p=0.001, 95% CI: 18.7-27.7).</div>
                    <div class="text-sm text-gray-600 mb-1">/rɪˈzʌlts ˈsʌməri ˈtritmənt grup eɪ ʃoʊd sɪgˈnɪfəkənt ɪmˈpruvmənt min plʌs ɔr ˈmaɪnəs ˈstændərd diviˈeɪʃən ˈeɪti faɪv pɔɪnt θri plʌs ɔr ˈmaɪnəs fɔr pɔɪnt tu ɛn ikwəlz ˈθɜrti kənˈtroʊl grup ɪgˈzɪbətəd ˈbeɪsˌlaɪn ˈvæljuz min plʌs ɔr ˈmaɪnəs ˈstændərd diviˈeɪʃən ˈsɪksti tu pɔɪnt wʌn plʌs ɔr ˈmaɪnəs θri pɔɪnt eɪt ɛn ikwəlz ˈθɜrti stəˈtɪstəkəl sɪgˈnɪfəkəns wʌz əbˈzɜrvd ti tɛst pi ikwəlz ˈziroʊ pɔɪnt ˈziroʊ ˈziroʊ wʌn ˈnaɪnti faɪv pərˈsɛnt ˈkɑnfədəns ˈɪntərvəl ˈeɪˈtin pɔɪnt ˈsɛvən tu ˈtwɛnti ˈsɛvən pɔɪnt ˈsɛvən/</div>
                    <div class="text-gray-700">结果摘要：治疗组A显示显著改善（平均值±标准差：85.3±4.2，n=30）；对照组表现出基线值（平均值±标准差：62.1±3.8，n=30）；观察到统计学显著性（t检验：p=0.001，95%置信区间：18.7-27.7）。</div>
                </div>

                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">讨论部分格式</div>
                    <div class="keyword text-lg mb-1">Discussion: Our findings align with previous research (Smith et al., 2022; Johnson & Brown, 2023); however, several limitations must be acknowledged: small sample size (n=60); short observation period (4 weeks); potential confounding variables (age, gender, baseline health status).</div>
                    <div class="text-sm text-gray-600 mb-1">/dɪˈskʌʃən ˈaʊər ˈfaɪndɪŋz əˈlaɪn wɪð ˈpriviəs rɪˈsɜrtʃ smɪθ ɛt æl tu ˈθaʊzənd ˈtwɛnti tu ˈdʒɑnsən ænd braʊn tu ˈθaʊzənd ˈtwɛnti θri haʊˈɛvər ˈsɛvərəl ˌlɪməˈteɪʃənz mʌst bi ækˈnɑlɪdʒd smɔl ˈsæmpəl saɪz ɛn ikwəlz ˈsɪksti ʃɔrt ˌɑbzərˈveɪʃən ˈpɪriəd fɔr wiks pəˈtɛnʃəl kənˈfaʊndɪŋ ˈvɛriəbəlz eɪdʒ ˈdʒɛndər ˈbeɪsˌlaɪn hɛlθ ˈsteɪtəs/</div>
                    <div class="text-gray-700">讨论：我们的发现与先前的研究一致（Smith et al., 2022; Johnson & Brown, 2023）；然而，必须承认几个局限性：样本量小（n=60）；观察期短（4周）；潜在的混杂变量（年龄、性别、基线健康状况）。</div>
                </div>

                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">结论陈述格式</div>
                    <div class="keyword text-lg mb-1">Conclusions: This study demonstrates that: (1) the intervention significantly improves outcomes; (2) the effect is dose-dependent; (3) no serious adverse events were observed; (4) further research with larger samples is warranted.</div>
                    <div class="text-sm text-gray-600 mb-1">/kənˈkluʒənz ðɪs ˈstʌdi ˈdɛmənˌstreɪts ðæt wʌn ði ˌɪntərˈvɛnʃən sɪgˈnɪfəkəntli ɪmˈpruvz ˈaʊtˌkʌmz tu ði ɪˈfɛkt ɪz doʊs dɪˈpɛndənt θri noʊ ˈsɪriəs ædˈvɜrs ɪˈvɛnts wər əbˈzɜrvd fɔr ˈfɜrðər rɪˈsɜrtʃ wɪð ˈlɑrdʒər ˈsæmpəlz ɪz ˈwɔrəntəd/</div>
                    <div class="text-gray-700">结论：本研究表明：（1）干预显著改善结果；（2）效果呈剂量依赖性；（3）未观察到严重不良事件；（4）需要进行更大样本的进一步研究。</div>
                </div>
            </div>
        </section>

        <!-- 标点符号的国际化应用 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">标点符号的国际化应用</h2>
            <p class="text-gray-700 mb-4">在全球化的商务和学术环境中，了解不同地区和文化背景下标点符号的使用差异，有助于更好地进行跨文化交流。</p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">数字格式差异</div>
                    <div class="keyword text-lg mb-1">US Format: $1,234.56 vs European Format: €1.234,56</div>
                    <div class="text-sm text-gray-600 mb-1">/ju ɛs ˈfɔrmæt wʌn ˈθaʊzənd tu ˈhʌndrəd ˈθɜrti fɔr ˈdɑlərz ænd ˈfɪfti sɪks sɛnts vɜrsəs ˌjʊrəˈpiən ˈfɔrmæt wʌn ˈθaʊzənd tu ˈhʌndrəd ˈθɜrti fɔr ˈjʊroʊz ænd ˈfɪfti sɪks sɛnts/</div>
                    <div class="text-gray-700">美式格式：$1,234.56 vs 欧式格式：€1.234,56</div>
                </div>

                <div class="card bg-lime-50 border border-lime-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">日期格式差异</div>
                    <div class="keyword text-lg mb-1">US: March 15, 2024 vs UK: 15 March 2024 vs ISO: 2024-03-15</div>
                    <div class="text-sm text-gray-600 mb-1">/ju ɛs mɑrtʃ ˈfɪfˈtin tu ˈθaʊzənd ˈtwɛnti fɔr vɜrsəs ju keɪ ˈfɪfˈtin mɑrtʃ tu ˈθaʊzənd ˈtwɛnti fɔr vɜrsəs aɪ ɛs oʊ tu ˈθaʊzənd ˈtwɛnti fɔr oʊ θri ˈfɪfˈtin/</div>
                    <div class="text-gray-700">美式：March 15, 2024 vs 英式：15 March 2024 vs ISO：2024-03-15</div>
                </div>

                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">引号使用差异</div>
                    <div class="keyword text-lg mb-1">American: "Hello," she said. vs British: 'Hello,' she said.</div>
                    <div class="text-sm text-gray-600 mb-1">/əˈmɛrəkən həˈloʊ ʃi sɛd vɜrsəs ˈbrɪtɪʃ həˈloʊ ʃi sɛd/</div>
                    <div class="text-gray-700">美式："Hello," she said. vs 英式：'Hello,' she said.</div>
                </div>

                <div class="card bg-rose-50 border border-rose-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">地址格式差异</div>
                    <div class="keyword text-lg mb-1">US: 123 Main St., New York, NY 10001 vs UK: 123 Main Street, London SW1A 1AA</div>
                    <div class="text-sm text-gray-600 mb-1">/ju ɛs wʌn tu θri meɪn strit nu jɔrk nu jɔrk wʌn ˈziroʊ ˈziroʊ ˈziroʊ wʌn vɜrsəs ju keɪ wʌn tu θri meɪn strit ˈlʌndən ɛs dʌbəlju wʌn eɪ wʌn eɪ eɪ/</div>
                    <div class="text-gray-700">美式：123 Main St., New York, NY 10001 vs 英式：123 Main Street, London SW1A 1AA</div>
                </div>
            </div>
        </section>

        <!-- 标点符号学习的最终指导 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">标点符号学习的最终指导</h2>
            <p class="text-gray-700 mb-4">通过系统学习标点符号的语法功能和实际应用，我们可以显著提升英语写作和交流的质量。标点符号不仅是语法规则，更是表达思想、传递情感、构建意义的重要工具。</p>

            <div class="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-lg p-6 mb-6">
                <h3 class="text-xl font-semibold mb-4 text-gray-800">掌握标点符号的核心要点</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div class="text-center">
                        <div class="keyword text-2xl mb-2">.</div>
                        <p class="text-sm text-gray-700">句号：完整陈述的终结</p>
                    </div>
                    <div class="text-center">
                        <div class="keyword text-2xl mb-2">,</div>
                        <p class="text-sm text-gray-700">逗号：分隔与连接的平衡</p>
                    </div>
                    <div class="text-center">
                        <div class="keyword text-2xl mb-2">?</div>
                        <p class="text-sm text-gray-700">问号：疑问与探索的标志</p>
                    </div>
                    <div class="text-center">
                        <div class="keyword text-2xl mb-2">!</div>
                        <p class="text-sm text-gray-700">感叹号：情感与强调的表达</p>
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm text-center">
                    <div class="keyword text-lg mb-2">Practice Regularly</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈpræktəs ˈrɛgjələrli/</div>
                    <div class="text-gray-700">定期练习标点符号的使用</div>
                </div>

                <div class="card bg-amber-50 border border-amber-200 rounded-lg p-4 shadow-sm text-center">
                    <div class="keyword text-lg mb-2">Read Extensively</div>
                    <div class="text-sm text-gray-600 mb-1">/rid ɪkˈstɛnsɪvli/</div>
                    <div class="text-gray-700">广泛阅读优秀的英语文本</div>
                </div>

                <div class="card bg-violet-50 border border-violet-200 rounded-lg p-4 shadow-sm text-center">
                    <div class="keyword text-lg mb-2">Apply Consistently</div>
                    <div class="text-sm text-gray-600 mb-1">/əˈplaɪ kənˈsɪstəntli/</div>
                    <div class="text-gray-700">在写作中一致地应用规则</div>
                </div>
            </div>
        </section>

        <!-- 标点符号的深度语法分析 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">标点符号的深度语法分析</h2>
            <p class="text-gray-700 mb-4">深入理解标点符号的语法机制，有助于我们在复杂的语言环境中准确运用这些重要的语言工具。</p>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">句法结构中的标点作用</h3>
            <div class="grid grid-cols-1 gap-4 mb-6">
                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">主从句关系标记</div>
                    <div class="keyword text-lg mb-1">Although the weather was terrible, which made driving dangerous, we decided to continue our journey because we had no other choice.</div>
                    <div class="text-sm text-gray-600 mb-1">/ɔlˈðoʊ ðə ˈwɛðər wʌz ˈtɛrəbəl wɪtʃ meɪd ˈdraɪvɪŋ ˈdeɪndʒərəs wi dɪˈsaɪdəd tu kənˈtɪnju ˈaʊər ˈdʒɜrni bɪˈkɔz wi hæd noʊ ˈʌðər tʃɔɪs/</div>
                    <div class="text-gray-700">尽管天气很糟糕，这使得开车很危险，我们还是决定继续我们的旅程，因为我们别无选择。</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">并列结构层次</div>
                    <div class="keyword text-lg mb-1">The company's success depends on three factors: innovative products that meet customer needs; efficient operations that reduce costs and improve quality; and strong leadership that inspires teams, drives change, and maintains vision.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈkʌmpəniz səkˈsɛs dɪˈpɛndz ɑn θri ˈfæktərz ˈɪnəˌveɪtɪv ˈprɑdʌkts ðæt mit ˈkʌstəmər nidz ɪˈfɪʃənt ˌɑpəˈreɪʃənz ðæt rɪˈdus kɔsts ænd ɪmˈpruv ˈkwɑləti ænd strɔŋ ˈlidərˌʃɪp ðæt ɪnˈspaɪərz timz draɪvz tʃeɪndʒ ænd meɪnˈteɪnz ˈvɪʒən/</div>
                    <div class="text-gray-700">公司的成功取决于三个因素：满足客户需求的创新产品；降低成本和提高质量的高效运营；以及激励团队、推动变革和保持愿景的强有力领导。</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">修饰语界定</div>
                    <div class="keyword text-lg mb-1">The research team, consisting of experts from various fields—biology, chemistry, physics, and mathematics—collaborated on this groundbreaking study, which took five years to complete.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə rɪˈsɜrtʃ tim kənˈsɪstɪŋ ʌv ˈɛkspɜrts frʌm ˈvɛriəs fildz baɪˈɑlədʒi ˈkɛməstri ˈfɪzɪks ænd ˌmæθəˈmætɪks kəˈlæbəˌreɪtəd ɑn ðɪs ˈgraʊndˌbreɪkɪŋ ˈstʌdi wɪtʃ tʊk faɪv jɪrz tu kəmˈplit/</div>
                    <div class="text-gray-700">由来自各个领域——生物学、化学、物理学和数学——的专家组成的研究团队合作完成了这项开创性研究，该研究耗时五年。</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">语义关系标示</div>
                    <div class="keyword text-lg mb-1">The results were unexpected: not only did the treatment work better than anticipated, but it also showed fewer side effects; moreover, the benefits appeared to be long-lasting—a finding that could revolutionize current medical practice.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə rɪˈzʌlts wər ˌʌnɪkˈspɛktəd nɑt ˈoʊnli dɪd ðə ˈtritmənt wɜrk ˈbɛtər ðæn ænˈtɪsəˌpeɪtəd bʌt ɪt ˈɔlsoʊ ʃoʊd ˈfjuər saɪd ɪˈfɛkts mɔrˈoʊvər ðə ˈbɛnəfɪts əˈpɪrd tu bi lɔŋ ˈlæstɪŋ ə ˈfaɪndɪŋ ðæt kʊd ˌrɛvəˈluʃəˌnaɪz ˈkɜrənt ˈmɛdəkəl ˈpræktəs/</div>
                    <div class="text-gray-700">结果出乎意料：治疗不仅比预期效果更好，而且副作用更少；此外，益处似乎是持久的——这一发现可能会彻底改变当前的医疗实践。</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">语篇连贯中的标点功能</h3>
            <div class="grid grid-cols-1 gap-4 mb-6">
                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">段落内部连接</div>
                    <div class="keyword text-lg mb-1">The study began with a simple question: How do environmental factors affect learning? Initial observations suggested multiple variables were involved; subsequent analysis revealed three primary factors. First, lighting conditions significantly impact concentration levels. Second, temperature affects cognitive performance. Third, noise levels influence memory retention.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈstʌdi bɪˈgæn wɪð ə ˈsɪmpəl ˈkwɛstʃən haʊ du ɪnˌvaɪrənˈmɛntəl ˈfæktərz əˈfɛkt ˈlɜrnɪŋ ɪˈnɪʃəl ˌɑbzərˈveɪʃənz səgˈdʒɛstəd ˈmʌltəpəl ˈvɛriəbəlz wər ɪnˈvɑlvd ˈsʌbsəkwənt əˈnæləsəs rɪˈvild θri ˈpraɪˌmɛri ˈfæktərz fɜrst ˈlaɪtɪŋ kənˈdɪʃənz sɪgˈnɪfəkəntli ˈɪmpækt ˌkɑnsənˈtreɪʃən ˈlɛvəlz ˈsɛkənd ˈtɛmpərətʃər əˈfɛkts ˈkɑgnətɪv pərˈfɔrməns θɜrd nɔɪz ˈlɛvəlz ˈɪnfluəns ˈmɛməri rɪˈtɛnʃən/</div>
                    <div class="text-gray-700">研究始于一个简单的问题：环境因素如何影响学习？初步观察表明涉及多个变量；后续分析揭示了三个主要因素。首先，照明条件显著影响注意力水平。其次，温度影响认知表现。第三，噪音水平影响记忆保持。</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">论证结构标记</div>
                    <div class="keyword text-lg mb-1">The evidence supports our hypothesis in several ways: (a) statistical analysis shows significant correlations (p<0.001); (b) qualitative data reveals consistent patterns across all groups; (c) longitudinal observations confirm the stability of these effects over time. However, limitations must be acknowledged: the sample size was relatively small (n=120); the study duration was limited (6 months); external validity may be restricted to similar populations.</div>
                    <div class="text-sm text-gray-600 mb-1">/ði ˈɛvədəns səˈpɔrts ˈaʊər haɪˈpɑθəsəs ɪn ˈsɛvərəl weɪz eɪ stəˈtɪstəkəl əˈnæləsəs ʃoʊz sɪgˈnɪfəkənt ˌkɔrəˈleɪʃənz pi lɛs ðæn ˈziroʊ pɔɪnt ˈziroʊ ˈziroʊ wʌn bi ˈkwɑləˌteɪtɪv ˈdeɪtə rɪˈvilz kənˈsɪstənt ˈpætərnz əˈkrɔs ɔl grups si ˌlɑndʒəˈtudənəl ˌɑbzərˈveɪʃənz kənˈfɜrm ðə stəˈbɪləti ʌv ðiz ɪˈfɛkts ˈoʊvər taɪm haʊˈɛvər ˌlɪməˈteɪʃənz mʌst bi ækˈnɑlɪdʒd ðə ˈsæmpəl saɪz wʌz ˈrɛlətɪvli smɔl ɛn ikwəlz wʌn ˈhʌndrəd ˈtwɛnti ðə ˈstʌdi dʊˈreɪʃən wʌz ˈlɪmətəd sɪks mʌnθs ɪkˈstɜrnəl vəˈlɪdəti meɪ bi rɪˈstrɪktəd tu ˈsɪmələr ˌpɑpjəˈleɪʃənz/</div>
                    <div class="text-gray-700">证据在几个方面支持我们的假设：（a）统计分析显示显著相关性（p<0.001）；（b）定性数据揭示所有组间的一致模式；（c）纵向观察证实这些效应随时间的稳定性。然而，必须承认局限性：样本量相对较小（n=120）；研究持续时间有限（6个月）；外部效度可能仅限于类似人群。</div>
                </div>

                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">转折与递进关系</div>
                    <div class="keyword text-lg mb-1">Traditional methods have proven effective in many contexts; nevertheless, emerging technologies offer new possibilities. While conventional approaches emphasize standardization—ensuring consistency across implementations—innovative solutions focus on personalization: adapting to individual needs, preferences, and circumstances. This shift represents more than a technological upgrade; it signifies a fundamental change in philosophy.</div>
                    <div class="text-sm text-gray-600 mb-1">/trəˈdɪʃənəl ˈmɛθədz hæv ˈpruvən ɪˈfɛktɪv ɪn ˈmɛni ˈkɑntɛksts ˌnɛvərðəˈlɛs ɪˈmɜrdʒɪŋ tɛkˈnɑlədʒiz ˈɔfər nu ˌpɑsəˈbɪlətiz waɪl kənˈvɛnʃənəl əˈproʊtʃəz ˈɛmfəˌsaɪz ˌstændərdəˈzeɪʃən ɪnˈʃʊrɪŋ kənˈsɪstənsi əˈkrɔs ˌɪmpləmənˈteɪʃənz ˈɪnəˌveɪtɪv səˈluʃənz ˈfoʊkəs ɑn ˌpɜrsənələˈzeɪʃən əˈdæptɪŋ tu ˌɪndəˈvɪdʒuəl nidz ˈprɛfərənsəz ænd ˈsɜrkəmˌstænsəz ðɪs ʃɪft ˌrɛprɪˈzɛnts mɔr ðæn ə ˌtɛknəˈlɑdʒəkəl ˈʌpˌgreɪd ɪt ˈsɪgnəˌfaɪz ə ˌfʌndəˈmɛntəl tʃeɪndʒ ɪn fəˈlɑsəfi/</div>
                    <div class="text-gray-700">传统方法在许多情况下已被证明有效；然而，新兴技术提供了新的可能性。虽然传统方法强调标准化——确保实施的一致性——创新解决方案专注于个性化：适应个人需求、偏好和环境。这种转变不仅仅是技术升级；它标志着哲学的根本改变。</div>
                </div>
            </div>
        </section>

        <!-- 标点符号的修辞效果深度分析 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">标点符号的修辞效果深度分析</h2>
            <p class="text-gray-700 mb-4">标点符号在修辞学中扮演着重要角色，它们不仅影响文本的节奏和语调，更能创造独特的修辞效果，增强表达的说服力和感染力。</p>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">节奏与韵律控制</h3>
            <div class="grid grid-cols-1 gap-4 mb-6">
                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">渐进式节奏</div>
                    <div class="keyword text-lg mb-1">The storm approached slowly... then faster... faster still... until it engulfed everything in its path—trees, houses, dreams, hopes—leaving only silence in its wake.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə stɔrm əˈproʊtʃt ˈsloʊli ðɛn ˈfæstər ˈfæstər stɪl ənˈtɪl ɪt ɪnˈgʌlft ˈɛvriθɪŋ ɪn ɪts pæθ triz ˈhaʊsəz drimz hoʊps ˈlivɪŋ ˈoʊnli ˈsaɪləns ɪn ɪts weɪk/</div>
                    <div class="text-gray-700">暴风雨缓慢地接近...然后更快...更快...直到它吞没了路径上的一切——树木、房屋、梦想、希望——只留下寂静。</div>
                </div>

                <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">对比式节奏</div>
                    <div class="keyword text-lg mb-1">Success demands sacrifice: early mornings, late nights, missed celebrations, postponed pleasures. Yet failure—failure demands nothing and gives nothing in return.</div>
                    <div class="text-sm text-gray-600 mb-1">/səkˈsɛs dɪˈmændz ˈsækrəˌfaɪs ˈɜrli ˈmɔrnɪŋz leɪt naɪts mɪst ˌsɛləˈbreɪʃənz poʊstˈpoʊnd ˈplɛʒərz jɛt ˈfeɪljər ˈfeɪljər dɪˈmændz ˈnʌθɪŋ ænd gɪvz ˈnʌθɪŋ ɪn rɪˈtɜrn/</div>
                    <div class="text-gray-700">成功需要牺牲：早起、熬夜、错过庆祝、推迟享乐。然而失败——失败什么都不要求，也什么都不给予回报。</div>
                </div>

                <div class="card bg-lime-50 border border-lime-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">悬念式节奏</div>
                    <div class="keyword text-lg mb-1">The envelope lay on the table, unopened, untouched... containing either the answer to her prayers or the end of her dreams. She reached for it... hesitated... withdrew her hand... then, with sudden determination, tore it open.</div>
                    <div class="text-sm text-gray-600 mb-1">/ði ˈɛnvəˌloʊp leɪ ɑn ðə ˈteɪbəl ʌnˈoʊpənd ʌnˈtʌtʃt kənˈteɪnɪŋ ˈiðər ði ˈænsər tu hər prɛrz ɔr ði ɛnd ʌv hər drimz ʃi ritʃt fɔr ɪt ˈhɛzəˌteɪtəd wɪðˈdru hər hænd ðɛn wɪð ˈsʌdən dɪˌtɜrməˈneɪʃən tɔr ɪt ˈoʊpən/</div>
                    <div class="text-gray-700">信封躺在桌子上，未开封，未触碰...里面要么是她祈祷的答案，要么是她梦想的终结。她伸手去拿...犹豫了...收回了手...然后，带着突然的决心，撕开了它。</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">情感强化技巧</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">愤怒表达</div>
                    <div class="keyword text-lg mb-1">How dare they! After everything we've done—the sacrifices, the compromises, the endless patience—they have the audacity to question our commitment?!</div>
                    <div class="text-sm text-gray-600 mb-1">/haʊ dɛr ðeɪ ˈæftər ˈɛvriθɪŋ wiv dʌn ðə ˈsækrəˌfaɪsəz ðə ˈkɑmprəˌmaɪzəz ði ˈɛndləs ˈpeɪʃəns ðeɪ hæv ði ɔˈdæsəti tu ˈkwɛstʃən ˈaʊər kəˈmɪtmənt/</div>
                    <div class="text-gray-700">他们怎么敢！在我们所做的一切之后——牺牲、妥协、无尽的耐心——他们竟然有胆量质疑我们的承诺？！</div>
                </div>

                <div class="card bg-rose-50 border border-rose-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">悲伤表达</div>
                    <div class="keyword text-lg mb-1">She was gone... just like that... no warning, no goodbye, no chance to say the words that mattered most. The silence that followed was deafening.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi wʌz gɔn dʒʌst laɪk ðæt noʊ ˈwɔrnɪŋ noʊ gʊdˈbaɪ noʊ tʃæns tu seɪ ðə wɜrdz ðæt ˈmætərd moʊst ðə ˈsaɪləns ðæt ˈfɑloʊd wʌz ˈdɛfənɪŋ/</div>
                    <div class="text-gray-700">她走了...就这样...没有警告，没有告别，没有机会说出最重要的话。随之而来的沉默震耳欲聋。</div>
                </div>

                <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">喜悦表达</div>
                    <div class="keyword text-lg mb-1">Victory! At last—after years of struggle, months of preparation, weeks of anxiety—the moment had arrived, and it was everything they had dreamed it would be!</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈvɪktəri æt læst ˈæftər jɪrz ʌv ˈstrʌgəl mʌnθs ʌv ˌprɛpəˈreɪʃən wiks ʌv æŋˈzaɪəti ðə ˈmoʊmənt hæd əˈraɪvd ænd ɪt wʌz ˈɛvriθɪŋ ðeɪ hæd drimd ɪt wʊd bi/</div>
                    <div class="text-gray-700">胜利！终于——经过多年的奋斗、数月的准备、数周的焦虑——这一刻到来了，它就是他们梦想中的一切！</div>
                </div>

                <div class="card bg-amber-50 border border-amber-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">恐惧表达</div>
                    <div class="keyword text-lg mb-1">Something was wrong... terribly wrong. The house was too quiet, the shadows too deep, the air too still. Every instinct screamed: "Run!"</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈsʌmθɪŋ wʌz rɔŋ ˈtɛrəbli rɔŋ ðə haʊs wʌz tu ˈkwaɪət ðə ˈʃædoʊz tu dip ði ɛr tu stɪl ˈɛvri ˈɪnstɪŋkt skrimd rʌn/</div>
                    <div class="text-gray-700">有什么不对劲...非常不对劲。房子太安静，阴影太深，空气太静止。每一个本能都在尖叫："跑！"</div>
                </div>
            </div>
        </section>

        <!-- 标点符号的最终掌握要点 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">标点符号的最终掌握要点</h2>
            <p class="text-gray-700 mb-4">通过深入学习标点符号的语法功能、修辞效果和实际应用，我们可以显著提升英语表达的准确性、流畅性和感染力。标点符号是语言艺术的重要组成部分，掌握它们就是掌握了更精确、更生动的表达方式。</p>

            <div class="bg-gradient-to-br from-blue-50 via-purple-50 to-pink-50 border border-blue-200 rounded-lg p-8 text-center">
                <h3 class="text-2xl font-bold mb-4 text-gray-800">标点符号：语言表达的精髓</h3>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-6 mb-6">
                    <div>
                        <div class="keyword text-4xl mb-2">.</div>
                        <p class="text-sm font-semibold text-gray-700">Period</p>
                        <p class="text-xs text-gray-600">Finality & Completion</p>
                    </div>
                    <div>
                        <div class="keyword text-4xl mb-2">,</div>
                        <p class="text-sm font-semibold text-gray-700">Comma</p>
                        <p class="text-xs text-gray-600">Separation & Connection</p>
                    </div>
                    <div>
                        <div class="keyword text-4xl mb-2">;</div>
                        <p class="text-sm font-semibold text-gray-700">Semicolon</p>
                        <p class="text-xs text-gray-600">Balance & Relationship</p>
                    </div>
                    <div>
                        <div class="keyword text-4xl mb-2">:</div>
                        <p class="text-sm font-semibold text-gray-700">Colon</p>
                        <p class="text-xs text-gray-600">Introduction & Explanation</p>
                    </div>
                </div>

                <div class="border-t border-gray-200 pt-6">
                    <p class="text-lg text-gray-800 mb-2">
                        <span class="keyword">"Punctuation is the art of dividing a written composition into sentences, or parts of sentences, by points or stops, for the purpose of marking the different pauses which the sense, and an accurate pronunciation require."</span>
                    </p>
                    <p class="text-sm text-gray-600 mb-1">/ˌpʌŋktʃuˈeɪʃən ɪz ði ɑrt ʌv dɪˈvaɪdɪŋ ə ˈrɪtən ˌkɑmpəˈzɪʃən ˈɪntu ˈsɛntənsəz ɔr pɑrts ʌv ˈsɛntənsəz baɪ pɔɪnts ɔr stɑps fɔr ðə ˈpɜrpəs ʌv ˈmɑrkɪŋ ðə ˈdɪfərənt ˈpɔzəz wɪtʃ ðə sɛns ænd æn ˈækjərət prəˌnʌnsiˈeɪʃən rɪˈkwaɪər/</p>
                    <p class="text-gray-700">"标点符号是通过点或停顿将书面作品分为句子或句子部分的艺术，目的是标记语义和准确发音所需要的不同停顿。"</p>
                </div>
            </div>
        </section>
    </div>
</body>
</html>
