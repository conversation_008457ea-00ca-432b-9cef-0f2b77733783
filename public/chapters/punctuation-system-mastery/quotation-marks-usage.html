<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>引号使用规范</title>
    <script src="/static/tailwindcss-3.4.17.js"></script>
    <style>
        .keyword {
            color: #3d74ed;
            font-weight: bold;
        }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        }
    </style>
</head>
<body class="bg-white">
    <div class="p-6">
        <!-- 引号基础概念 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">引号的基本形式</h2>
            <p class="text-gray-700 mb-4">英语中的引号主要有两种形式：双引号（" "）和单引号（' '）。双引号是最常用的形式，用于标记直接引语。单引号通常用于引语中的引语或特殊用途。</p>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">双引号示例</div>
                    <div class="keyword text-lg mb-1">"Hello, world!"</div>
                    <div class="text-sm text-gray-600 mb-1">/həˈloʊ wɜrld/</div>
                    <div class="text-gray-700">"你好，世界！"</div>
                </div>
                
                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">单引号示例</div>
                    <div class="keyword text-lg mb-1">'Good morning'</div>
                    <div class="text-sm text-gray-600 mb-1">/ɡʊd ˈmɔrnɪŋ/</div>
                    <div class="text-gray-700">'早上好'</div>
                </div>
            </div>
        </section>

        <!-- 直接引语 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">直接引语的使用</h2>
            <p class="text-gray-700 mb-4">直接引语是指完全按照说话人的原话进行引用，需要用引号将所引用的内容完整地包围起来。直接引语保持原话的时态、人称和语气。</p>
            
            <h3 class="text-xl font-semibold mb-3 text-gray-800">基本陈述句的直接引语</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">简单陈述</div>
                    <div class="keyword text-lg mb-1">She said, "I am happy."</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi sɛd aɪ æm ˈhæpi/</div>
                    <div class="text-gray-700">她说："我很高兴。"</div>
                </div>
                
                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">现在进行时</div>
                    <div class="keyword text-lg mb-1">He said, "I am working."</div>
                    <div class="text-sm text-gray-600 mb-1">/hi sɛd aɪ æm ˈwɜrkɪŋ/</div>
                    <div class="text-gray-700">他说："我正在工作。"</div>
                </div>
                
                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">过去时态</div>
                    <div class="keyword text-lg mb-1">Mary said, "I went home."</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈmɛri sɛd aɪ wɛnt hoʊm/</div>
                    <div class="text-gray-700">玛丽说："我回家了。"</div>
                </div>
                
                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">将来时态</div>
                    <div class="keyword text-lg mb-1">Tom said, "I will come."</div>
                    <div class="text-sm text-gray-600 mb-1">/tɑm sɛd aɪ wɪl kʌm/</div>
                    <div class="text-gray-700">汤姆说："我会来的。"</div>
                </div>
                
                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">完成时态</div>
                    <div class="keyword text-lg mb-1">She said, "I have finished."</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi sɛd aɪ hæv ˈfɪnɪʃt/</div>
                    <div class="text-gray-700">她说："我已经完成了。"</div>
                </div>
                
                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">否定句</div>
                    <div class="keyword text-lg mb-1">He said, "I don't know."</div>
                    <div class="text-sm text-gray-600 mb-1">/hi sɛd aɪ doʊnt noʊ/</div>
                    <div class="text-gray-700">他说："我不知道。"</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">疑问句的直接引语</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">一般疑问句</div>
                    <div class="keyword text-lg mb-1">She asked, "Are you ready?"</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi æskt ɑr ju ˈrɛdi/</div>
                    <div class="text-gray-700">她问："你准备好了吗？"</div>
                </div>
                
                <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">特殊疑问句</div>
                    <div class="keyword text-lg mb-1">He asked, "What time is it?"</div>
                    <div class="text-sm text-gray-600 mb-1">/hi æskt wʌt taɪm ɪz ɪt/</div>
                    <div class="text-gray-700">他问："现在几点了？"</div>
                </div>
                
                <div class="card bg-lime-50 border border-lime-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">选择疑问句</div>
                    <div class="keyword text-lg mb-1">She asked, "Tea or coffee?"</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi æskt ti ɔr ˈkɔfi/</div>
                    <div class="text-gray-700">她问："茶还是咖啡？"</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">感叹句和祈使句的直接引语</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-rose-50 border border-rose-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">感叹句</div>
                    <div class="keyword text-lg mb-1">He shouted, "What a surprise!"</div>
                    <div class="text-sm text-gray-600 mb-1">/hi ˈʃaʊtɪd wʌt ə sərˈpraɪz/</div>
                    <div class="text-gray-700">他喊道："真是个惊喜！"</div>
                </div>
                
                <div class="card bg-amber-50 border border-amber-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">祈使句</div>
                    <div class="keyword text-lg mb-1">She said, "Please sit down."</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi sɛd pliz sɪt daʊn/</div>
                    <div class="text-gray-700">她说："请坐下。"</div>
                </div>
                
                <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">命令句</div>
                    <div class="keyword text-lg mb-1">The teacher said, "Open your books."</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈtiʧər sɛd ˈoʊpən jʊr bʊks/</div>
                    <div class="text-gray-700">老师说："打开你们的书。"</div>
                </div>
            </div>
        </section>

        <!-- 间接引语 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">间接引语的使用</h2>
            <p class="text-gray-700 mb-4">间接引语是指不直接引用说话人的原话，而是用自己的话来转述别人说过的内容。间接引语不使用引号，通常需要改变时态、人称和某些词语。</p>
            
            <h3 class="text-xl font-semibold mb-3 text-gray-800">陈述句的间接引语转换</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">直接引语</div>
                    <div class="keyword text-lg mb-1">She said, "I am tired."</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi sɛd aɪ æm ˈtaɪərd/</div>
                    <div class="text-gray-700">她说："我累了。"</div>
                </div>
                
                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">间接引语</div>
                    <div class="keyword text-lg mb-1">She said that she was tired.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi sɛd ðæt ʃi wʌz ˈtaɪərd/</div>
                    <div class="text-gray-700">她说她累了。</div>
                </div>
            </div>
        </section>

        <!-- 时态变化规则 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">间接引语中的时态变化</h2>
            <p class="text-gray-700 mb-4">当主句的谓语动词是过去时态时，间接引语中的时态通常需要向过去推移一个时态。这被称为"时态后移"规则。</p>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">一般现在时 → 一般过去时</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">直接引语</div>
                    <div class="keyword text-lg mb-1">He said, "I work here."</div>
                    <div class="text-sm text-gray-600 mb-1">/hi sɛd aɪ wɜrk hɪr/</div>
                    <div class="text-gray-700">他说："我在这里工作。"</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">间接引语</div>
                    <div class="keyword text-lg mb-1">He said that he worked there.</div>
                    <div class="text-sm text-gray-600 mb-1">/hi sɛd ðæt hi wɜrkt ðɛr/</div>
                    <div class="text-gray-700">他说他在那里工作。</div>
                </div>

                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">直接引语</div>
                    <div class="keyword text-lg mb-1">She said, "I like music."</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi sɛd aɪ laɪk ˈmjuzɪk/</div>
                    <div class="text-gray-700">她说："我喜欢音乐。"</div>
                </div>

                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">间接引语</div>
                    <div class="keyword text-lg mb-1">She said that she liked music.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi sɛd ðæt ʃi laɪkt ˈmjuzɪk/</div>
                    <div class="text-gray-700">她说她喜欢音乐。</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">现在进行时 → 过去进行时</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">直接引语</div>
                    <div class="keyword text-lg mb-1">Tom said, "I am reading."</div>
                    <div class="text-sm text-gray-600 mb-1">/tɑm sɛd aɪ æm ˈridɪŋ/</div>
                    <div class="text-gray-700">汤姆说："我正在读书。"</div>
                </div>

                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">间接引语</div>
                    <div class="keyword text-lg mb-1">Tom said that he was reading.</div>
                    <div class="text-sm text-gray-600 mb-1">/tɑm sɛd ðæt hi wʌz ˈridɪŋ/</div>
                    <div class="text-gray-700">汤姆说他正在读书。</div>
                </div>

                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">直接引语</div>
                    <div class="keyword text-lg mb-1">Mary said, "I am cooking."</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈmɛri sɛd aɪ æm ˈkʊkɪŋ/</div>
                    <div class="text-gray-700">玛丽说："我正在做饭。"</div>
                </div>

                <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">间接引语</div>
                    <div class="keyword text-lg mb-1">Mary said that she was cooking.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈmɛri sɛd ðæt ʃi wʌz ˈkʊkɪŋ/</div>
                    <div class="text-gray-700">玛丽说她正在做饭。</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">一般将来时 → 过去将来时</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-lime-50 border border-lime-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">直接引语</div>
                    <div class="keyword text-lg mb-1">He said, "I will go tomorrow."</div>
                    <div class="text-sm text-gray-600 mb-1">/hi sɛd aɪ wɪl goʊ təˈmɑroʊ/</div>
                    <div class="text-gray-700">他说："我明天会去。"</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">间接引语</div>
                    <div class="keyword text-lg mb-1">He said that he would go the next day.</div>
                    <div class="text-sm text-gray-600 mb-1">/hi sɛd ðæt hi wʊd goʊ ðə nɛkst deɪ/</div>
                    <div class="text-gray-700">他说他第二天会去。</div>
                </div>

                <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">直接引语</div>
                    <div class="keyword text-lg mb-1">She said, "I will call you."</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi sɛd aɪ wɪl kɔl ju/</div>
                    <div class="text-gray-700">她说："我会给你打电话。"</div>
                </div>

                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">间接引语</div>
                    <div class="keyword text-lg mb-1">She said that she would call me.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi sɛd ðæt ʃi wʊd kɔl mi/</div>
                    <div class="text-gray-700">她说她会给我打电话。</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">现在完成时 → 过去完成时</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-rose-50 border border-rose-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">直接引语</div>
                    <div class="keyword text-lg mb-1">John said, "I have finished."</div>
                    <div class="text-sm text-gray-600 mb-1">/ʤɑn sɛd aɪ hæv ˈfɪnɪʃt/</div>
                    <div class="text-gray-700">约翰说："我已经完成了。"</div>
                </div>

                <div class="card bg-amber-50 border border-amber-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">间接引语</div>
                    <div class="keyword text-lg mb-1">John said that he had finished.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʤɑn sɛd ðæt hi hæd ˈfɪnɪʃt/</div>
                    <div class="text-gray-700">约翰说他已经完成了。</div>
                </div>
            </div>
        </section>

        <!-- 人称代词变化 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">人称代词的变化</h2>
            <p class="text-gray-700 mb-4">在间接引语中，人称代词需要根据说话人和听话人的关系进行相应的变化。第一人称通常变为第三人称，第二人称根据具体情况变化。</p>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">第一人称的变化</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-violet-50 border border-violet-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">I → he/she</div>
                    <div class="keyword text-lg mb-1">Tom said, "I am busy."</div>
                    <div class="text-sm text-gray-600 mb-1">/tɑm sɛd aɪ æm ˈbɪzi/</div>
                    <div class="text-gray-700">汤姆说："我很忙。"</div>
                </div>

                <div class="card bg-fuchsia-50 border border-fuchsia-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">间接引语</div>
                    <div class="keyword text-lg mb-1">Tom said that he was busy.</div>
                    <div class="text-sm text-gray-600 mb-1">/tɑm sɛd ðæt hi wʌz ˈbɪzi/</div>
                    <div class="text-gray-700">汤姆说他很忙。</div>
                </div>

                <div class="card bg-sky-50 border border-sky-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">my → his/her</div>
                    <div class="keyword text-lg mb-1">She said, "My car is new."</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi sɛd maɪ kɑr ɪz nu/</div>
                    <div class="text-gray-700">她说："我的车是新的。"</div>
                </div>

                <div class="card bg-slate-50 border border-slate-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">间接引语</div>
                    <div class="keyword text-lg mb-1">She said that her car was new.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi sɛd ðæt hər kɑr wʌz nu/</div>
                    <div class="text-gray-700">她说她的车是新的。</div>
                </div>

                <div class="card bg-stone-50 border border-stone-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">we → they</div>
                    <div class="keyword text-lg mb-1">They said, "We are ready."</div>
                    <div class="text-sm text-gray-600 mb-1">/ðeɪ sɛd wi ɑr ˈrɛdi/</div>
                    <div class="text-gray-700">他们说："我们准备好了。"</div>
                </div>

                <div class="card bg-neutral-50 border border-neutral-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">间接引语</div>
                    <div class="keyword text-lg mb-1">They said that they were ready.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðeɪ sɛd ðæt ðeɪ wər ˈrɛdi/</div>
                    <div class="text-gray-700">他们说他们准备好了。</div>
                </div>
            </div>
        </section>

        <!-- 时间和地点状语的变化 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">时间和地点状语的变化</h2>
            <p class="text-gray-700 mb-4">在间接引语中，时间和地点状语需要根据转述时的实际情况进行调整，以保持语义的准确性。</p>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">时间状语的变化</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">today → that day</div>
                    <div class="keyword text-lg mb-1">"I will go today."</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ wɪl goʊ təˈdeɪ/</div>
                    <div class="text-gray-700">"我今天会去。"</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">间接引语</div>
                    <div class="keyword text-lg mb-1">He said he would go that day.</div>
                    <div class="text-sm text-gray-600 mb-1">/hi sɛd hi wʊd goʊ ðæt deɪ/</div>
                    <div class="text-gray-700">他说他那天会去。</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">tomorrow → the next day</div>
                    <div class="keyword text-lg mb-1">"I will call tomorrow."</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ wɪl kɔl təˈmɑroʊ/</div>
                    <div class="text-gray-700">"我明天会打电话。"</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">间接引语</div>
                    <div class="keyword text-lg mb-1">She said she would call the next day.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi sɛd ʃi wʊd kɔl ðə nɛkst deɪ/</div>
                    <div class="text-gray-700">她说她第二天会打电话。</div>
                </div>

                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">yesterday → the day before</div>
                    <div class="keyword text-lg mb-1">"I saw him yesterday."</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ sɔ hɪm ˈjɛstərdeɪ/</div>
                    <div class="text-gray-700">"我昨天见到了他。"</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">间接引语</div>
                    <div class="keyword text-lg mb-1">He said he had seen him the day before.</div>
                    <div class="text-sm text-gray-600 mb-1">/hi sɛd hi hæd sin hɪm ðə deɪ bɪˈfɔr/</div>
                    <div class="text-gray-700">他说他前一天见到了他。</div>
                </div>

                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">now → then</div>
                    <div class="keyword text-lg mb-1">"I am busy now."</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ æm ˈbɪzi naʊ/</div>
                    <div class="text-gray-700">"我现在很忙。"</div>
                </div>

                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">间接引语</div>
                    <div class="keyword text-lg mb-1">She said she was busy then.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi sɛd ʃi wʌz ˈbɪzi ðɛn/</div>
                    <div class="text-gray-700">她说她那时很忙。</div>
                </div>

                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">this week → that week</div>
                    <div class="keyword text-lg mb-1">"I will finish this week."</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ wɪl ˈfɪnɪʃ ðɪs wik/</div>
                    <div class="text-gray-700">"我这周会完成。"</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">地点状语的变化</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">here → there</div>
                    <div class="keyword text-lg mb-1">"I work here."</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ wɜrk hɪr/</div>
                    <div class="text-gray-700">"我在这里工作。"</div>
                </div>

                <div class="card bg-lime-50 border border-lime-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">间接引语</div>
                    <div class="keyword text-lg mb-1">He said he worked there.</div>
                    <div class="text-sm text-gray-600 mb-1">/hi sɛd hi wɜrkt ðɛr/</div>
                    <div class="text-gray-700">他说他在那里工作。</div>
                </div>

                <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">this place → that place</div>
                    <div class="keyword text-lg mb-1">"I like this place."</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ laɪk ðɪs pleɪs/</div>
                    <div class="text-gray-700">"我喜欢这个地方。"</div>
                </div>

                <div class="card bg-rose-50 border border-rose-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">间接引语</div>
                    <div class="keyword text-lg mb-1">She said she liked that place.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi sɛd ʃi laɪkt ðæt pleɪs/</div>
                    <div class="text-gray-700">她说她喜欢那个地方。</div>
                </div>
            </div>
        </section>

        <!-- 疑问句的间接引语 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">疑问句的间接引语</h2>
            <p class="text-gray-700 mb-4">疑问句转换为间接引语时，需要将疑问句改为陈述句语序，并使用适当的连接词。一般疑问句用whether或if连接，特殊疑问句保留疑问词作连接词。</p>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">一般疑问句的转换</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-amber-50 border border-amber-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">直接引语</div>
                    <div class="keyword text-lg mb-1">He asked, "Are you ready?"</div>
                    <div class="text-sm text-gray-600 mb-1">/hi æskt ɑr ju ˈrɛdi/</div>
                    <div class="text-gray-700">他问："你准备好了吗？"</div>
                </div>

                <div class="card bg-violet-50 border border-violet-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">间接引语</div>
                    <div class="keyword text-lg mb-1">He asked if I was ready.</div>
                    <div class="text-sm text-gray-600 mb-1">/hi æskt ɪf aɪ wʌz ˈrɛdi/</div>
                    <div class="text-gray-700">他问我是否准备好了。</div>
                </div>

                <div class="card bg-fuchsia-50 border border-fuchsia-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">直接引语</div>
                    <div class="keyword text-lg mb-1">She asked, "Do you like coffee?"</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi æskt du ju laɪk ˈkɔfi/</div>
                    <div class="text-gray-700">她问："你喜欢咖啡吗？"</div>
                </div>

                <div class="card bg-sky-50 border border-sky-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">间接引语</div>
                    <div class="keyword text-lg mb-1">She asked whether I liked coffee.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi æskt ˈwɛðər aɪ laɪkt ˈkɔfi/</div>
                    <div class="text-gray-700">她问我是否喜欢咖啡。</div>
                </div>

                <div class="card bg-slate-50 border border-slate-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">直接引语</div>
                    <div class="keyword text-lg mb-1">Tom asked, "Can you help me?"</div>
                    <div class="text-sm text-gray-600 mb-1">/tɑm æskt kæn ju hɛlp mi/</div>
                    <div class="text-gray-700">汤姆问："你能帮我吗？"</div>
                </div>

                <div class="card bg-stone-50 border border-stone-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">间接引语</div>
                    <div class="keyword text-lg mb-1">Tom asked if I could help him.</div>
                    <div class="text-sm text-gray-600 mb-1">/tɑm æskt ɪf aɪ kʊd hɛlp hɪm/</div>
                    <div class="text-gray-700">汤姆问我是否能帮他。</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">特殊疑问句的转换</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-neutral-50 border border-neutral-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">what疑问句</div>
                    <div class="keyword text-lg mb-1">She asked, "What are you doing?"</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi æskt wʌt ɑr ju ˈduɪŋ/</div>
                    <div class="text-gray-700">她问："你在做什么？"</div>
                </div>

                <div class="card bg-zinc-50 border border-zinc-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">间接引语</div>
                    <div class="keyword text-lg mb-1">She asked what I was doing.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi æskt wʌt aɪ wʌz ˈduɪŋ/</div>
                    <div class="text-gray-700">她问我在做什么。</div>
                </div>
            </div>
        </section>

        <!-- 更多特殊疑问句转换 -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-3 text-gray-800">各种疑问词的转换示例</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">where疑问句</div>
                    <div class="keyword text-lg mb-1">"Where do you live?"</div>
                    <div class="text-sm text-gray-600 mb-1">/wɛr du ju lɪv/</div>
                    <div class="text-gray-700">"你住在哪里？"</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">间接引语</div>
                    <div class="keyword text-lg mb-1">He asked where I lived.</div>
                    <div class="text-sm text-gray-600 mb-1">/hi æskt wɛr aɪ lɪvd/</div>
                    <div class="text-gray-700">他问我住在哪里。</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">when疑问句</div>
                    <div class="keyword text-lg mb-1">"When will you come?"</div>
                    <div class="text-sm text-gray-600 mb-1">/wɛn wɪl ju kʌm/</div>
                    <div class="text-gray-700">"你什么时候来？"</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">间接引语</div>
                    <div class="keyword text-lg mb-1">She asked when I would come.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi æskt wɛn aɪ wʊd kʌm/</div>
                    <div class="text-gray-700">她问我什么时候来。</div>
                </div>

                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">why疑问句</div>
                    <div class="keyword text-lg mb-1">"Why are you late?"</div>
                    <div class="text-sm text-gray-600 mb-1">/waɪ ɑr ju leɪt/</div>
                    <div class="text-gray-700">"你为什么迟到？"</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">间接引语</div>
                    <div class="keyword text-lg mb-1">He asked why I was late.</div>
                    <div class="text-sm text-gray-600 mb-1">/hi æskt waɪ aɪ wʌz leɪt/</div>
                    <div class="text-gray-700">他问我为什么迟到。</div>
                </div>

                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">how疑问句</div>
                    <div class="keyword text-lg mb-1">"How do you feel?"</div>
                    <div class="text-sm text-gray-600 mb-1">/haʊ du ju fil/</div>
                    <div class="text-gray-700">"你感觉怎么样？"</div>
                </div>

                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">间接引语</div>
                    <div class="keyword text-lg mb-1">She asked how I felt.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi æskt haʊ aɪ fɛlt/</div>
                    <div class="text-gray-700">她问我感觉怎么样。</div>
                </div>

                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">who疑问句</div>
                    <div class="keyword text-lg mb-1">"Who is that person?"</div>
                    <div class="text-sm text-gray-600 mb-1">/hu ɪz ðæt ˈpɜrsən/</div>
                    <div class="text-gray-700">"那个人是谁？"</div>
                </div>

                <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">间接引语</div>
                    <div class="keyword text-lg mb-1">He asked who that person was.</div>
                    <div class="text-sm text-gray-600 mb-1">/hi æskt hu ðæt ˈpɜrsən wʌz/</div>
                    <div class="text-gray-700">他问那个人是谁。</div>
                </div>

                <div class="card bg-lime-50 border border-lime-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">which疑问句</div>
                    <div class="keyword text-lg mb-1">"Which book do you prefer?"</div>
                    <div class="text-sm text-gray-600 mb-1">/wɪʧ bʊk du ju prɪˈfɜr/</div>
                    <div class="text-gray-700">"你更喜欢哪本书？"</div>
                </div>

                <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">间接引语</div>
                    <div class="keyword text-lg mb-1">She asked which book I preferred.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi æskt wɪʧ bʊk aɪ prɪˈfɜrd/</div>
                    <div class="text-gray-700">她问我更喜欢哪本书。</div>
                </div>
            </div>
        </section>

        <!-- 祈使句的间接引语 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">祈使句的间接引语</h2>
            <p class="text-gray-700 mb-4">祈使句转换为间接引语时，通常使用"tell/ask/order + 宾语 + to do"的结构。肯定祈使句用"to do"，否定祈使句用"not to do"。</p>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">肯定祈使句的转换</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-rose-50 border border-rose-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">直接引语</div>
                    <div class="keyword text-lg mb-1">He said, "Close the door."</div>
                    <div class="text-sm text-gray-600 mb-1">/hi sɛd kloʊs ðə dɔr/</div>
                    <div class="text-gray-700">他说："关门。"</div>
                </div>

                <div class="card bg-amber-50 border border-amber-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">间接引语</div>
                    <div class="keyword text-lg mb-1">He told me to close the door.</div>
                    <div class="text-sm text-gray-600 mb-1">/hi toʊld mi tu kloʊs ðə dɔr/</div>
                    <div class="text-gray-700">他叫我关门。</div>
                </div>

                <div class="card bg-violet-50 border border-violet-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">直接引语</div>
                    <div class="keyword text-lg mb-1">She said, "Please help me."</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi sɛd pliz hɛlp mi/</div>
                    <div class="text-gray-700">她说："请帮助我。"</div>
                </div>

                <div class="card bg-fuchsia-50 border border-fuchsia-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">间接引语</div>
                    <div class="keyword text-lg mb-1">She asked me to help her.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi æskt mi tu hɛlp hər/</div>
                    <div class="text-gray-700">她请我帮助她。</div>
                </div>

                <div class="card bg-sky-50 border border-sky-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">直接引语</div>
                    <div class="keyword text-lg mb-1">The teacher said, "Study hard."</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈtiʧər sɛd ˈstʌdi hɑrd/</div>
                    <div class="text-gray-700">老师说："努力学习。"</div>
                </div>

                <div class="card bg-slate-50 border border-slate-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">间接引语</div>
                    <div class="keyword text-lg mb-1">The teacher told us to study hard.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈtiʧər toʊld ʌs tu ˈstʌdi hɑrd/</div>
                    <div class="text-gray-700">老师叫我们努力学习。</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">否定祈使句的转换</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-stone-50 border border-stone-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">直接引语</div>
                    <div class="keyword text-lg mb-1">He said, "Don't be late."</div>
                    <div class="text-sm text-gray-600 mb-1">/hi sɛd doʊnt bi leɪt/</div>
                    <div class="text-gray-700">他说："不要迟到。"</div>
                </div>

                <div class="card bg-neutral-50 border border-neutral-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">间接引语</div>
                    <div class="keyword text-lg mb-1">He told me not to be late.</div>
                    <div class="text-sm text-gray-600 mb-1">/hi toʊld mi nɑt tu bi leɪt/</div>
                    <div class="text-gray-700">他叫我不要迟到。</div>
                </div>

                <div class="card bg-zinc-50 border border-zinc-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">直接引语</div>
                    <div class="keyword text-lg mb-1">She said, "Don't worry."</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi sɛd doʊnt ˈwɜri/</div>
                    <div class="text-gray-700">她说："不要担心。"</div>
                </div>

                <div class="card bg-gray-50 border border-gray-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">间接引语</div>
                    <div class="keyword text-lg mb-1">She told me not to worry.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi toʊld mi nɑt tu ˈwɜri/</div>
                    <div class="text-gray-700">她叫我不要担心。</div>
                </div>
            </div>
        </section>

        <!-- 感叹句的间接引语 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">感叹句的间接引语</h2>
            <p class="text-gray-700 mb-4">感叹句转换为间接引语时，通常使用"exclaim/cry out/shout"等动词，并将感叹句改为陈述句形式。</p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">直接引语</div>
                    <div class="keyword text-lg mb-1">He said, "What a beautiful day!"</div>
                    <div class="text-sm text-gray-600 mb-1">/hi sɛd wʌt ə ˈbjutəfəl deɪ/</div>
                    <div class="text-gray-700">他说："多么美好的一天！"</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">间接引语</div>
                    <div class="keyword text-lg mb-1">He exclaimed that it was a beautiful day.</div>
                    <div class="text-sm text-gray-600 mb-1">/hi ɪkˈskleɪmd ðæt ɪt wʌz ə ˈbjutəfəl deɪ/</div>
                    <div class="text-gray-700">他感叹说那是美好的一天。</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">直接引语</div>
                    <div class="keyword text-lg mb-1">She cried, "How wonderful!"</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi kraɪd haʊ ˈwʌndərfəl/</div>
                    <div class="text-gray-700">她叫道："多么精彩！"</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">间接引语</div>
                    <div class="keyword text-lg mb-1">She cried out that it was wonderful.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi kraɪd aʊt ðæt ɪt wʌz ˈwʌndərfəl/</div>
                    <div class="text-gray-700">她叫道说那很精彩。</div>
                </div>
            </div>
        </section>

        <!-- 引号与其他标点符号的配合使用 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">引号与其他标点符号的配合</h2>
            <p class="text-gray-700 mb-4">引号与其他标点符号配合使用时，需要遵循特定的规则。句号、逗号通常放在引号内，而问号、感叹号的位置取决于它们是否属于引用内容。</p>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">引号与逗号的配合</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">逗号在引号内</div>
                    <div class="keyword text-lg mb-1">"Hello," she said.</div>
                    <div class="text-sm text-gray-600 mb-1">/həˈloʊ ʃi sɛd/</div>
                    <div class="text-gray-700">"你好，"她说。</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">引语在句中</div>
                    <div class="keyword text-lg mb-1">She said, "I'm fine," and smiled.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi sɛd aɪm faɪn ænd smaɪld/</div>
                    <div class="text-gray-700">她说，"我很好，"然后笑了。</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">多个引语</div>
                    <div class="keyword text-lg mb-1">"Yes," "No," "Maybe."</div>
                    <div class="text-sm text-gray-600 mb-1">/jɛs noʊ ˈmeɪbi/</div>
                    <div class="text-gray-700">"是的，""不，""也许。"</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">引号与句号的配合</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">句号在引号内</div>
                    <div class="keyword text-lg mb-1">He said, "I understand."</div>
                    <div class="text-sm text-gray-600 mb-1">/hi sɛd aɪ ˌʌndərˈstænd/</div>
                    <div class="text-gray-700">他说："我明白。"</div>
                </div>

                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">完整句子</div>
                    <div class="keyword text-lg mb-1">"The meeting is over."</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈmitɪŋ ɪz ˈoʊvər/</div>
                    <div class="text-gray-700">"会议结束了。"</div>
                </div>

                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">引语结尾</div>
                    <div class="keyword text-lg mb-1">She whispered, "Good night."</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi ˈwɪspərd ɡʊd naɪt/</div>
                    <div class="text-gray-700">她轻声说："晚安。"</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">引号与问号的配合</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">问号在引号内</div>
                    <div class="keyword text-lg mb-1">She asked, "Are you coming?"</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi æskt ɑr ju ˈkʌmɪŋ/</div>
                    <div class="text-gray-700">她问："你来吗？"</div>
                </div>

                <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">问号在引号外</div>
                    <div class="keyword text-lg mb-1">Did she say "I'm busy"?</div>
                    <div class="text-sm text-gray-600 mb-1">/dɪd ʃi seɪ aɪm ˈbɪzi/</div>
                    <div class="text-gray-700">她说"我很忙"了吗？</div>
                </div>

                <div class="card bg-lime-50 border border-lime-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">双重疑问</div>
                    <div class="keyword text-lg mb-1">He asked, "Why did you say 'no'?"</div>
                    <div class="text-sm text-gray-600 mb-1">/hi æskt waɪ dɪd ju seɪ noʊ/</div>
                    <div class="text-gray-700">他问："你为什么说'不'？"</div>
                </div>

                <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">反问句</div>
                    <div class="keyword text-lg mb-1">"You're not serious, are you?"</div>
                    <div class="text-sm text-gray-600 mb-1">/jʊr nɑt ˈsɪriəs ɑr ju/</div>
                    <div class="text-gray-700">"你不是认真的，对吧？"</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">引号与感叹号的配合</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-rose-50 border border-rose-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">感叹号在引号内</div>
                    <div class="keyword text-lg mb-1">He shouted, "Stop!"</div>
                    <div class="text-sm text-gray-600 mb-1">/hi ˈʃaʊtɪd stɑp/</div>
                    <div class="text-gray-700">他喊道："停！"</div>
                </div>

                <div class="card bg-amber-50 border border-amber-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">感叹号在引号外</div>
                    <div class="keyword text-lg mb-1">I can't believe he said "maybe"!</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ kænt bɪˈliv hi sɛd ˈmeɪbi/</div>
                    <div class="text-gray-700">我不敢相信他说"也许"！</div>
                </div>

                <div class="card bg-violet-50 border border-violet-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">强烈感叹</div>
                    <div class="keyword text-lg mb-1">"What a surprise!" she exclaimed.</div>
                    <div class="text-sm text-gray-600 mb-1">/wʌt ə sərˈpraɪz ʃi ɪkˈskleɪmd/</div>
                    <div class="text-gray-700">"真是个惊喜！"她感叹道。</div>
                </div>

                <div class="card bg-fuchsia-50 border border-fuchsia-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">惊讶表达</div>
                    <div class="keyword text-lg mb-1">"Oh my goodness!" Mary cried.</div>
                    <div class="text-sm text-gray-600 mb-1">/oʊ maɪ ˈɡʊdnəs ˈmɛri kraɪd/</div>
                    <div class="text-gray-700">"我的天哪！"玛丽叫道。</div>
                </div>
            </div>
        </section>

        <!-- 引号中的引号 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">引号中的引号</h2>
            <p class="text-gray-700 mb-4">当引语中还包含另一个引语时，外层使用双引号，内层使用单引号。这种嵌套引用在日常对话和文学作品中经常出现。</p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-sky-50 border border-sky-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">嵌套引语</div>
                    <div class="keyword text-lg mb-1">He said, "She told me 'I'm busy.'"</div>
                    <div class="text-sm text-gray-600 mb-1">/hi sɛd ʃi toʊld mi aɪm ˈbɪzi/</div>
                    <div class="text-gray-700">他说："她告诉我'我很忙。'"</div>
                </div>

                <div class="card bg-slate-50 border border-slate-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">转述他人话语</div>
                    <div class="keyword text-lg mb-1">Mary said, "Tom asked 'Where are you?'"</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈmɛri sɛd tɑm æskt wɛr ɑr ju/</div>
                    <div class="text-gray-700">玛丽说："汤姆问'你在哪里？'"</div>
                </div>

                <div class="card bg-stone-50 border border-stone-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">复杂对话</div>
                    <div class="keyword text-lg mb-1">"John said 'Call me later,'" she explained.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʤɑn sɛd kɔl mi ˈleɪtər ʃi ɪkˈspleɪnd/</div>
                    <div class="text-gray-700">"约翰说'稍后给我打电话，'"她解释道。</div>
                </div>

                <div class="card bg-neutral-50 border border-neutral-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">引用名言</div>
                    <div class="keyword text-lg mb-1">The teacher said, "Remember 'Practice makes perfect.'"</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈtiʧər sɛd rɪˈmɛmbər ˈpræktəs meɪks ˈpɜrfəkt/</div>
                    <div class="text-gray-700">老师说："记住'熟能生巧。'"</div>
                </div>
            </div>
        </section>

        <!-- 特殊情况下的引号使用 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">特殊情况下的引号使用</h2>
            <p class="text-gray-700 mb-4">除了标记直接引语外，引号还有其他特殊用途，如表示讽刺、强调特定词汇、引用书名或专有名词等。</p>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">表示讽刺或质疑</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-zinc-50 border border-zinc-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">讽刺用法</div>
                    <div class="keyword text-lg mb-1">His "help" made things worse.</div>
                    <div class="text-sm text-gray-600 mb-1">/hɪz hɛlp meɪd θɪŋz wɜrs/</div>
                    <div class="text-gray-700">他的"帮助"让事情变得更糟。</div>
                </div>

                <div class="card bg-gray-50 border border-gray-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">质疑表达</div>
                    <div class="keyword text-lg mb-1">That was a "great" idea.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðæt wʌz ə ɡreɪt aɪˈdiə/</div>
                    <div class="text-gray-700">那真是个"好"主意。</div>
                </div>

                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">反语效果</div>
                    <div class="keyword text-lg mb-1">What a "wonderful" day!</div>
                    <div class="text-sm text-gray-600 mb-1">/wʌt ə ˈwʌndərfəl deɪ/</div>
                    <div class="text-gray-700">多么"美好"的一天！</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">引用专有名词和术语</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">专业术语</div>
                    <div class="keyword text-lg mb-1">The concept of "machine learning"</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈkɑnsɛpt ʌv məˈʃin ˈlɜrnɪŋ/</div>
                    <div class="text-gray-700">"机器学习"的概念</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">外来词汇</div>
                    <div class="keyword text-lg mb-1">The French word "bonjour"</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə frɛnʧ wɜrd bonˈʒʊr/</div>
                    <div class="text-gray-700">法语单词"bonjour"</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">俚语表达</div>
                    <div class="keyword text-lg mb-1">The slang term "cool"</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə slæŋ tɜrm kul/</div>
                    <div class="text-gray-700">俚语词汇"cool"</div>
                </div>
            </div>
        </section>

        <!-- 长篇引语的处理 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">长篇引语的处理</h2>
            <p class="text-gray-700 mb-4">当引语较长或包含多个段落时，需要特殊的处理方式。通常每个段落开头都要加引号，但只有最后一个段落结尾才加结束引号。</p>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">多段落引语开始</div>
                    <div class="keyword text-lg mb-1">"First paragraph here.</div>
                    <div class="text-sm text-gray-600 mb-1">/fɜrst ˈpærəˌɡræf hɪr/</div>
                    <div class="text-gray-700">"第一段在这里。</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">中间段落</div>
                    <div class="keyword text-lg mb-1">"Second paragraph continues.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈsɛkənd ˈpærəˌɡræf kənˈtɪnjuz/</div>
                    <div class="text-gray-700">"第二段继续。</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">引语结束</div>
                    <div class="keyword text-lg mb-1">"Final paragraph ends here."</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈfaɪnəl ˈpærəˌɡræf ɛndz hɪr/</div>
                    <div class="text-gray-700">"最后一段在这里结束。"</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">引语中断</div>
                    <div class="keyword text-lg mb-1">"I think," he said, "you're right."</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ θɪŋk hi sɛd jʊr raɪt/</div>
                    <div class="text-gray-700">"我认为，"他说，"你是对的。"</div>
                </div>
            </div>
        </section>

        <!-- 对话中的引号使用 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">对话中的引号使用</h2>
            <p class="text-gray-700 mb-4">在对话中，每个说话人的话都需要用引号标记，并且通常每个说话人的话要另起一行。这样可以清楚地区分不同说话人的内容。</p>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">简单对话</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">对话开始</div>
                    <div class="keyword text-lg mb-1">"Hello," said John.</div>
                    <div class="text-sm text-gray-600 mb-1">/həˈloʊ sɛd ʤɑn/</div>
                    <div class="text-gray-700">"你好，"约翰说。</div>
                </div>

                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">回应</div>
                    <div class="keyword text-lg mb-1">"Hi there," Mary replied.</div>
                    <div class="text-sm text-gray-600 mb-1">/haɪ ðɛr ˈmɛri rɪˈplaɪd/</div>
                    <div class="text-gray-700">"嗨，"玛丽回答。</div>
                </div>

                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">继续对话</div>
                    <div class="keyword text-lg mb-1">"How are you?" he asked.</div>
                    <div class="text-sm text-gray-600 mb-1">/haʊ ɑr ju hi æskt/</div>
                    <div class="text-gray-700">"你好吗？"他问。</div>
                </div>

                <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">对话结束</div>
                    <div class="keyword text-lg mb-1">"I'm fine, thanks," she answered.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪm faɪn θæŋks ʃi ˈænsərd/</div>
                    <div class="text-gray-700">"我很好，谢谢，"她回答。</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">复杂对话结构</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-lime-50 border border-lime-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">动作描述</div>
                    <div class="keyword text-lg mb-1">"Wait," she said, raising her hand.</div>
                    <div class="text-sm text-gray-600 mb-1">/weɪt ʃi sɛd ˈreɪzɪŋ hər hænd/</div>
                    <div class="text-gray-700">"等等，"她说着举起了手。</div>
                </div>

                <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">情感表达</div>
                    <div class="keyword text-lg mb-1">"I can't believe it!" Tom exclaimed.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ kænt bɪˈliv ɪt tɑm ɪkˈskleɪmd/</div>
                    <div class="text-gray-700">"我不敢相信！"汤姆感叹道。</div>
                </div>

                <div class="card bg-rose-50 border border-rose-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">思考过程</div>
                    <div class="keyword text-lg mb-1">"Let me think," she murmured.</div>
                    <div class="text-sm text-gray-600 mb-1">/lɛt mi θɪŋk ʃi ˈmɜrmərd/</div>
                    <div class="text-gray-700">"让我想想，"她喃喃自语。</div>
                </div>

                <div class="card bg-amber-50 border border-amber-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">语调描述</div>
                    <div class="keyword text-lg mb-1">"Really?" she asked skeptically.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈrɪli ʃi æskt ˈskɛptɪkli/</div>
                    <div class="text-gray-700">"真的吗？"她怀疑地问。</div>
                </div>
            </div>
        </section>

        <!-- 引语动词的使用 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">引语动词的丰富表达</h2>
            <p class="text-gray-700 mb-4">除了基本的"said"之外，英语中有许多其他动词可以更准确地描述说话的方式、语调和情感。选择合适的引语动词能让对话更生动。</p>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">表示音量的动词</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-violet-50 border border-violet-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">大声说话</div>
                    <div class="keyword text-lg mb-1">"Help!" she shouted.</div>
                    <div class="text-sm text-gray-600 mb-1">/hɛlp ʃi ˈʃaʊtɪd/</div>
                    <div class="text-gray-700">"救命！"她大喊。</div>
                </div>

                <div class="card bg-fuchsia-50 border border-fuchsia-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">小声说话</div>
                    <div class="keyword text-lg mb-1">"Secret," he whispered.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈsikrət hi ˈwɪspərd/</div>
                    <div class="text-gray-700">"秘密，"他低声说。</div>
                </div>

                <div class="card bg-sky-50 border border-sky-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">喃喃自语</div>
                    <div class="keyword text-lg mb-1">"Strange," she murmured.</div>
                    <div class="text-sm text-gray-600 mb-1">/streɪnʤ ʃi ˈmɜrmərd/</div>
                    <div class="text-gray-700">"奇怪，"她喃喃道。</div>
                </div>

                <div class="card bg-slate-50 border border-slate-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">尖叫</div>
                    <div class="keyword text-lg mb-1">"No!" she screamed.</div>
                    <div class="text-sm text-gray-600 mb-1">/noʊ ʃi skrimd/</div>
                    <div class="text-gray-700">"不！"她尖叫。</div>
                </div>

                <div class="card bg-stone-50 border border-stone-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">呼喊</div>
                    <div class="keyword text-lg mb-1">"Come here!" he called.</div>
                    <div class="text-sm text-gray-600 mb-1">/kʌm hɪr hi kɔld/</div>
                    <div class="text-gray-700">"过来！"他呼喊。</div>
                </div>

                <div class="card bg-neutral-50 border border-neutral-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">咆哮</div>
                    <div class="keyword text-lg mb-1">"Get out!" he roared.</div>
                    <div class="text-sm text-gray-600 mb-1">/ɡɛt aʊt hi rɔrd/</div>
                    <div class="text-gray-700">"出去！"他咆哮。</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">表示情感的动词</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-zinc-50 border border-zinc-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">抱怨</div>
                    <div class="keyword text-lg mb-1">"It's unfair," she complained.</div>
                    <div class="text-sm text-gray-600 mb-1">/ɪts ʌnˈfɛr ʃi kəmˈpleɪnd/</div>
                    <div class="text-gray-700">"这不公平，"她抱怨。</div>
                </div>

                <div class="card bg-gray-50 border border-gray-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">叹息</div>
                    <div class="keyword text-lg mb-1">"I'm tired," he sighed.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪm ˈtaɪərd hi saɪd/</div>
                    <div class="text-gray-700">"我累了，"他叹息。</div>
                </div>

                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">咯咯笑</div>
                    <div class="keyword text-lg mb-1">"That's funny," she giggled.</div>
                    <div class="text-sm text-gray-600 mb-1">/ðæts ˈfʌni ʃi ˈɡɪɡəld/</div>
                    <div class="text-gray-700">"真有趣，"她咯咯笑。</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">哭泣</div>
                    <div class="keyword text-lg mb-1">"I miss you," she sobbed.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ mɪs ju ʃi sɑbd/</div>
                    <div class="text-gray-700">"我想你，"她哭泣。</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">嘲笑</div>
                    <div class="keyword text-lg mb-1">"How silly," he chuckled.</div>
                    <div class="text-sm text-gray-600 mb-1">/haʊ ˈsɪli hi ˈʧʌkəld/</div>
                    <div class="text-gray-700">"多愚蠢，"他轻笑。</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">惊呼</div>
                    <div class="keyword text-lg mb-1">"Amazing!" she gasped.</div>
                    <div class="text-sm text-gray-600 mb-1">/əˈmeɪzɪŋ ʃi ɡæspt/</div>
                    <div class="text-gray-700">"太棒了！"她惊呼。</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">表示态度的动词</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">坚持</div>
                    <div class="keyword text-lg mb-1">"I'm right," he insisted.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪm raɪt hi ɪnˈsɪstəd/</div>
                    <div class="text-gray-700">"我是对的，"他坚持。</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">承认</div>
                    <div class="keyword text-lg mb-1">"You're right," she admitted.</div>
                    <div class="text-sm text-gray-600 mb-1">/jʊr raɪt ʃi ədˈmɪtəd/</div>
                    <div class="text-gray-700">"你是对的，"她承认。</div>
                </div>

                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">宣布</div>
                    <div class="keyword text-lg mb-1">"Meeting starts now," he announced.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈmitɪŋ stɑrts naʊ hi əˈnaʊnst/</div>
                    <div class="text-gray-700">"会议现在开始，"他宣布。</div>
                </div>
            </div>
        </section>

        <!-- 引号在不同语境中的应用 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">引号在不同语境中的应用</h2>
            <p class="text-gray-700 mb-4">引号的使用会根据不同的语境和文体而有所变化。在学术写作、新闻报道、文学作品和日常对话中，引号的使用都有其特定的规范和习惯。</p>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">学术写作中的引号</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">引用研究</div>
                    <div class="keyword text-lg mb-1">Smith argues that "education is key."</div>
                    <div class="text-sm text-gray-600 mb-1">/smɪθ ˈɑrɡjuz ðæt ˌɛʤəˈkeɪʃən ɪz ki/</div>
                    <div class="text-gray-700">史密斯认为"教育是关键。"</div>
                </div>

                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">定义术语</div>
                    <div class="keyword text-lg mb-1">The term "globalization" refers to...</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə tɜrm ˌɡloʊbələˈzeɪʃən rɪˈfɜrz tu/</div>
                    <div class="text-gray-700">"全球化"这个术语指的是...</div>
                </div>

                <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">引用原文</div>
                    <div class="keyword text-lg mb-1">According to the report, "data shows..."</div>
                    <div class="text-sm text-gray-600 mb-1">/əˈkɔrdɪŋ tu ðə rɪˈpɔrt ˈdeɪtə ʃoʊz/</div>
                    <div class="text-gray-700">根据报告，"数据显示..."</div>
                </div>

                <div class="card bg-lime-50 border border-lime-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">理论概念</div>
                    <div class="keyword text-lg mb-1">The concept of "social capital"</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈkɑnsɛpt ʌv ˈsoʊʃəl ˈkæpətəl/</div>
                    <div class="text-gray-700">"社会资本"的概念</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">新闻报道中的引号</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">官方声明</div>
                    <div class="keyword text-lg mb-1">The mayor said, "We will rebuild."</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈmeɪər sɛd wi wɪl riˈbɪld/</div>
                    <div class="text-gray-700">市长说："我们将重建。"</div>
                </div>

                <div class="card bg-rose-50 border border-rose-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">目击者证词</div>
                    <div class="keyword text-lg mb-1">A witness reported, "I saw everything."</div>
                    <div class="text-sm text-gray-600 mb-1">/ə ˈwɪtnəs rɪˈpɔrtəd aɪ sɔ ˈɛvriθɪŋ/</div>
                    <div class="text-gray-700">一位目击者报告："我看到了一切。"</div>
                </div>

                <div class="card bg-amber-50 border border-amber-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">专家意见</div>
                    <div class="keyword text-lg mb-1">The expert explained, "This is normal."</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈɛkspərt ɪkˈspleɪnd ðɪs ɪz ˈnɔrməl/</div>
                    <div class="text-gray-700">专家解释："这很正常。"</div>
                </div>

                <div class="card bg-violet-50 border border-violet-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">公司回应</div>
                    <div class="keyword text-lg mb-1">The company stated, "We deny all charges."</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈkʌmpəni ˈsteɪtəd wi dɪˈnaɪ ɔl ˈʧɑrʤəz/</div>
                    <div class="text-gray-700">公司声明："我们否认所有指控。"</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">文学作品中的引号</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-fuchsia-50 border border-fuchsia-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">人物对话</div>
                    <div class="keyword text-lg mb-1">"I love you," she whispered softly.</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ lʌv ju ʃi ˈwɪspərd ˈsɔftli/</div>
                    <div class="text-gray-700">"我爱你，"她轻声低语。</div>
                </div>

                <div class="card bg-sky-50 border border-sky-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">内心独白</div>
                    <div class="keyword text-lg mb-1">"What should I do?" he thought.</div>
                    <div class="text-sm text-gray-600 mb-1">/wʌt ʃʊd aɪ du hi θɔt/</div>
                    <div class="text-gray-700">"我该怎么办？"他想。</div>
                </div>

                <div class="card bg-slate-50 border border-slate-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">叙述者引用</div>
                    <div class="keyword text-lg mb-1">As Shakespeare wrote, "To be or not to be."</div>
                    <div class="text-sm text-gray-600 mb-1">/æz ˈʃeɪkspɪr roʊt tu bi ɔr nɑt tu bi/</div>
                    <div class="text-gray-700">正如莎士比亚所写："生存还是毁灭。"</div>
                </div>

                <div class="card bg-stone-50 border border-stone-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">诗歌引用</div>
                    <div class="keyword text-lg mb-1">The poem begins, "Roses are red."</div>
                    <div class="text-sm text-gray-600 mb-1">/ðə ˈpoʊəm bɪˈɡɪnz ˈroʊzəz ɑr rɛd/</div>
                    <div class="text-gray-700">这首诗开头是："玫瑰是红色的。"</div>
                </div>
            </div>
        </section>

        <!-- 引号使用的常见错误 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">引号使用的常见错误</h2>
            <p class="text-gray-700 mb-4">在使用引号时，学习者经常会犯一些典型错误。了解这些错误并学会避免它们，能够显著提高英语写作的准确性和专业性。</p>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">标点符号位置错误</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">错误示例</div>
                    <div class="keyword text-lg mb-1">She said "hello".</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi sɛd həˈloʊ/</div>
                    <div class="text-gray-700">她说"你好"。（错误）</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">正确示例</div>
                    <div class="keyword text-lg mb-1">She said, "Hello."</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi sɛd həˈloʊ/</div>
                    <div class="text-gray-700">她说："你好。"（正确）</div>
                </div>

                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">错误示例</div>
                    <div class="keyword text-lg mb-1">He asked "why"?</div>
                    <div class="text-sm text-gray-600 mb-1">/hi æskt waɪ/</div>
                    <div class="text-gray-700">他问"为什么"？（错误）</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">正确示例</div>
                    <div class="keyword text-lg mb-1">He asked, "Why?"</div>
                    <div class="text-sm text-gray-600 mb-1">/hi æskt waɪ/</div>
                    <div class="text-gray-700">他问："为什么？"（正确）</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">间接引语误用引号</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">错误示例</div>
                    <div class="keyword text-lg mb-1">He said that "he was tired."</div>
                    <div class="text-sm text-gray-600 mb-1">/hi sɛd ðæt hi wʌz ˈtaɪərd/</div>
                    <div class="text-gray-700">他说"他累了。"（错误）</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">正确示例</div>
                    <div class="keyword text-lg mb-1">He said that he was tired.</div>
                    <div class="text-sm text-gray-600 mb-1">/hi sɛd ðæt hi wʌz ˈtaɪərd/</div>
                    <div class="text-gray-700">他说他累了。（正确）</div>
                </div>

                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">错误示例</div>
                    <div class="keyword text-lg mb-1">She told me that "she would come."</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi toʊld mi ðæt ʃi wʊd kʌm/</div>
                    <div class="text-gray-700">她告诉我"她会来。"（错误）</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">正确示例</div>
                    <div class="keyword text-lg mb-1">She told me that she would come.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi toʊld mi ðæt ʃi wʊd kʌm/</div>
                    <div class="text-gray-700">她告诉我她会来。（正确）</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">引号配对错误</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">错误示例</div>
                    <div class="keyword text-lg mb-1">"Hello, she said.</div>
                    <div class="text-sm text-gray-600 mb-1">/həˈloʊ ʃi sɛd/</div>
                    <div class="text-gray-700">"你好，她说。（缺少结束引号）</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">正确示例</div>
                    <div class="keyword text-lg mb-1">"Hello," she said.</div>
                    <div class="text-sm text-gray-600 mb-1">/həˈloʊ ʃi sɛd/</div>
                    <div class="text-gray-700">"你好，"她说。（正确）</div>
                </div>

                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">错误示例</div>
                    <div class="keyword text-lg mb-1">He said 'I'm fine".</div>
                    <div class="text-sm text-gray-600 mb-1">/hi sɛd aɪm faɪn/</div>
                    <div class="text-gray-700">他说'我很好"。（引号不匹配）</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">正确示例</div>
                    <div class="keyword text-lg mb-1">He said, "I'm fine."</div>
                    <div class="text-sm text-gray-600 mb-1">/hi sɛd aɪm faɪn/</div>
                    <div class="text-gray-700">他说："我很好。"（正确）</div>
                </div>
            </div>
        </section>

        <!-- 引号的高级用法 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">引号的高级用法</h2>
            <p class="text-gray-700 mb-4">掌握了基本的引号使用规则后，还需要了解一些高级用法，这些用法在正式写作和复杂语境中经常出现。</p>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">省略号与引号的结合</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-neutral-50 border border-neutral-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">引语开头省略</div>
                    <div class="keyword text-lg mb-1">He said, "...and that's the truth."</div>
                    <div class="text-sm text-gray-600 mb-1">/hi sɛd ænd ðæts ðə truθ/</div>
                    <div class="text-gray-700">他说："...这就是事实。"</div>
                </div>

                <div class="card bg-zinc-50 border border-zinc-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">引语中间省略</div>
                    <div class="keyword text-lg mb-1">"I think...maybe you're right."</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ θɪŋk ˈmeɪbi jʊr raɪt/</div>
                    <div class="text-gray-700">"我想...也许你是对的。"</div>
                </div>

                <div class="card bg-gray-50 border border-gray-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">引语结尾省略</div>
                    <div class="keyword text-lg mb-1">"I was going to say..."</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ wʌz ˈɡoʊɪŋ tu seɪ/</div>
                    <div class="text-gray-700">"我本来想说..."</div>
                </div>

                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">学术引用省略</div>
                    <div class="keyword text-lg mb-1">Smith argues that "education...is vital."</div>
                    <div class="text-sm text-gray-600 mb-1">/smɪθ ˈɑrɡjuz ðæt ˌɛʤəˈkeɪʃən ɪz ˈvaɪtəl/</div>
                    <div class="text-gray-700">史密斯认为"教育...是至关重要的。"</div>
                </div>
            </div>
        </section>

        <!-- 引号在数字媒体中的使用 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">引号在数字媒体中的使用</h2>
            <p class="text-gray-700 mb-4">在数字化时代，引号的使用也扩展到了电子邮件、社交媒体、即时消息等新的交流平台。这些平台有其特殊的引号使用习惯和规范。</p>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">电子邮件中的引号</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">引用回复</div>
                    <div class="keyword text-lg mb-1">You wrote: "Meeting at 3 PM."</div>
                    <div class="text-sm text-gray-600 mb-1">/ju roʊt ˈmitɪŋ æt θri pi ɛm/</div>
                    <div class="text-gray-700">你写道："下午3点开会。"</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">强调重点</div>
                    <div class="keyword text-lg mb-1">Please note the "urgent" status.</div>
                    <div class="text-sm text-gray-600 mb-1">/pliz noʊt ðə ˈɜrʤənt ˈsteɪtəs/</div>
                    <div class="text-gray-700">请注意"紧急"状态。</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">标题引用</div>
                    <div class="keyword text-lg mb-1">Re: "Project Update" email</div>
                    <div class="text-sm text-gray-600 mb-1">/ri ˈprɑʤɛkt ʌpˈdeɪt ˈimeɪl/</div>
                    <div class="text-gray-700">回复："项目更新"邮件</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">附件说明</div>
                    <div class="keyword text-lg mb-1">See attached "Report.pdf" file.</div>
                    <div class="text-sm text-gray-600 mb-1">/si əˈtæʧt rɪˈpɔrt faɪl/</div>
                    <div class="text-gray-700">请查看附件"Report.pdf"文件。</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">社交媒体中的引号</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">转发引用</div>
                    <div class="keyword text-lg mb-1">RT: "Great news everyone!"</div>
                    <div class="text-sm text-gray-600 mb-1">/ɑr ti ɡreɪt nuz ˈɛvriˌwʌn/</div>
                    <div class="text-gray-700">转发："大家好消息！"</div>
                </div>

                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">标签引用</div>
                    <div class="keyword text-lg mb-1">Love this "motivation Monday" post!</div>
                    <div class="text-sm text-gray-600 mb-1">/lʌv ðɪs ˌmoʊtəˈveɪʃən ˈmʌndeɪ poʊst/</div>
                    <div class="text-gray-700">喜欢这个"动力星期一"帖子！</div>
                </div>

                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">讽刺表达</div>
                    <div class="keyword text-lg mb-1">Another "perfect" day at work.</div>
                    <div class="text-sm text-gray-600 mb-1">/əˈnʌðər ˈpɜrfəkt deɪ æt wɜrk/</div>
                    <div class="text-gray-700">又是"完美"的工作日。</div>
                </div>

                <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">歌词引用</div>
                    <div class="keyword text-lg mb-1">Singing "Happy Birthday" to myself.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈsɪŋɪŋ ˈhæpi ˈbɜrθdeɪ tu maɪˈsɛlf/</div>
                    <div class="text-gray-700">给自己唱"生日快乐"。</div>
                </div>

                <div class="card bg-lime-50 border border-lime-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">电影引用</div>
                    <div class="keyword text-lg mb-1">Just watched "The Matrix" again.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʤʌst wɑʧt ðə ˈmeɪtrɪks əˈɡɛn/</div>
                    <div class="text-gray-700">刚又看了一遍"黑客帝国"。</div>
                </div>

                <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">品牌提及</div>
                    <div class="keyword text-lg mb-1">Love my new "iPhone" camera!</div>
                    <div class="text-sm text-gray-600 mb-1">/lʌv maɪ nu ˈaɪfoʊn ˈkæmərə/</div>
                    <div class="text-gray-700">喜欢我的新"iPhone"相机！</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">即时消息中的引号</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-rose-50 border border-rose-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">回复消息</div>
                    <div class="keyword text-lg mb-1">You said "see you later"</div>
                    <div class="text-sm text-gray-600 mb-1">/ju sɛd si ju ˈleɪtər/</div>
                    <div class="text-gray-700">你说"待会见"</div>
                </div>

                <div class="card bg-amber-50 border border-amber-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">澄清意思</div>
                    <div class="keyword text-lg mb-1">By "soon" I mean tomorrow.</div>
                    <div class="text-sm text-gray-600 mb-1">/baɪ sun aɪ min təˈmɑroʊ/</div>
                    <div class="text-gray-700">"很快"我是指明天。</div>
                </div>

                <div class="card bg-violet-50 border border-violet-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">强调词汇</div>
                    <div class="keyword text-lg mb-1">That was "amazing"!</div>
                    <div class="text-sm text-gray-600 mb-1">/ðæt wʌz əˈmeɪzɪŋ/</div>
                    <div class="text-gray-700">那真是"太棒了"！</div>
                </div>

                <div class="card bg-fuchsia-50 border border-fuchsia-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">引用链接</div>
                    <div class="keyword text-lg mb-1">Check out "www.example.com"</div>
                    <div class="text-sm text-gray-600 mb-1">/ʧɛk aʊt dʌbəlju dʌbəlju dʌbəlju ɪɡˈzæmpəl kɑm/</div>
                    <div class="text-gray-700">查看"www.example.com"</div>
                </div>
            </div>
        </section>

        <!-- 引号使用的文化差异 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">引号使用的地域差异</h2>
            <p class="text-gray-700 mb-4">虽然我们主要学习标准英语的引号使用，但了解不同地区在引号使用上的细微差异也很重要，这有助于更好地理解各种英语文本。</p>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">标点符号位置的地域差异</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-sky-50 border border-sky-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">标准用法</div>
                    <div class="keyword text-lg mb-1">She said, "Hello."</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi sɛd həˈloʊ/</div>
                    <div class="text-gray-700">她说："你好。"</div>
                </div>

                <div class="card bg-slate-50 border border-slate-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">逻辑标点</div>
                    <div class="keyword text-lg mb-1">She said "hello".</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi sɛd həˈloʊ/</div>
                    <div class="text-gray-700">她说"你好"。</div>
                </div>

                <div class="card bg-stone-50 border border-stone-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">问号位置</div>
                    <div class="keyword text-lg mb-1">Did she say "yes"?</div>
                    <div class="text-sm text-gray-600 mb-1">/dɪd ʃi seɪ jɛs/</div>
                    <div class="text-gray-700">她说"是"了吗？</div>
                </div>

                <div class="card bg-neutral-50 border border-neutral-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">感叹号位置</div>
                    <div class="keyword text-lg mb-1">I love "chocolate"!</div>
                    <div class="text-sm text-gray-600 mb-1">/aɪ lʌv ˈʧɔklət/</div>
                    <div class="text-gray-700">我爱"巧克力"！</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">引号样式的差异</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-zinc-50 border border-zinc-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">直引号</div>
                    <div class="keyword text-lg mb-1">"Hello world"</div>
                    <div class="text-sm text-gray-600 mb-1">/həˈloʊ wɜrld/</div>
                    <div class="text-gray-700">"你好世界"</div>
                </div>

                <div class="card bg-gray-50 border border-gray-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">弯引号</div>
                    <div class="keyword text-lg mb-1">"Hello world"</div>
                    <div class="text-sm text-gray-600 mb-1">/həˈloʊ wɜrld/</div>
                    <div class="text-gray-700">"你好世界"</div>
                </div>

                <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">单引号</div>
                    <div class="keyword text-lg mb-1">'Hello world'</div>
                    <div class="text-sm text-gray-600 mb-1">/həˈloʊ wɜrld/</div>
                    <div class="text-gray-700">'你好世界'</div>
                </div>

                <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">法式引号</div>
                    <div class="keyword text-lg mb-1">« Hello world »</div>
                    <div class="text-sm text-gray-600 mb-1">/həˈloʊ wɜrld/</div>
                    <div class="text-gray-700">« 你好世界 »</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">德式引号</div>
                    <div class="keyword text-lg mb-1">„Hello world"</div>
                    <div class="text-sm text-gray-600 mb-1">/həˈloʊ wɜrld/</div>
                    <div class="text-gray-700">„你好世界"</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">日式引号</div>
                    <div class="keyword text-lg mb-1">「Hello world」</div>
                    <div class="text-sm text-gray-600 mb-1">/həˈloʊ wɜrld/</div>
                    <div class="text-gray-700">「你好世界」</div>
                </div>
            </div>
        </section>

        <!-- 引号使用的实践技巧 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">引号使用的实践技巧</h2>
            <p class="text-gray-700 mb-4">掌握引号的理论知识后，还需要通过实践来提高使用的准确性和流畅性。以下是一些实用的练习方法和记忆技巧。</p>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">记忆口诀和技巧</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">直接引语口诀</div>
                    <div class="keyword text-lg mb-1">Direct quote, use quotes right</div>
                    <div class="text-sm text-gray-600 mb-1">/dəˈrɛkt kwoʊt juz kwoʊts raɪt/</div>
                    <div class="text-gray-700">直接引用，引号要用对</div>
                </div>

                <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">间接引语口诀</div>
                    <div class="keyword text-lg mb-1">Indirect speech, no quotes to reach</div>
                    <div class="text-sm text-gray-600 mb-1">/ˌɪndəˈrɛkt spiʧ noʊ kwoʊts tu riʧ/</div>
                    <div class="text-gray-700">间接引语，引号不要加</div>
                </div>

                <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">标点位置口诀</div>
                    <div class="keyword text-lg mb-1">Comma and period, inside they stay</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈkɑmə ænd ˈpɪriəd ɪnˈsaɪd ðeɪ steɪ/</div>
                    <div class="text-gray-700">逗号句号，引号内停留</div>
                </div>

                <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">问号感叹号口诀</div>
                    <div class="keyword text-lg mb-1">Question and exclamation, check the relation</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈkwɛsʧən ænd ˌɛkskləˈmeɪʃən ʧɛk ðə rɪˈleɪʃən/</div>
                    <div class="text-gray-700">问号感叹号，看关系定位置</div>
                </div>
            </div>

            <h3 class="text-xl font-semibold mb-3 text-gray-800">常见搭配和表达</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">引语动词搭配</div>
                    <div class="keyword text-lg mb-1">according to + "quote"</div>
                    <div class="text-sm text-gray-600 mb-1">/əˈkɔrdɪŋ tu kwoʊt/</div>
                    <div class="text-gray-700">根据 + "引语"</div>
                </div>

                <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">引用介绍</div>
                    <div class="keyword text-lg mb-1">as he put it, "..."</div>
                    <div class="text-sm text-gray-600 mb-1">/æz hi pʊt ɪt/</div>
                    <div class="text-gray-700">正如他所说，"..."</div>
                </div>

                <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">强调引用</div>
                    <div class="keyword text-lg mb-1">in his words, "..."</div>
                    <div class="text-sm text-gray-600 mb-1">/ɪn hɪz wɜrdz/</div>
                    <div class="text-gray-700">用他的话说，"..."</div>
                </div>

                <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">转述引用</div>
                    <div class="keyword text-lg mb-1">to quote him directly, "..."</div>
                    <div class="text-sm text-gray-600 mb-1">/tu kwoʊt hɪm dəˈrɛktli/</div>
                    <div class="text-gray-700">直接引用他的话，"..."</div>
                </div>

                <div class="card bg-lime-50 border border-lime-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">结束引用</div>
                    <div class="keyword text-lg mb-1">"..." he concluded.</div>
                    <div class="text-sm text-gray-600 mb-1">/hi kənˈkludəd/</div>
                    <div class="text-gray-700">"..."他总结道。</div>
                </div>

                <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">补充说明</div>
                    <div class="keyword text-lg mb-1">"..." she added.</div>
                    <div class="text-sm text-gray-600 mb-1">/ʃi ˈædəd/</div>
                    <div class="text-gray-700">"..."她补充说。</div>
                </div>
            </div>
        </section>

        <!-- 总结性内容 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">引号使用的核心要点</h2>
            <p class="text-gray-700 mb-4">通过系统学习引号的各种用法，我们可以总结出一些核心要点，这些要点将帮助你在实际使用中更加准确和自信。</p>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-6">
                <div class="card bg-rose-50 border border-rose-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">基本原则</div>
                    <div class="keyword text-lg mb-1">Direct speech needs quotes</div>
                    <div class="text-sm text-gray-600 mb-1">/dəˈrɛkt spiʧ nidz kwoʊts/</div>
                    <div class="text-gray-700">直接引语需要引号</div>
                </div>

                <div class="card bg-amber-50 border border-amber-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">转换规则</div>
                    <div class="keyword text-lg mb-1">Indirect speech changes tense</div>
                    <div class="text-sm text-gray-600 mb-1">/ˌɪndəˈrɛkt spiʧ ˈʧeɪnʤəz tɛns/</div>
                    <div class="text-gray-700">间接引语改变时态</div>
                </div>

                <div class="card bg-violet-50 border border-violet-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">标点配合</div>
                    <div class="keyword text-lg mb-1">Punctuation follows rules</div>
                    <div class="text-sm text-gray-600 mb-1">/ˌpʌŋkʧuˈeɪʃən ˈfɑloʊz rulz/</div>
                    <div class="text-gray-700">标点符号遵循规则</div>
                </div>

                <div class="card bg-fuchsia-50 border border-fuchsia-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">语境适应</div>
                    <div class="keyword text-lg mb-1">Context determines usage</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈkɑntɛkst dɪˈtɜrmənz ˈjusəʤ/</div>
                    <div class="text-gray-700">语境决定用法</div>
                </div>

                <div class="card bg-sky-50 border border-sky-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">准确表达</div>
                    <div class="keyword text-lg mb-1">Precision in communication</div>
                    <div class="text-sm text-gray-600 mb-1">/prɪˈsɪʒən ɪn kəˌmjunəˈkeɪʃən/</div>
                    <div class="text-gray-700">交流中的精确性</div>
                </div>

                <div class="card bg-slate-50 border border-slate-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">持续练习</div>
                    <div class="keyword text-lg mb-1">Practice makes perfect</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈpræktəs meɪks ˈpɜrfəkt/</div>
                    <div class="text-gray-700">熟能生巧</div>
                </div>
            </div>
        </section>
    </div>
</body>
</html>
