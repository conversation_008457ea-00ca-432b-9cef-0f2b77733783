<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>括号和方括号</title>
    <script src="/static/tailwindcss-3.4.17.js"></script>
    <style>
        .keyword {
            color: #3d74ed;
            font-weight: bold;
        }
    </style>
</head>
<body class="bg-white">
    <div class="p-6">
        <!-- 圆括号基础用法 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4">圆括号 (Parentheses) 的基本用法</h2>
            
            <div class="mb-6">
                <h3 class="text-xl font-semibold mb-3">插入语和补充说明</h3>
                <p class="mb-4">圆括号最常用于在句子中插入额外的信息、解释或补充说明，这些信息通常不是句子的核心内容，但能提供有用的背景或细节。</p>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">基础插入语</div>
                        <div class="keyword text-lg mb-1">The movie (which I saw yesterday) was excellent.</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə ˈmuːvi (wɪtʃ aɪ sɔː ˈjestərdeɪ) wʌz ˈeksələnt/</div>
                        <div class="text-gray-700">这部电影（我昨天看的）很棒。</div>
                    </div>
                    
                    <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">年份补充</div>
                        <div class="keyword text-lg mb-1">Shakespeare (1564-1616) wrote many famous plays.</div>
                        <div class="text-sm text-gray-600 mb-1">/ˈʃeɪkspɪr (ˈfɪftiːn ˈsɪksti fɔːr tu ˈsɪkstiːn ˈsɪkstiːn) roʊt ˈmeni ˈfeɪməs pleɪz/</div>
                        <div class="text-gray-700">莎士比亚（1564-1616）写了许多著名戏剧。</div>
                    </div>
                    
                    <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">地点说明</div>
                        <div class="keyword text-lg mb-1">My friend John (from Boston) is visiting next week.</div>
                        <div class="text-sm text-gray-600 mb-1">/maɪ frend dʒɑːn (frʌm ˈbɔːstən) ɪz ˈvɪzɪtɪŋ nekst wiːk/</div>
                        <div class="text-gray-700">我的朋友约翰（来自波士顿）下周要来访问。</div>
                    </div>
                    
                    <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">职业介绍</div>
                        <div class="keyword text-lg mb-1">Dr. Smith (a cardiologist) will see you at 3 PM.</div>
                        <div class="text-sm text-gray-600 mb-1">/ˈdɑːktər smɪθ (ə ˌkɑːrdiˈɑːlədʒɪst) wɪl siː juː æt θriː piː em/</div>
                        <div class="text-gray-700">史密斯医生（心脏病专家）将在下午3点见你。</div>
                    </div>
                </div>
            </div>
            
            <div class="mb-6">
                <h3 class="text-xl font-semibold mb-3">缩写和全称对照</h3>
                <p class="mb-4">当使用缩写时，可以用圆括号提供完整形式，或者相反。</p>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">缩写说明</div>
                        <div class="keyword text-lg mb-1">The FBI (Federal Bureau of Investigation) is investigating.</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə ˌef biː ˈaɪ (ˈfedərəl ˈbjʊroʊ ʌv ɪnˌvestɪˈɡeɪʃən) ɪz ɪnˈvestɪɡeɪtɪŋ/</div>
                        <div class="text-gray-700">联邦调查局（Federal Bureau of Investigation）正在调查。</div>
                    </div>
                    
                    <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">全称后缩写</div>
                        <div class="keyword text-lg mb-1">World Health Organization (WHO) issued a statement.</div>
                        <div class="text-sm text-gray-600 mb-1">/wɜːrld helθ ˌɔːrɡənəˈzeɪʃən (ˈdʌbəljuː eɪtʃ oʊ) ˈɪʃuːd ə ˈsteɪtmənt/</div>
                        <div class="text-gray-700">世界卫生组织（WHO）发布了一份声明。</div>
                    </div>
                    
                    <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">技术缩写</div>
                        <div class="keyword text-lg mb-1">CPU (Central Processing Unit) controls the computer.</div>
                        <div class="text-sm text-gray-600 mb-1">/ˌsiː piː ˈjuː (ˈsentrəl ˈprɑːsesɪŋ ˈjuːnɪt) kənˈtroʊlz ðə kəmˈpjuːtər/</div>
                        <div class="text-gray-700">CPU（中央处理器）控制计算机。</div>
                    </div>
                    
                    <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">学术缩写</div>
                        <div class="keyword text-lg mb-1">She has a PhD (Doctor of Philosophy) in linguistics.</div>
                        <div class="text-sm text-gray-600 mb-1">/ʃiː hæz ə ˌpiː eɪtʃ ˈdiː (ˈdɑːktər ʌv fəˈlɑːsəfi) ɪn lɪŋˈɡwɪstɪks/</div>
                        <div class="text-gray-700">她拥有语言学博士学位（Doctor of Philosophy）。</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 数字和列表中的括号 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4">数字和列表中的圆括号</h2>
            
            <div class="mb-6">
                <h3 class="text-xl font-semibold mb-3">编号列表</h3>
                <p class="mb-4">在正式文档中，圆括号常用于编号列表项。</p>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">步骤说明</div>
                        <div class="keyword text-lg mb-1">(1) Open the file, (2) edit the content, (3) save changes.</div>
                        <div class="text-sm text-gray-600 mb-1">/(wʌn) ˈoʊpən ðə faɪl, (tuː) ˈedɪt ðə ˈkɑːntent, (θriː) seɪv ˈtʃeɪndʒəz/</div>
                        <div class="text-gray-700">（1）打开文件，（2）编辑内容，（3）保存更改。</div>
                    </div>
                    
                    <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">选项列表</div>
                        <div class="keyword text-lg mb-1">Choose: (a) red, (b) blue, or (c) green.</div>
                        <div class="text-sm text-gray-600 mb-1">/tʃuːz: (eɪ) red, (biː) bluː, ɔːr (siː) ɡriːn/</div>
                        <div class="text-gray-700">选择：（a）红色，（b）蓝色，或（c）绿色。</div>
                    </div>
                </div>
            </div>
            
            <div class="mb-6">
                <h3 class="text-xl font-semibold mb-3">数学和科学表达</h3>
                <p class="mb-4">在数学公式和科学表达中，圆括号用于分组和优先级。</p>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div class="card bg-lime-50 border border-lime-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">数学公式</div>
                        <div class="keyword text-lg mb-1">The result is (x + y) × (a - b).</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə rɪˈzʌlt ɪz (eks plʌs waɪ) taɪmz (eɪ ˈmaɪnəs biː)/</div>
                        <div class="text-gray-700">结果是（x + y）×（a - b）。</div>
                    </div>
                    
                    <div class="card bg-amber-50 border border-amber-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">温度表示</div>
                        <div class="keyword text-lg mb-1">Water boils at 100°C (212°F).</div>
                        <div class="text-sm text-gray-600 mb-1">/ˈwɔːtər bɔɪlz æt wʌn ˈhʌndrəd dɪˈɡriːz ˈselsɪəs (tuː ˈhʌndrəd ˈtwelv dɪˈɡriːz ˈfærənhaɪt)/</div>
                        <div class="text-gray-700">水在100°C（212°F）时沸腾。</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 方括号的用法 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4">方括号 [Square Brackets] 的用法</h2>
            
            <div class="mb-6">
                <h3 class="text-xl font-semibold mb-3">引用中的修改和澄清</h3>
                <p class="mb-4">方括号主要用于在引用他人话语时添加澄清、修正或补充信息。</p>
                
                <div class="grid grid-cols-1 gap-4 mb-4">
                    <div class="card bg-violet-50 border border-violet-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">澄清代词</div>
                        <div class="keyword text-lg mb-1">He said, "I think [the president] should resign."</div>
                        <div class="text-sm text-gray-600 mb-1">/hiː sed, "aɪ θɪŋk [ðə ˈprezɪdənt] ʃʊd rɪˈzaɪn"/</div>
                        <div class="text-gray-700">他说："我认为[总统]应该辞职。"</div>
                    </div>
                    
                    <div class="card bg-rose-50 border border-rose-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">语法修正</div>
                        <div class="keyword text-lg mb-1">The witness stated, "I seen [saw] him leave."</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə ˈwɪtnəs ˈsteɪtəd, "aɪ siːn [sɔː] hɪm liːv"/</div>
                        <div class="text-gray-700">证人陈述："我看见[saw]他离开。"</div>
                    </div>
                    
                    <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">添加背景信息</div>
                        <div class="keyword text-lg mb-1">"The company [Apple Inc.] announced new products."</div>
                        <div class="text-sm text-gray-600 mb-1">/"ðə ˈkʌmpəni [ˈæpəl ɪŋk] əˈnaʊnst nuː ˈprɑːdʌkts"/</div>
                        <div class="text-gray-700">"该公司[苹果公司]宣布了新产品。"</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 方括号在学术写作中的应用 -->
        <section class="mb-8">
            <h3 class="text-xl font-semibold mb-3">学术引用中的方括号</h3>
            <p class="mb-4">在学术写作中，方括号用于标注引用来源、添加译者注释或说明省略内容。</p>

            <div class="grid grid-cols-1 gap-4 mb-4">
                <div class="card bg-sky-50 border border-sky-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">引用标注</div>
                    <div class="keyword text-lg mb-1">Recent studies [Smith, 2023] show significant improvement.</div>
                    <div class="text-sm text-gray-600 mb-1">/ˈriːsənt ˈstʌdiz [smɪθ, ˈtuː ˈθaʊzənd ˈtwenti θriː] ʃoʊ sɪɡˈnɪfɪkənt ɪmˈpruːvmənt/</div>
                    <div class="text-gray-700">最近的研究[Smith, 2023]显示了显著改善。</div>
                </div>

                <div class="card bg-stone-50 border border-stone-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">省略标记</div>
                    <div class="keyword text-lg mb-1">"The results [...] were conclusive," the report stated.</div>
                    <div class="text-sm text-gray-600 mb-1">/"ðə rɪˈzʌlts [...] wɜːr kənˈkluːsɪv," ðə rɪˈpɔːrt ˈsteɪtəd/</div>
                    <div class="text-gray-700">"结果[...]是确定的，"报告指出。</div>
                </div>

                <div class="card bg-neutral-50 border border-neutral-200 rounded-lg p-4 shadow-sm">
                    <div class="text-sm text-gray-500 mb-1">译者注释</div>
                    <div class="keyword text-lg mb-1">"Il fait beau [It's beautiful weather] aujourd'hui."</div>
                    <div class="text-sm text-gray-600 mb-1">/"ɪl feɪ boʊ [ɪts ˈbjuːtəfəl ˈweðər] oʊˈʒʊrˈdwiː"/</div>
                    <div class="text-gray-700">"Il fait beau [天气很好] aujourd'hui。"</div>
                </div>
            </div>
        </section>

        <!-- 嵌套括号的使用 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4">嵌套括号的正确使用</h2>

            <div class="mb-6">
                <h3 class="text-xl font-semibold mb-3">圆括号内的方括号</h3>
                <p class="mb-4">当需要在圆括号内再次使用括号时，应使用方括号。</p>

                <div class="grid grid-cols-1 gap-4 mb-4">
                    <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">嵌套说明</div>
                        <div class="keyword text-lg mb-1">The study (conducted in 2023 [see Appendix A]) was comprehensive.</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə ˈstʌdi (kənˈdʌktəd ɪn ˈtuː ˈθaʊzənd ˈtwenti θriː [siː əˈpendɪks eɪ]) wʌz ˌkɑːmprɪˈhensɪv/</div>
                        <div class="text-gray-700">这项研究（在2023年进行[见附录A]）是全面的。</div>
                    </div>

                    <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">复杂引用</div>
                        <div class="keyword text-lg mb-1">The author (Johnson [1995, p. 45]) argues this point.</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə ˈɔːθər (ˈdʒɑːnsən [ˈnaɪnˈtiːn ˈnaɪnti faɪv, piː ˈfɔːrti faɪv]) ˈɑːrɡjuːz ðɪs pɔɪnt/</div>
                        <div class="text-gray-700">作者（Johnson [1995, p. 45]）论证了这一点。</div>
                    </div>
                </div>
            </div>

            <div class="mb-6">
                <h3 class="text-xl font-semibold mb-3">多层嵌套的避免</h3>
                <p class="mb-4">通常应避免过度复杂的嵌套，保持句子清晰易读。</p>

                <div class="grid grid-cols-1 gap-4 mb-4">
                    <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">避免复杂嵌套</div>
                        <div class="keyword text-lg mb-1">The report was detailed. (See the appendix for methodology.)</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə rɪˈpɔːrt wʌz ˈdiːteɪld. (siː ðə əˈpendɪks fɔːr ˌmeθəˈdɑːlədʒi)/</div>
                        <div class="text-gray-700">报告很详细。（方法论见附录。）</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 特殊情况下的括号使用 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4">特殊情况下的括号使用</h2>

            <div class="mb-6">
                <h3 class="text-xl font-semibold mb-3">电话号码和地址</h3>
                <p class="mb-4">在联系信息中，圆括号常用于区号和补充地址信息。</p>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">电话区号</div>
                        <div class="keyword text-lg mb-1">Call me at (555) 123-4567.</div>
                        <div class="text-sm text-gray-600 mb-1">/kɔːl miː æt (faɪv faɪv faɪv) wʌn tuː θriː fɔːr faɪv sɪks ˈsevən/</div>
                        <div class="text-gray-700">请拨打(555) 123-4567联系我。</div>
                    </div>

                    <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">地址补充</div>
                        <div class="keyword text-lg mb-1">123 Main St. (Apt. 4B), New York</div>
                        <div class="text-sm text-gray-600 mb-1">/wʌn tuː θriː meɪn striːt (əˈpɑːrtmənt fɔːr biː), nuː jɔːrk/</div>
                        <div class="text-gray-700">纽约市主街123号（4B公寓）</div>
                    </div>

                    <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">邮政编码</div>
                        <div class="keyword text-lg mb-1">Send it to Boston, MA (02101).</div>
                        <div class="text-sm text-gray-600 mb-1">/send ɪt tuː ˈbɔːstən, ˌem eɪ (oʊ tuː wʌn oʊ wʌn)/</div>
                        <div class="text-gray-700">寄到波士顿，MA (02101)。</div>
                    </div>

                    <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">国际区号</div>
                        <div class="keyword text-lg mb-1">International: (+1) 555-0123</div>
                        <div class="text-sm text-gray-600 mb-1">/ˌɪntərˈnæʃənəl: (plʌs wʌn) faɪv faɪv faɪv oʊ wʌn tuː θriː/</div>
                        <div class="text-gray-700">国际号码：(+1) 555-0123</div>
                    </div>
                </div>
            </div>

            <div class="mb-6">
                <h3 class="text-xl font-semibold mb-3">时间和日期表示</h3>
                <p class="mb-4">括号在时间和日期表示中提供额外的时区或格式信息。</p>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">时区标注</div>
                        <div class="keyword text-lg mb-1">The meeting is at 3:00 PM (EST).</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə ˈmiːtɪŋ ɪz æt θriː oʊ ˈklɑːk piː em (iː es tiː)/</div>
                        <div class="text-gray-700">会议在下午3:00（东部标准时间）。</div>
                    </div>

                    <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">日期格式</div>
                        <div class="keyword text-lg mb-1">The deadline is March 15 (03/15/2024).</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə ˈdedlaɪn ɪz mɑːrtʃ ˈfɪfˈtiːn (oʊ θriː ˈfɪfˈtiːn ˈtuː ˈθaʊzənd ˈtwenti fɔːr)/</div>
                        <div class="text-gray-700">截止日期是3月15日（03/15/2024）。</div>
                    </div>

                    <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">星期补充</div>
                        <div class="keyword text-lg mb-1">See you on the 20th (Friday).</div>
                        <div class="text-sm text-gray-600 mb-1">/siː juː ɑːn ðə ˈtwentiəθ (ˈfraɪdeɪ)/</div>
                        <div class="text-gray-700">20号（星期五）见。</div>
                    </div>

                    <div class="card bg-lime-50 border border-lime-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">持续时间</div>
                        <div class="keyword text-lg mb-1">The event lasts 2 hours (120 minutes).</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə ɪˈvent læsts tuː ˈaʊərz (wʌn ˈhʌndrəd ˈtwenti ˈmɪnəts)/</div>
                        <div class="text-gray-700">活动持续2小时（120分钟）。</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 商业和法律文件中的括号 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4">商业和法律文件中的括号使用</h2>

            <div class="mb-6">
                <h3 class="text-xl font-semibold mb-3">合同条款</h3>
                <p class="mb-4">在法律和商业文件中，括号用于定义术语、提供替代表述或添加法律条件。</p>

                <div class="grid grid-cols-1 gap-4 mb-4">
                    <div class="card bg-amber-50 border border-amber-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">术语定义</div>
                        <div class="keyword text-lg mb-1">The Company (ABC Corporation) agrees to the terms.</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə ˈkʌmpəni (eɪ biː siː ˌkɔːrpəˈreɪʃən) əˈɡriːz tuː ðə tɜːrmz/</div>
                        <div class="text-gray-700">公司（ABC Corporation）同意这些条款。</div>
                    </div>

                    <div class="card bg-violet-50 border border-violet-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">法律条件</div>
                        <div class="keyword text-lg mb-1">Payment is due within thirty (30) days.</div>
                        <div class="text-sm text-gray-600 mb-1">/ˈpeɪmənt ɪz duː wɪˈðɪn ˈθɜːrti (ˈθɜːrti) deɪz/</div>
                        <div class="text-gray-700">付款应在三十（30）天内到期。</div>
                    </div>

                    <div class="card bg-rose-50 border border-rose-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">替代表述</div>
                        <div class="keyword text-lg mb-1">The Buyer (Purchaser) shall inspect the goods.</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə ˈbaɪər (ˈpɜːrtʃəsər) ʃæl ɪnˈspekt ðə ɡʊdz/</div>
                        <div class="text-gray-700">买方（购买者）应检查货物。</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 技术文档中的括号使用 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4">技术文档中的括号应用</h2>

            <div class="mb-6">
                <h3 class="text-xl font-semibold mb-3">编程和代码注释</h3>
                <p class="mb-4">在技术文档中，括号用于解释技术术语、提供版本信息或标注代码示例。</p>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">版本标注</div>
                        <div class="keyword text-lg mb-1">Use Python (version 3.8 or higher) for this script.</div>
                        <div class="text-sm text-gray-600 mb-1">/juːz ˈpaɪθɑːn (ˈvɜːrʒən θriː pɔɪnt eɪt ɔːr ˈhaɪər) fɔːr ðɪs skrɪpt/</div>
                        <div class="text-gray-700">使用Python（3.8版本或更高）运行此脚本。</div>
                    </div>

                    <div class="card bg-sky-50 border border-sky-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">参数说明</div>
                        <div class="keyword text-lg mb-1">The function accepts two parameters (x and y).</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə ˈfʌŋkʃən ækˈsepts tuː pəˈræmətərz (eks ænd waɪ)/</div>
                        <div class="text-gray-700">该函数接受两个参数（x和y）。</div>
                    </div>

                    <div class="card bg-stone-50 border border-stone-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">文件格式</div>
                        <div class="keyword text-lg mb-1">Save the file as JSON (.json extension).</div>
                        <div class="text-sm text-gray-600 mb-1">/seɪv ðə faɪl æz ˈdʒeɪsən (pɔɪnt ˈdʒeɪsən ɪkˈstenʃən)/</div>
                        <div class="text-gray-700">将文件保存为JSON（.json扩展名）。</div>
                    </div>

                    <div class="card bg-neutral-50 border border-neutral-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">默认值</div>
                        <div class="keyword text-lg mb-1">The timeout is set to 30 seconds (default).</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə ˈtaɪmaʊt ɪz set tuː ˈθɜːrti ˈsekəndz (dɪˈfɔːlt)/</div>
                        <div class="text-gray-700">超时设置为30秒（默认值）。</div>
                    </div>
                </div>
            </div>

            <div class="mb-6">
                <h3 class="text-xl font-semibold mb-3">API文档和规范</h3>
                <p class="mb-4">在API文档中，括号用于标注数据类型、可选参数或示例值。</p>

                <div class="grid grid-cols-1 gap-4 mb-4">
                    <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">数据类型</div>
                        <div class="keyword text-lg mb-1">The user_id field (integer) is required.</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə ˈjuːzər aɪ diː fiːld (ˈɪntədʒər) ɪz rɪˈkwaɪərd/</div>
                        <div class="text-gray-700">user_id字段（整数）是必需的。</div>
                    </div>

                    <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">可选参数</div>
                        <div class="keyword text-lg mb-1">Include the limit parameter (optional, default: 10).</div>
                        <div class="text-sm text-gray-600 mb-1">/ɪnˈkluːd ðə ˈlɪmət pəˈræmətər (ˈɑːpʃənəl, dɪˈfɔːlt: ten)/</div>
                        <div class="text-gray-700">包含limit参数（可选，默认值：10）。</div>
                    </div>

                    <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">示例值</div>
                        <div class="keyword text-lg mb-1">Enter your email (example: <EMAIL>).</div>
                        <div class="text-sm text-gray-600 mb-1">/ˈentər jʊr ˈiːmeɪl (ɪɡˈzæmpəl: ˈjuːzər æt doʊˈmeɪn dɑːt kɑːm)/</div>
                        <div class="text-gray-700">输入您的邮箱（示例：<EMAIL>）。</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 学术写作中的高级括号用法 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4">学术写作中的高级括号用法</h2>

            <div class="mb-6">
                <h3 class="text-xl font-semibold mb-3">统计数据和研究结果</h3>
                <p class="mb-4">在学术论文中，括号用于提供统计数据、样本大小或置信区间。</p>

                <div class="grid grid-cols-1 gap-4 mb-4">
                    <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">样本大小</div>
                        <div class="keyword text-lg mb-1">The participants (n = 150) completed the survey.</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə pɑːrˈtɪsəpənts (en ˈiːkwəlz wʌn ˈhʌndrəd ˈfɪfti) kəmˈpliːtəd ðə ˈsɜːrveɪ/</div>
                        <div class="text-gray-700">参与者（n = 150）完成了调查。</div>
                    </div>

                    <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">统计显著性</div>
                        <div class="keyword text-lg mb-1">The difference was significant (p < 0.05).</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə ˈdɪfərəns wʌz sɪɡˈnɪfɪkənt (piː les ðæn ˈzɪroʊ pɔɪnt ˈzɪroʊ faɪv)/</div>
                        <div class="text-gray-700">差异具有统计学意义（p < 0.05）。</div>
                    </div>

                    <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">置信区间</div>
                        <div class="keyword text-lg mb-1">The mean score was 85.3 (95% CI: 82.1-88.5).</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə miːn skɔːr wʌz ˈeɪti faɪv pɔɪnt θriː (ˈnaɪnti faɪv pərˈsent siː aɪ: ˈeɪti tuː pɔɪnt wʌn tu ˈeɪti eɪt pɔɪnt faɪv)/</div>
                        <div class="text-gray-700">平均分为85.3（95%置信区间：82.1-88.5）。</div>
                    </div>

                    <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">标准差</div>
                        <div class="keyword text-lg mb-1">The average age was 25.4 years (SD = 3.2).</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə ˈævərɪdʒ eɪdʒ wʌz ˈtwenti faɪv pɔɪnt fɔːr jɪrz (es diː ˈiːkwəlz θriː pɔɪnt tuː)/</div>
                        <div class="text-gray-700">平均年龄为25.4岁（SD = 3.2）。</div>
                    </div>
                </div>
            </div>

            <div class="mb-6">
                <h3 class="text-xl font-semibold mb-3">引用格式和参考文献</h3>
                <p class="mb-4">不同的引用格式对括号的使用有特定要求。</p>

                <div class="grid grid-cols-1 gap-4 mb-4">
                    <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">APA格式</div>
                        <div class="keyword text-lg mb-1">According to Smith (2023), the results were significant.</div>
                        <div class="text-sm text-gray-600 mb-1">/əˈkɔːrdɪŋ tuː smɪθ (ˈtuː ˈθaʊzənd ˈtwenti θriː), ðə rɪˈzʌlts wɜːr sɪɡˈnɪfɪkənt/</div>
                        <div class="text-gray-700">根据Smith（2023）的研究，结果具有显著性。</div>
                    </div>

                    <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">多作者引用</div>
                        <div class="keyword text-lg mb-1">Recent studies (Johnson & Lee, 2022; Wang et al., 2023) support this.</div>
                        <div class="text-sm text-gray-600 mb-1">/ˈriːsənt ˈstʌdiz (ˈdʒɑːnsən ænd liː, ˈtuː ˈθaʊzənd ˈtwenti tuː; wɑːŋ et æl., ˈtuː ˈθaʊzənd ˈtwenti θriː) səˈpɔːrt ðɪs/</div>
                        <div class="text-gray-700">最近的研究（Johnson & Lee, 2022; Wang et al., 2023）支持这一观点。</div>
                    </div>

                    <div class="card bg-lime-50 border border-lime-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">页码引用</div>
                        <div class="keyword text-lg mb-1">The author states "knowledge is power" (Brown, 2021, p. 45).</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə ˈɔːθər steɪts "ˈnɑːlɪdʒ ɪz ˈpaʊər" (braʊn, ˈtuː ˈθaʊzənd ˈtwenti wʌn, piː ˈfɔːrti faɪv)/</div>
                        <div class="text-gray-700">作者指出"知识就是力量"（Brown, 2021, p. 45）。</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 创意写作中的括号运用 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4">创意写作中的括号运用</h2>

            <div class="mb-6">
                <h3 class="text-xl font-semibold mb-3">内心独白和旁白</h3>
                <p class="mb-4">在创意写作中，括号可以表示角色的内心想法或作者的旁白评论。</p>

                <div class="grid grid-cols-1 gap-4 mb-4">
                    <div class="card bg-amber-50 border border-amber-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">内心想法</div>
                        <div class="keyword text-lg mb-1">"I'll be there soon," she said (though she had no intention of going).</div>
                        <div class="text-sm text-gray-600 mb-1">/"aɪl biː ðer suːn," ʃiː sed (ðoʊ ʃiː hæd noʊ ɪnˈtenʃən ʌv ˈɡoʊɪŋ)/</div>
                        <div class="text-gray-700">"我很快就到，"她说（尽管她根本不打算去）。</div>
                    </div>

                    <div class="card bg-violet-50 border border-violet-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">作者旁白</div>
                        <div class="keyword text-lg mb-1">The old house stood empty (it had been vacant for years).</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə oʊld haʊs stʊd ˈempti (ɪt hæd bɪn ˈveɪkənt fɔːr jɪrz)/</div>
                        <div class="text-gray-700">老房子空无一人（它已经空置多年了）。</div>
                    </div>

                    <div class="card bg-rose-50 border border-rose-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">讽刺评论</div>
                        <div class="keyword text-lg mb-1">He was a "brilliant" student (who never studied).</div>
                        <div class="text-sm text-gray-600 mb-1">/hiː wʌz ə "ˈbrɪljənt" ˈstuːdənt (huː ˈnevər ˈstʌdid)/</div>
                        <div class="text-gray-700">他是个"优秀"学生（从不学习的那种）。</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 新闻写作中的括号使用 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4">新闻写作中的括号使用</h2>

            <div class="mb-6">
                <h3 class="text-xl font-semibold mb-3">新闻报道中的补充信息</h3>
                <p class="mb-4">在新闻写作中，括号用于提供背景信息、年龄、职位或其他相关细节。</p>

                <div class="grid grid-cols-1 gap-4 mb-4">
                    <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">年龄标注</div>
                        <div class="keyword text-lg mb-1">Mayor Johnson (45) announced the new policy yesterday.</div>
                        <div class="text-sm text-gray-600 mb-1">/ˈmeɪər ˈdʒɑːnsən (ˈfɔːrti faɪv) əˈnaʊnst ðə nuː ˈpɑːləsi ˈjestərdeɪ/</div>
                        <div class="text-gray-700">市长约翰逊（45岁）昨天宣布了新政策。</div>
                    </div>

                    <div class="card bg-sky-50 border border-sky-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">党派标注</div>
                        <div class="keyword text-lg mb-1">Senator Smith (D-California) voted against the bill.</div>
                        <div class="text-sm text-gray-600 mb-1">/ˈsenətər smɪθ (diː kæləˈfɔːrnjə) ˈvoʊtəd əˈɡenst ðə bɪl/</div>
                        <div class="text-gray-700">参议员史密斯（加州民主党）投票反对该法案。</div>
                    </div>

                    <div class="card bg-stone-50 border border-stone-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">公司信息</div>
                        <div class="keyword text-lg mb-1">Apple Inc. (NASDAQ: AAPL) reported strong earnings.</div>
                        <div class="text-sm text-gray-600 mb-1">/ˈæpəl ɪŋk (ˈnæzdæk: ˈeɪ eɪ piː el) rɪˈpɔːrtəd strɔːŋ ˈɜːrnɪŋz/</div>
                        <div class="text-gray-700">苹果公司（纳斯达克：AAPL）报告了强劲的收益。</div>
                    </div>

                    <div class="card bg-neutral-50 border border-neutral-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">地点说明</div>
                        <div class="keyword text-lg mb-1">The accident occurred on Highway 101 (near San Francisco).</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə ˈæksədənt əˈkɜːrd ɑːn ˈhaɪweɪ wʌn oʊ wʌn (nɪr sæn frənˈsɪskoʊ)/</div>
                        <div class="text-gray-700">事故发生在101号高速公路上（旧金山附近）。</div>
                    </div>
                </div>
            </div>

            <div class="mb-6">
                <h3 class="text-xl font-semibold mb-3">体育报道中的括号</h3>
                <p class="mb-4">体育新闻中，括号用于显示比分、记录或统计数据。</p>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">比赛记录</div>
                        <div class="keyword text-lg mb-1">The Lakers (15-8) defeated the Warriors (12-11).</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə ˈleɪkərz (ˈfɪfˈtiːn eɪt) dɪˈfiːtəd ðə ˈwɔːriərz (ˈtwelv ɪˈlevən)/</div>
                        <div class="text-gray-700">湖人队（15胜8负）击败了勇士队（12胜11负）。</div>
                    </div>

                    <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">球员统计</div>
                        <div class="keyword text-lg mb-1">James scored 30 points (12-of-18 shooting).</div>
                        <div class="text-sm text-gray-600 mb-1">/dʒeɪmz skɔːrd ˈθɜːrti pɔɪnts (ˈtwelv ʌv ˈeɪˈtiːn ˈʃuːtɪŋ)/</div>
                        <div class="text-gray-700">詹姆斯得到30分（18投12中）。</div>
                    </div>

                    <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">排名信息</div>
                        <div class="keyword text-lg mb-1">The team (ranked #3) won the championship.</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə tiːm (ræŋkt ˈnʌmbər θriː) wʌn ðə ˈtʃæmpiənʃɪp/</div>
                        <div class="text-gray-700">该队（排名第3）赢得了冠军。</div>
                    </div>

                    <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">时间记录</div>
                        <div class="keyword text-lg mb-1">She finished the race in 2:15:30 (a personal best).</div>
                        <div class="text-sm text-gray-600 mb-1">/ʃiː ˈfɪnɪʃt ðə reɪs ɪn tuː ˈfɪfˈtiːn ˈθɜːrti (ə ˈpɜːrsənəl best)/</div>
                        <div class="text-gray-700">她以2:15:30完成比赛（个人最佳成绩）。</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 科学写作中的括号应用 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4">科学写作中的括号应用</h2>

            <div class="mb-6">
                <h3 class="text-xl font-semibold mb-3">化学和物理表达</h3>
                <p class="mb-4">在科学文献中，括号用于化学式、物理单位和实验条件的标注。</p>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">化学式</div>
                        <div class="keyword text-lg mb-1">Water (H₂O) is essential for life.</div>
                        <div class="text-sm text-gray-600 mb-1">/ˈwɔːtər (eɪtʃ tuː oʊ) ɪz ɪˈsenʃəl fɔːr laɪf/</div>
                        <div class="text-gray-700">水（H₂O）是生命必需的。</div>
                    </div>

                    <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">物理单位</div>
                        <div class="keyword text-lg mb-1">The speed of light is 299,792,458 m/s (in vacuum).</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə spiːd ʌv laɪt ɪz tuː ˈnaɪnti naɪn ˈmɪljən ˈsevən ˈhʌndrəd ˈnaɪnti tuː ˈθaʊzənd fɔːr ˈhʌndrəd ˈfɪfti eɪt ˈmiːtərz pər ˈsekənd (ɪn ˈvækjuːm)/</div>
                        <div class="text-gray-700">光速是299,792,458米/秒（真空中）。</div>
                    </div>

                    <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">实验条件</div>
                        <div class="keyword text-lg mb-1">The reaction was conducted at 25°C (room temperature).</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə riˈækʃən wʌz kənˈdʌktəd æt ˈtwenti faɪv dɪˈɡriːz ˈselsɪəs (ruːm ˈtemprətʃər)/</div>
                        <div class="text-gray-700">反应在25°C（室温）下进行。</div>
                    </div>

                    <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">浓度表示</div>
                        <div class="keyword text-lg mb-1">Add 10 mL of HCl (0.1 M concentration).</div>
                        <div class="text-sm text-gray-600 mb-1">/æd ten ˈmɪləˌliːtərz ʌv eɪtʃ siː el (ˈzɪroʊ pɔɪnt wʌn ˈmoʊlər ˌkɑːnsənˈtreɪʃən)/</div>
                        <div class="text-gray-700">加入10毫升HCl（0.1M浓度）。</div>
                    </div>
                </div>
            </div>

            <div class="mb-6">
                <h3 class="text-xl font-semibold mb-3">生物学和医学术语</h3>
                <p class="mb-4">生物学和医学文献中，括号用于学名、基因符号和医学缩写。</p>

                <div class="grid grid-cols-1 gap-4 mb-4">
                    <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">生物学名</div>
                        <div class="keyword text-lg mb-1">The common oak (Quercus robur) is native to Europe.</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə ˈkɑːmən oʊk (ˈkwɜːrkəs ˈroʊbər) ɪz ˈneɪtɪv tuː ˈjʊrəp/</div>
                        <div class="text-gray-700">普通橡树（Quercus robur）原产于欧洲。</div>
                    </div>

                    <div class="card bg-lime-50 border border-lime-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">基因表达</div>
                        <div class="keyword text-lg mb-1">The BRCA1 gene (breast cancer susceptibility gene) was studied.</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə biː ɑːr siː eɪ wʌn dʒiːn (brest ˈkænsər səˌseptəˈbɪləti dʒiːn) wʌz ˈstʌdid/</div>
                        <div class="text-gray-700">研究了BRCA1基因（乳腺癌易感基因）。</div>
                    </div>

                    <div class="card bg-amber-50 border border-amber-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">医学缩写</div>
                        <div class="keyword text-lg mb-1">The patient has COPD (chronic obstructive pulmonary disease).</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə ˈpeɪʃənt hæz siː oʊ piː diː (ˈkrɑːnɪk əbˈstrʌktɪv ˈpʊlməˌneri dɪˈziːz)/</div>
                        <div class="text-gray-700">患者患有COPD（慢性阻塞性肺疾病）。</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 商务通信中的括号使用 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4">商务通信中的括号使用</h2>

            <div class="mb-6">
                <h3 class="text-xl font-semibold mb-3">邮件和备忘录</h3>
                <p class="mb-4">在商务邮件中，括号用于提供额外信息、截止日期或联系方式。</p>

                <div class="grid grid-cols-1 gap-4 mb-4">
                    <div class="card bg-violet-50 border border-violet-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">截止日期</div>
                        <div class="keyword text-lg mb-1">Please submit your report by Friday (March 15th).</div>
                        <div class="text-sm text-gray-600 mb-1">/pliːz səbˈmɪt jʊr rɪˈpɔːrt baɪ ˈfraɪdeɪ (mɑːrtʃ ˈfɪfˈtiːnθ)/</div>
                        <div class="text-gray-700">请在星期五（3月15日）之前提交您的报告。</div>
                    </div>

                    <div class="card bg-rose-50 border border-rose-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">联系信息</div>
                        <div class="keyword text-lg mb-1">Contact Sarah Johnson (ext. 1234) for more details.</div>
                        <div class="text-sm text-gray-600 mb-1">/ˈkɑːntækt ˈserə ˈdʒɑːnsən (ɪkˈstenʃən ˈtwelv ˈθɜːrti fɔːr) fɔːr mɔːr ˈdiːteɪlz/</div>
                        <div class="text-gray-700">联系Sarah Johnson（分机1234）了解更多详情。</div>
                    </div>

                    <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">会议信息</div>
                        <div class="keyword text-lg mb-1">The meeting is scheduled for 2 PM (Conference Room A).</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə ˈmiːtɪŋ ɪz ˈskedʒuːld fɔːr tuː piː em (ˈkɑːnfərəns ruːm eɪ)/</div>
                        <div class="text-gray-700">会议安排在下午2点（会议室A）。</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 数字和货币表示中的括号 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4">数字和货币表示中的括号</h2>

            <div class="mb-6">
                <h3 class="text-xl font-semibold mb-3">财务报表和会计</h3>
                <p class="mb-4">在财务文档中，括号通常用于表示负数、损失或需要特别注意的数字。</p>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div class="card bg-sky-50 border border-sky-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">负数表示</div>
                        <div class="keyword text-lg mb-1">The company reported a loss of ($2.5 million).</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə ˈkʌmpəni rɪˈpɔːrtəd ə lɔːs ʌv (tuː pɔɪnt faɪv ˈmɪljən ˈdɑːlərz)/</div>
                        <div class="text-gray-700">公司报告亏损（250万美元）。</div>
                    </div>

                    <div class="card bg-stone-50 border border-stone-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">预算分配</div>
                        <div class="keyword text-lg mb-1">Marketing budget: $50,000 (increased from $40,000).</div>
                        <div class="text-sm text-gray-600 mb-1">/ˈmɑːrkətɪŋ ˈbʌdʒət: ˈfɪfti ˈθaʊzənd ˈdɑːlərz (ɪnˈkriːst frʌm ˈfɔːrti ˈθaʊzənd ˈdɑːlərz)/</div>
                        <div class="text-gray-700">营销预算：50,000美元（从40,000美元增加）。</div>
                    </div>

                    <div class="card bg-neutral-50 border border-neutral-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">百分比变化</div>
                        <div class="keyword text-lg mb-1">Sales increased by 15% (compared to last year).</div>
                        <div class="text-sm text-gray-600 mb-1">/seɪlz ɪnˈkriːst baɪ ˈfɪfˈtiːn pərˈsent (kəmˈperd tuː læst jɪr)/</div>
                        <div class="text-gray-700">销售额增长了15%（与去年相比）。</div>
                    </div>

                    <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">汇率转换</div>
                        <div class="keyword text-lg mb-1">The price is €100 (approximately $110).</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə praɪs ɪz wʌn ˈhʌndrəd ˈjʊroʊz (əˈprɑːksəmətli wʌn ˈhʌndrəd ten ˈdɑːlərz)/</div>
                        <div class="text-gray-700">价格是100欧元（约110美元）。</div>
                    </div>
                </div>
            </div>

            <div class="mb-6">
                <h3 class="text-xl font-semibold mb-3">统计数据和调查结果</h3>
                <p class="mb-4">在数据报告中，括号用于提供样本大小、误差范围或补充统计信息。</p>

                <div class="grid grid-cols-1 gap-4 mb-4">
                    <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">调查结果</div>
                        <div class="keyword text-lg mb-1">85% of respondents agreed (margin of error: ±3%).</div>
                        <div class="text-sm text-gray-600 mb-1">/ˈeɪti faɪv pərˈsent ʌv rɪˈspɑːndənts əˈɡriːd (ˈmɑːrdʒən ʌv ˈerər: plʌs ɔːr ˈmaɪnəs θriː pərˈsent)/</div>
                        <div class="text-gray-700">85%的受访者同意（误差范围：±3%）。</div>
                    </div>

                    <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">人口统计</div>
                        <div class="keyword text-lg mb-1">The city has 500,000 residents (2023 census data).</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə ˈsɪti hæz faɪv ˈhʌndrəd ˈθaʊzənd ˈrezədənts (ˈtuː ˈθaʊzənd ˈtwenti θriː ˈsensəs ˈdeɪtə)/</div>
                        <div class="text-gray-700">该市有50万居民（2023年人口普查数据）。</div>
                    </div>

                    <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">增长率</div>
                        <div class="keyword text-lg mb-1">GDP grew by 3.2% (fastest rate in five years).</div>
                        <div class="text-sm text-gray-600 mb-1">/dʒiː diː piː ɡruː baɪ θriː pɔɪnt tuː pərˈsent (ˈfæstəst reɪt ɪn faɪv jɪrz)/</div>
                        <div class="text-gray-700">GDP增长了3.2%（五年来最快增长率）。</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 教育材料中的括号使用 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4">教育材料中的括号使用</h2>

            <div class="mb-6">
                <h3 class="text-xl font-semibold mb-3">教学指导和说明</h3>
                <p class="mb-4">在教育材料中，括号用于提供教学提示、答案或补充解释。</p>

                <div class="grid grid-cols-1 gap-4 mb-4">
                    <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">教师指导</div>
                        <div class="keyword text-lg mb-1">Read the passage aloud (emphasize key words).</div>
                        <div class="text-sm text-gray-600 mb-1">/riːd ðə ˈpæsɪdʒ əˈlaʊd (ˈemfəsaɪz kiː wɜːrdz)/</div>
                        <div class="text-gray-700">大声朗读这段文字（强调关键词）。</div>
                    </div>

                    <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">学习提示</div>
                        <div class="keyword text-lg mb-1">Study Chapter 5 (focus on examples 5.1-5.3).</div>
                        <div class="text-sm text-gray-600 mb-1">/ˈstʌdi ˈtʃæptər faɪv (ˈfoʊkəs ɑːn ɪɡˈzæmpəlz faɪv pɔɪnt wʌn tu faɪv pɔɪnt θriː)/</div>
                        <div class="text-gray-700">学习第5章（重点关注例题5.1-5.3）。</div>
                    </div>

                    <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">时间分配</div>
                        <div class="keyword text-lg mb-1">Complete the assignment (allow 2 hours).</div>
                        <div class="text-sm text-gray-600 mb-1">/kəmˈpliːt ðə əˈsaɪnmənt (əˈlaʊ tuː ˈaʊərz)/</div>
                        <div class="text-gray-700">完成作业（预留2小时）。</div>
                    </div>

                    <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">参考页码</div>
                        <div class="keyword text-lg mb-1">Review the formula (see page 45).</div>
                        <div class="text-sm text-gray-600 mb-1">/rɪˈvjuː ðə ˈfɔːrmjələ (siː peɪdʒ ˈfɔːrti faɪv)/</div>
                        <div class="text-gray-700">复习公式（见第45页）。</div>
                    </div>
                </div>
            </div>

            <div class="mb-6">
                <h3 class="text-xl font-semibold mb-3">词汇学习和语言教学</h3>
                <p class="mb-4">在语言学习材料中，括号用于提供发音、词性或使用说明。</p>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">词性标注</div>
                        <div class="keyword text-lg mb-1">Beautiful (adjective) describes the sunset.</div>
                        <div class="text-sm text-gray-600 mb-1">/ˈbjuːtəfəl (ˈædʒɪktɪv) dɪˈskraɪbz ðə ˈsʌnset/</div>
                        <div class="text-gray-700">Beautiful（形容词）描述日落。</div>
                    </div>

                    <div class="card bg-lime-50 border border-lime-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">同义词</div>
                        <div class="keyword text-lg mb-1">Happy (joyful, cheerful) is a positive emotion.</div>
                        <div class="text-sm text-gray-600 mb-1">/ˈhæpi (ˈdʒɔɪfəl, ˈtʃɪrfəl) ɪz ə ˈpɑːzətɪv ɪˈmoʊʃən/</div>
                        <div class="text-gray-700">Happy（快乐的，愉快的）是一种积极情绪。</div>
                    </div>

                    <div class="card bg-amber-50 border border-amber-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">使用场合</div>
                        <div class="keyword text-lg mb-1">Greetings (formal situations) require proper etiquette.</div>
                        <div class="text-sm text-gray-600 mb-1">/ˈɡriːtɪŋz (ˈfɔːrməl ˌsɪtʃuˈeɪʃənz) rɪˈkwaɪər ˈprɑːpər ˈetɪkət/</div>
                        <div class="text-gray-700">问候语（正式场合）需要适当的礼仪。</div>
                    </div>

                    <div class="card bg-violet-50 border border-violet-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">语法规则</div>
                        <div class="keyword text-lg mb-1">Use "a" before consonants (not vowels).</div>
                        <div class="text-sm text-gray-600 mb-1">/juːz "eɪ" bɪˈfɔːr ˈkɑːnsənənts (nɑːt ˈvaʊəlz)/</div>
                        <div class="text-gray-700">在辅音前使用"a"（不是元音）。</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 网络和数字媒体中的括号 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4">网络和数字媒体中的括号</h2>

            <div class="mb-6">
                <h3 class="text-xl font-semibold mb-3">社交媒体和在线交流</h3>
                <p class="mb-4">在数字通信中，括号用于表达情感、提供链接信息或添加标签。</p>

                <div class="grid grid-cols-1 gap-4 mb-4">
                    <div class="card bg-rose-50 border border-rose-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">情感表达</div>
                        <div class="keyword text-lg mb-1">Great news everyone! (I'm so excited!)</div>
                        <div class="text-sm text-gray-600 mb-1">/ɡreɪt nuːz ˈevriˌwʌn! (aɪm soʊ ɪkˈsaɪtəd!)/</div>
                        <div class="text-gray-700">大家好消息！（我太兴奋了！）</div>
                    </div>

                    <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">链接说明</div>
                        <div class="keyword text-lg mb-1">Check out this article (link in bio).</div>
                        <div class="text-sm text-gray-600 mb-1">/tʃek aʊt ðɪs ˈɑːrtɪkəl (lɪŋk ɪn ˈbaɪoʊ)/</div>
                        <div class="text-gray-700">看看这篇文章（链接在简介中）。</div>
                    </div>

                    <div class="card bg-sky-50 border border-sky-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">标签说明</div>
                        <div class="keyword text-lg mb-1">Beautiful sunset today (#nofilter).</div>
                        <div class="text-sm text-gray-600 mb-1">/ˈbjuːtəfəl ˈsʌnset təˈdeɪ (hæʃtæɡ noʊ ˈfɪltər)/</div>
                        <div class="text-gray-700">今天美丽的日落（#无滤镜）。</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 括号使用的常见错误 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4">括号使用的常见错误与正确示例</h2>

            <div class="mb-6">
                <h3 class="text-xl font-semibold mb-3">标点符号的位置错误</h3>
                <p class="mb-4">括号与其他标点符号的位置关系经常出现错误。</p>

                <div class="grid grid-cols-1 gap-4 mb-4">
                    <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">错误示例</div>
                        <div class="keyword text-lg mb-1">She went to the store (to buy milk.)</div>
                        <div class="text-sm text-gray-600 mb-1">/ʃiː went tuː ðə stɔːr (tuː baɪ mɪlk.)/</div>
                        <div class="text-gray-700">她去商店（买牛奶。）</div>
                    </div>

                    <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">正确示例</div>
                        <div class="keyword text-lg mb-1">She went to the store (to buy milk).</div>
                        <div class="text-sm text-gray-600 mb-1">/ʃiː went tuː ðə stɔːr (tuː baɪ mɪlk)/</div>
                        <div class="text-gray-700">她去商店（买牛奶）。</div>
                    </div>
                </div>
            </div>

            <div class="mb-6">
                <h3 class="text-xl font-semibold mb-3">过度使用括号</h3>
                <p class="mb-4">避免在一个句子中使用过多的括号，这会影响阅读流畅性。</p>

                <div class="grid grid-cols-1 gap-4 mb-4">
                    <div class="card bg-red-50 border border-red-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">过度使用</div>
                        <div class="keyword text-lg mb-1">The meeting (scheduled for 2 PM) (in Room A) (with all staff) was cancelled.</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə ˈmiːtɪŋ (ˈskedʒuːld fɔːr tuː piː em) (ɪn ruːm eɪ) (wɪð ɔːl stæf) wʌz ˈkænsəld/</div>
                        <div class="text-gray-700">会议（安排在下午2点）（在A室）（全体员工参加）被取消了。</div>
                    </div>

                    <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">改进版本</div>
                        <div class="keyword text-lg mb-1">The staff meeting scheduled for 2 PM in Room A was cancelled.</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə stæf ˈmiːtɪŋ ˈskedʒuːld fɔːr tuː piː em ɪn ruːm eɪ wʌz ˈkænsəld/</div>
                        <div class="text-gray-700">安排在下午2点A室举行的员工会议被取消了。</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 专业领域中的特殊括号用法 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4">专业领域中的特殊括号用法</h2>

            <div class="mb-6">
                <h3 class="text-xl font-semibold mb-3">法律文件中的括号</h3>
                <p class="mb-4">法律文件中的括号使用有特定的格式和含义。</p>

                <div class="grid grid-cols-1 gap-4 mb-4">
                    <div class="card bg-stone-50 border border-stone-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">法律条款</div>
                        <div class="keyword text-lg mb-1">The defendant (hereinafter "Defendant") agrees to the terms.</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə dɪˈfendənt (ˌhɪrɪnˈæftər "dɪˈfendənt") əˈɡriːz tuː ðə tɜːrmz/</div>
                        <div class="text-gray-700">被告（以下简称"被告"）同意这些条款。</div>
                    </div>

                    <div class="card bg-neutral-50 border border-neutral-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">法规引用</div>
                        <div class="keyword text-lg mb-1">According to Section 15 (subsection a), the penalty is $500.</div>
                        <div class="text-sm text-gray-600 mb-1">/əˈkɔːrdɪŋ tuː ˈsekʃən ˈfɪfˈtiːn (ˈsʌbsekʃən eɪ), ðə ˈpenəlti ɪz faɪv ˈhʌndrəd ˈdɑːlərz/</div>
                        <div class="text-gray-700">根据第15条（a款），罚款为500美元。</div>
                    </div>

                    <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">案例引用</div>
                        <div class="keyword text-lg mb-1">In Smith v. Jones (1995), the court ruled in favor of the plaintiff.</div>
                        <div class="text-sm text-gray-600 mb-1">/ɪn smɪθ ˈvɜːrsəs dʒoʊnz (ˈnaɪnˈtiːn ˈnaɪnti faɪv), ðə kɔːrt ruːld ɪn ˈfeɪvər ʌv ðə ˈpleɪntɪf/</div>
                        <div class="text-gray-700">在Smith诉Jones案（1995年）中，法院判决原告胜诉。</div>
                    </div>
                </div>
            </div>

            <div class="mb-6">
                <h3 class="text-xl font-semibold mb-3">医学文献中的括号</h3>
                <p class="mb-4">医学文献中括号用于药物剂量、诊断代码和临床数据。</p>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">药物剂量</div>
                        <div class="keyword text-lg mb-1">Prescribe aspirin (325 mg daily) for pain relief.</div>
                        <div class="text-sm text-gray-600 mb-1">/prɪˈskraɪb ˈæsprɪn (θriː ˈhʌndrəd ˈtwenti faɪv ˈmɪləˌɡræmz ˈdeɪli) fɔːr peɪn rɪˈliːf/</div>
                        <div class="text-gray-700">开阿司匹林（每日325毫克）用于止痛。</div>
                    </div>

                    <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">诊断代码</div>
                        <div class="keyword text-lg mb-1">Patient diagnosed with diabetes (ICD-10: E11.9).</div>
                        <div class="text-sm text-gray-600 mb-1">/ˈpeɪʃənt ˈdaɪəɡnoʊzd wɪð ˌdaɪəˈbiːtiːz (aɪ siː diː ten: iː ɪˈlevən pɔɪnt naɪn)/</div>
                        <div class="text-gray-700">患者诊断为糖尿病（ICD-10: E11.9）。</div>
                    </div>

                    <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">生命体征</div>
                        <div class="keyword text-lg mb-1">Blood pressure: 120/80 mmHg (normal range).</div>
                        <div class="text-sm text-gray-600 mb-1">/blʌd ˈpreʃər: wʌn ˈhʌndrəd ˈtwenti ˈoʊvər ˈeɪti ˈmɪləˌmiːtərz ʌv ˈmɜːrkjəri (ˈnɔːrməl reɪndʒ)/</div>
                        <div class="text-gray-700">血压：120/80毫米汞柱（正常范围）。</div>
                    </div>

                    <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">实验室结果</div>
                        <div class="keyword text-lg mb-1">Cholesterol level: 180 mg/dL (within normal limits).</div>
                        <div class="text-sm text-gray-600 mb-1">/kəˈlestərɔːl ˈlevəl: wʌn ˈhʌndrəd ˈeɪti ˈmɪləˌɡræmz pər ˈdesəˌliːtər (wɪˈðɪn ˈnɔːrməl ˈlɪməts)/</div>
                        <div class="text-gray-700">胆固醇水平：180毫克/分升（正常范围内）。</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 国际化和多语言环境中的括号 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4">国际化和多语言环境中的括号</h2>

            <div class="mb-6">
                <h3 class="text-xl font-semibold mb-3">翻译和双语文本</h3>
                <p class="mb-4">在多语言文档中，括号用于提供翻译、原文或音译。</p>

                <div class="grid grid-cols-1 gap-4 mb-4">
                    <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">中英对照</div>
                        <div class="keyword text-lg mb-1">Welcome to Beijing (北京欢迎您).</div>
                        <div class="text-sm text-gray-600 mb-1">/ˈwelkəm tuː ˈbeɪˈdʒɪŋ (北京欢迎您)/</div>
                        <div class="text-gray-700">欢迎来到北京（北京欢迎您）。</div>
                    </div>

                    <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">音译标注</div>
                        <div class="keyword text-lg mb-1">Tokyo (東京, Tōkyō) is Japan's capital.</div>
                        <div class="text-sm text-gray-600 mb-1">/ˈtoʊkioʊ (東京, ˈtoʊkjoʊ) ɪz dʒəˈpænz ˈkæpətəl/</div>
                        <div class="text-gray-700">东京（東京, Tōkyō）是日本的首都。</div>
                    </div>

                    <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">原文保留</div>
                        <div class="keyword text-lg mb-1">The phrase "carpe diem" (seize the day) is Latin.</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə freɪz "ˈkɑːrpeɪ ˈdiːəm" (siːz ðə deɪ) ɪz ˈlætən/</div>
                        <div class="text-gray-700">短语"carpe diem"（抓住今天）是拉丁语。</div>
                    </div>
                </div>
            </div>

            <div class="mb-6">
                <h3 class="text-xl font-semibold mb-3">文化和地理标注</h3>
                <p class="mb-4">在国际文档中，括号用于提供文化背景或地理位置信息。</p>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">国家标注</div>
                        <div class="keyword text-lg mb-1">The conference will be held in Geneva (Switzerland).</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə ˈkɑːnfərəns wɪl biː held ɪn dʒəˈniːvə (ˈswɪtsərlənd)/</div>
                        <div class="text-gray-700">会议将在日内瓦（瑞士）举行。</div>
                    </div>

                    <div class="card bg-lime-50 border border-lime-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">时区说明</div>
                        <div class="keyword text-lg mb-1">The meeting starts at 9 AM (local time).</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə ˈmiːtɪŋ stɑːrts æt naɪn eɪ em (ˈloʊkəl taɪm)/</div>
                        <div class="text-gray-700">会议在上午9点（当地时间）开始。</div>
                    </div>

                    <div class="card bg-amber-50 border border-amber-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">货币换算</div>
                        <div class="keyword text-lg mb-1">The price is ¥1000 (approximately $7 USD).</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə praɪs ɪz wʌn ˈθaʊzənd jen (əˈprɑːksəmətli ˈsevən ˈdɑːlərz juː es diː)/</div>
                        <div class="text-gray-700">价格是1000日元（约7美元）。</div>
                    </div>

                    <div class="card bg-violet-50 border border-violet-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">文化节日</div>
                        <div class="keyword text-lg mb-1">Chinese New Year (Spring Festival) is in February.</div>
                        <div class="text-sm text-gray-600 mb-1">/tʃaɪˈniːz nuː jɪr (sprɪŋ ˈfestəvəl) ɪz ɪn ˈfebruˌeri/</div>
                        <div class="text-gray-700">中国新年（春节）在二月。</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 现代数字通信中的括号演变 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4">现代数字通信中的括号演变</h2>

            <div class="mb-6">
                <h3 class="text-xl font-semibold mb-3">电子邮件和即时消息</h3>
                <p class="mb-4">在数字通信中，括号的使用方式有了新的发展和变化。</p>

                <div class="grid grid-cols-1 gap-4 mb-4">
                    <div class="card bg-rose-50 border border-rose-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">表情符号说明</div>
                        <div class="keyword text-lg mb-1">That was hilarious! (laughing emoji)</div>
                        <div class="text-sm text-gray-600 mb-1">/ðæt wʌz hɪˈleriəs! (ˈlæfɪŋ ɪˈmoʊdʒi)/</div>
                        <div class="text-gray-700">太搞笑了！（笑脸表情）</div>
                    </div>

                    <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">状态更新</div>
                        <div class="keyword text-lg mb-1">Working from home today (finally some quiet time).</div>
                        <div class="text-sm text-gray-600 mb-1">/ˈwɜːrkɪŋ frʌm hoʊm təˈdeɪ (ˈfaɪnəli sʌm ˈkwaɪət taɪm)/</div>
                        <div class="text-gray-700">今天在家工作（终于有些安静时间了）。</div>
                    </div>

                    <div class="card bg-sky-50 border border-sky-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">技术说明</div>
                        <div class="keyword text-lg mb-1">Please restart your computer (this may take a few minutes).</div>
                        <div class="text-sm text-gray-600 mb-1">/pliːz ˈriːstɑːrt jʊr kəmˈpjuːtər (ðɪs meɪ teɪk ə fjuː ˈmɪnəts)/</div>
                        <div class="text-gray-700">请重启您的计算机（这可能需要几分钟）。</div>
                    </div>
                </div>
            </div>

            <div class="mb-6">
                <h3 class="text-xl font-semibold mb-3">编程和技术文档</h3>
                <p class="mb-4">在编程环境中，括号有特定的语法意义和使用规则。</p>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div class="card bg-stone-50 border border-stone-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">函数调用</div>
                        <div class="keyword text-lg mb-1">Call the function print() to display output.</div>
                        <div class="text-sm text-gray-600 mb-1">/kɔːl ðə ˈfʌŋkʃən prɪnt() tuː dɪˈspleɪ ˈaʊtpʊt/</div>
                        <div class="text-gray-700">调用print()函数来显示输出。</div>
                    </div>

                    <div class="card bg-neutral-50 border border-neutral-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">数组索引</div>
                        <div class="keyword text-lg mb-1">Access the first element using array[0].</div>
                        <div class="text-sm text-gray-600 mb-1">/ˈækses ðə fɜːrst ˈeləmənt ˈjuːzɪŋ əˈreɪ[ˈzɪroʊ]/</div>
                        <div class="text-gray-700">使用array[0]访问第一个元素。</div>
                    </div>

                    <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">条件语句</div>
                        <div class="keyword text-lg mb-1">Use if (condition) to check the value.</div>
                        <div class="text-sm text-gray-600 mb-1">/juːz ɪf (kənˈdɪʃən) tuː tʃek ðə ˈvæljuː/</div>
                        <div class="text-gray-700">使用if (condition)来检查值。</div>
                    </div>

                    <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">注释说明</div>
                        <div class="keyword text-lg mb-1">This code (line 25-30) handles user input.</div>
                        <div class="text-sm text-gray-600 mb-1">/ðɪs koʊd (laɪn ˈtwenti faɪv tu ˈθɜːrti) ˈhændəlz ˈjuːzər ˈɪnpʊt/</div>
                        <div class="text-gray-700">这段代码（第25-30行）处理用户输入。</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 括号在不同写作风格中的应用 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4">括号在不同写作风格中的应用</h2>

            <div class="mb-6">
                <h3 class="text-xl font-semibold mb-3">正式写作风格</h3>
                <p class="mb-4">在正式文档中，括号的使用应该谨慎和准确。</p>

                <div class="grid grid-cols-1 gap-4 mb-4">
                    <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">学术论文</div>
                        <div class="keyword text-lg mb-1">The research methodology (described in Section 3) was rigorous.</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə rɪˈsɜːrtʃ ˌmeθəˈdɑːlədʒi (dɪˈskraɪbd ɪn ˈsekʃən θriː) wʌz ˈrɪɡərəs/</div>
                        <div class="text-gray-700">研究方法（第3节中描述）是严格的。</div>
                    </div>

                    <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">商业报告</div>
                        <div class="keyword text-lg mb-1">Revenue increased by 15% (compared to Q3 2022).</div>
                        <div class="text-sm text-gray-600 mb-1">/ˈrevəˌnuː ɪnˈkriːst baɪ ˈfɪfˈtiːn pərˈsent (kəmˈperd tuː kjuː θriː ˈtuː ˈθaʊzənd ˈtwenti tuː)/</div>
                        <div class="text-gray-700">收入增长了15%（与2022年第三季度相比）。</div>
                    </div>

                    <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">政府文件</div>
                        <div class="keyword text-lg mb-1">The new policy (effective January 1st) will benefit citizens.</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə nuː ˈpɑːləsi (ɪˈfektɪv ˈdʒænjuˌeri fɜːrst) wɪl ˈbenəfɪt ˈsɪtəzənz/</div>
                        <div class="text-gray-700">新政策（1月1日生效）将使公民受益。</div>
                    </div>
                </div>
            </div>

            <div class="mb-6">
                <h3 class="text-xl font-semibold mb-3">非正式写作风格</h3>
                <p class="mb-4">在非正式写作中，括号可以更灵活地表达个人想法和情感。</p>

                <div class="grid grid-cols-1 gap-4 mb-4">
                    <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">个人博客</div>
                        <div class="keyword text-lg mb-1">I love coffee (especially in the morning!).</div>
                        <div class="text-sm text-gray-600 mb-1">/aɪ lʌv ˈkɔːfi (ɪˈspeʃəli ɪn ðə ˈmɔːrnɪŋ!)/</div>
                        <div class="text-gray-700">我喜欢咖啡（特别是在早上！）。</div>
                    </div>

                    <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">朋友聊天</div>
                        <div class="keyword text-lg mb-1">The movie was okay (not great, but watchable).</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə ˈmuːvi wʌz ˈoʊkeɪ (nɑːt ɡreɪt, bʌt ˈwɑːtʃəbəl)/</div>
                        <div class="text-gray-700">电影还可以（不是很棒，但能看）。</div>
                    </div>

                    <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">日记写作</div>
                        <div class="keyword text-lg mb-1">Today was exhausting (but productive).</div>
                        <div class="text-sm text-gray-600 mb-1">/təˈdeɪ wʌz ɪɡˈzɔːstɪŋ (bʌt prəˈdʌktɪv)/</div>
                        <div class="text-gray-700">今天很累（但很有成效）。</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 括号与其他标点符号的配合使用 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4">括号与其他标点符号的配合使用</h2>

            <div class="mb-6">
                <h3 class="text-xl font-semibold mb-3">括号与逗号的配合</h3>
                <p class="mb-4">括号和逗号的正确配合使用能够使句子结构更加清晰。</p>

                <div class="grid grid-cols-1 gap-4 mb-4">
                    <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">列举中的括号</div>
                        <div class="keyword text-lg mb-1">We need supplies: paper (white), pens (blue), and folders (manila).</div>
                        <div class="text-sm text-gray-600 mb-1">/wiː niːd səˈplaɪz: ˈpeɪpər (waɪt), penz (bluː), ænd ˈfoʊldərz (məˈnɪlə)/</div>
                        <div class="text-gray-700">我们需要用品：纸张（白色），笔（蓝色），和文件夹（马尼拉纸）。</div>
                    </div>

                    <div class="card bg-lime-50 border border-lime-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">复合句中的括号</div>
                        <div class="keyword text-lg mb-1">Although it was raining (quite heavily), we decided to go out.</div>
                        <div class="text-sm text-gray-600 mb-1">/ɔːlˈðoʊ ɪt wʌz ˈreɪnɪŋ (kwaɪt ˈhevəli), wiː dɪˈsaɪdəd tuː ɡoʊ aʊt/</div>
                        <div class="text-gray-700">尽管下雨（相当大），我们还是决定出去。</div>
                    </div>

                    <div class="card bg-amber-50 border border-amber-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">插入语的位置</div>
                        <div class="keyword text-lg mb-1">The book, which I bought yesterday (at the bookstore), is excellent.</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə bʊk, wɪtʃ aɪ bɔːt ˈjestərdeɪ (æt ðə ˈbʊkstɔːr), ɪz ˈeksələnt/</div>
                        <div class="text-gray-700">这本书，我昨天买的（在书店），很棒。</div>
                    </div>
                </div>
            </div>

            <div class="mb-6">
                <h3 class="text-xl font-semibold mb-3">括号与引号的配合</h3>
                <p class="mb-4">在引用文本中使用括号时需要注意正确的位置关系。</p>

                <div class="grid grid-cols-1 gap-4 mb-4">
                    <div class="card bg-violet-50 border border-violet-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">引用中的解释</div>
                        <div class="keyword text-lg mb-1">He said, "I'll be there soon (around 3 PM)."</div>
                        <div class="text-sm text-gray-600 mb-1">/hiː sed, "aɪl biː ðer suːn (əˈraʊnd θriː piː em)"/</div>
                        <div class="text-gray-700">他说："我很快就到（大约下午3点）。"</div>
                    </div>

                    <div class="card bg-rose-50 border border-rose-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">引用后的说明</div>
                        <div class="keyword text-lg mb-1">"Knowledge is power" (Francis Bacon) is a famous quote.</div>
                        <div class="text-sm text-gray-600 mb-1">/"ˈnɑːlɪdʒ ɪz ˈpaʊər" (ˈfrænsəs ˈbeɪkən) ɪz ə ˈfeɪməs kwoʊt/</div>
                        <div class="text-gray-700">"知识就是力量"（弗朗西斯·培根）是一句名言。</div>
                    </div>

                    <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">标题中的括号</div>
                        <div class="keyword text-lg mb-1">The article "Climate Change (A Global Challenge)" was published today.</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə ˈɑːrtɪkəl "ˈklaɪmət tʃeɪndʒ (ə ˈɡloʊbəl ˈtʃælɪndʒ)" wʌz ˈpʌblɪʃt təˈdeɪ/</div>
                        <div class="text-gray-700">文章《气候变化（全球挑战）》今天发表了。</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 括号在现代写作中的趋势 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4">括号在现代写作中的发展趋势</h2>

            <div class="mb-6">
                <h3 class="text-xl font-semibold mb-3">简化和效率导向</h3>
                <p class="mb-4">现代写作趋向于更简洁的表达方式，括号的使用也在发生变化。</p>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div class="card bg-sky-50 border border-sky-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">简化表达</div>
                        <div class="keyword text-lg mb-1">Meeting at 2 PM (Room 101).</div>
                        <div class="text-sm text-gray-600 mb-1">/ˈmiːtɪŋ æt tuː piː em (ruːm wʌn oʊ wʌn)/</div>
                        <div class="text-gray-700">下午2点开会（101室）。</div>
                    </div>

                    <div class="card bg-stone-50 border border-stone-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">快速标注</div>
                        <div class="keyword text-lg mb-1">Call John (urgent) about the project.</div>
                        <div class="text-sm text-gray-600 mb-1">/kɔːl dʒɑːn (ˈɜːrdʒənt) əˈbaʊt ðə ˈprɑːdʒekt/</div>
                        <div class="text-gray-700">给约翰打电话（紧急）关于项目。</div>
                    </div>

                    <div class="card bg-neutral-50 border border-neutral-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">状态更新</div>
                        <div class="keyword text-lg mb-1">Project completed (ahead of schedule).</div>
                        <div class="text-sm text-gray-600 mb-1">/ˈprɑːdʒekt kəmˈpliːtəd (əˈhed ʌv ˈskedʒuːl)/</div>
                        <div class="text-gray-700">项目完成（提前完成）。</div>
                    </div>

                    <div class="card bg-blue-50 border border-blue-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">快速备注</div>
                        <div class="keyword text-lg mb-1">Email sent (copy to manager).</div>
                        <div class="text-sm text-gray-600 mb-1">/ˈiːmeɪl sent (ˈkɑːpi tuː ˈmænədʒər)/</div>
                        <div class="text-gray-700">邮件已发送（抄送给经理）。</div>
                    </div>
                </div>
            </div>

            <div class="mb-6">
                <h3 class="text-xl font-semibold mb-3">多媒体环境中的适应</h3>
                <p class="mb-4">在多媒体和互动环境中，括号的功能在扩展。</p>

                <div class="grid grid-cols-1 gap-4 mb-4">
                    <div class="card bg-green-50 border border-green-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">视频字幕</div>
                        <div class="keyword text-lg mb-1">Welcome to our channel (subscribe for more content).</div>
                        <div class="text-sm text-gray-600 mb-1">/ˈwelkəm tuː ˈaʊər ˈtʃænəl (səbˈskraɪb fɔːr mɔːr ˈkɑːntent)/</div>
                        <div class="text-gray-700">欢迎来到我们的频道（订阅获取更多内容）。</div>
                    </div>

                    <div class="card bg-yellow-50 border border-yellow-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">互动提示</div>
                        <div class="keyword text-lg mb-1">Click here to continue (or press Enter).</div>
                        <div class="text-sm text-gray-600 mb-1">/klɪk hɪr tuː kənˈtɪnjuː (ɔːr pres ˈentər)/</div>
                        <div class="text-gray-700">点击这里继续（或按回车键）。</div>
                    </div>

                    <div class="card bg-purple-50 border border-purple-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">音频描述</div>
                        <div class="keyword text-lg mb-1">The sound you hear (background music) sets the mood.</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə saʊnd juː hɪr (ˈbækɡraʊnd ˈmjuːzɪk) sets ðə muːd/</div>
                        <div class="text-gray-700">你听到的声音（背景音乐）营造了氛围。</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 括号使用的最佳实践 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4">括号使用的最佳实践和建议</h2>

            <div class="mb-6">
                <h3 class="text-xl font-semibold mb-3">清晰性原则</h3>
                <p class="mb-4">使用括号时应始终以提高文本清晰度为目标。</p>

                <div class="grid grid-cols-1 gap-4 mb-4">
                    <div class="card bg-orange-50 border border-orange-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">避免歧义</div>
                        <div class="keyword text-lg mb-1">The meeting (scheduled for Monday) has been moved to Tuesday.</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə ˈmiːtɪŋ (ˈskedʒuːld fɔːr ˈmʌndeɪ) hæz bɪn muːvd tuː ˈtuːzdeɪ/</div>
                        <div class="text-gray-700">会议（原定周一）已改到周二。</div>
                    </div>

                    <div class="card bg-teal-50 border border-teal-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">提供必要信息</div>
                        <div class="keyword text-lg mb-1">Contact Sarah (extension 1234) for technical support.</div>
                        <div class="text-sm text-gray-600 mb-1">/ˈkɑːntækt ˈserə (ɪkˈstenʃən ˈtwelv ˈθɜːrti fɔːr) fɔːr ˈteknɪkəl səˈpɔːrt/</div>
                        <div class="text-gray-700">联系Sarah（分机1234）获取技术支持。</div>
                    </div>

                    <div class="card bg-pink-50 border border-pink-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">保持简洁</div>
                        <div class="keyword text-lg mb-1">The report (due Friday) should include all data.</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə rɪˈpɔːrt (duː ˈfraɪdeɪ) ʃʊd ɪnˈkluːd ɔːl ˈdeɪtə/</div>
                        <div class="text-gray-700">报告（周五截止）应包含所有数据。</div>
                    </div>
                </div>
            </div>

            <div class="mb-6">
                <h3 class="text-xl font-semibold mb-3">一致性原则</h3>
                <p class="mb-4">在同一文档中保持括号使用的一致性和规范性。</p>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                    <div class="card bg-indigo-50 border border-indigo-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">格式统一</div>
                        <div class="keyword text-lg mb-1">All meetings (9 AM, 2 PM, 4 PM) are in Conference Room A.</div>
                        <div class="text-sm text-gray-600 mb-1">/ɔːl ˈmiːtɪŋz (naɪn eɪ em, tuː piː em, fɔːr piː em) ɑːr ɪn ˈkɑːnfərəns ruːm eɪ/</div>
                        <div class="text-gray-700">所有会议（上午9点，下午2点，下午4点）都在会议室A。</div>
                    </div>

                    <div class="card bg-cyan-50 border border-cyan-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">风格一致</div>
                        <div class="keyword text-lg mb-1">Team members: John (manager), Sarah (developer), Mike (designer).</div>
                        <div class="text-sm text-gray-600 mb-1">/tiːm ˈmembərz: dʒɑːn (ˈmænədʒər), ˈserə (dɪˈveləpər), maɪk (dɪˈzaɪnər)/</div>
                        <div class="text-gray-700">团队成员：约翰（经理），萨拉（开发者），迈克（设计师）。</div>
                    </div>

                    <div class="card bg-lime-50 border border-lime-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">标准化表达</div>
                        <div class="keyword text-lg mb-1">Prices: Basic ($10), Premium ($20), Enterprise ($50).</div>
                        <div class="text-sm text-gray-600 mb-1">/ˈpraɪsəz: ˈbeɪsɪk (ten ˈdɑːlərz), ˈpriːmiəm (ˈtwenti ˈdɑːlərz), ˈentərpraɪz (ˈfɪfti ˈdɑːlərz)/</div>
                        <div class="text-gray-700">价格：基础版（10美元），高级版（20美元），企业版（50美元）。</div>
                    </div>

                    <div class="card bg-amber-50 border border-amber-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">规范使用</div>
                        <div class="keyword text-lg mb-1">Deadlines: Phase 1 (March 15), Phase 2 (April 30), Phase 3 (June 15).</div>
                        <div class="text-sm text-gray-600 mb-1">/ˈdedlaɪnz: feɪz wʌn (mɑːrtʃ ˈfɪfˈtiːn), feɪz tuː (ˈeɪprəl ˈθɜːrti), feɪz θriː (dʒuːn ˈfɪfˈtiːn)/</div>
                        <div class="text-gray-700">截止日期：第一阶段（3月15日），第二阶段（4月30日），第三阶段（6月15日）。</div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 总结性示例 -->
        <section class="mb-8">
            <h2 class="text-2xl font-bold mb-4">综合应用示例</h2>

            <div class="mb-6">
                <h3 class="text-xl font-semibold mb-3">复杂文档中的括号运用</h3>
                <p class="mb-4">在实际应用中，括号经常需要与多种信息类型配合使用。</p>

                <div class="grid grid-cols-1 gap-4 mb-4">
                    <div class="card bg-violet-50 border border-violet-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">综合商务文档</div>
                        <div class="keyword text-lg mb-1">The quarterly report (Q4 2023) shows revenue of $2.5M (15% increase from Q3).</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə ˈkwɔːrtərli rɪˈpɔːrt (kjuː fɔːr ˈtuː ˈθaʊzənd ˈtwenti θriː) ʃoʊz ˈrevəˌnuː ʌv tuː pɔɪnt faɪv ˈmɪljən ˈdɑːlərz (ˈfɪfˈtiːn pərˈsent ɪnˈkriːs frʌm kjuː θriː)/</div>
                        <div class="text-gray-700">季度报告（2023年第四季度）显示收入为250万美元（比第三季度增长15%）。</div>
                    </div>

                    <div class="card bg-rose-50 border border-rose-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">学术研究摘要</div>
                        <div class="keyword text-lg mb-1">The study (n=500, conducted 2022-2023) examined the effects of exercise [see methodology in Appendix B].</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə ˈstʌdi (en ˈiːkwəlz faɪv ˈhʌndrəd, kənˈdʌktəd ˈtuː ˈθaʊzənd ˈtwenti tuː tu ˈtuː ˈθaʊzənd ˈtwenti θriː) ɪɡˈzæmənd ðə ɪˈfekts ʌv ˈeksərˌsaɪz [siː ˌmeθəˈdɑːlədʒi ɪn əˈpendɪks biː]/</div>
                        <div class="text-gray-700">这项研究（n=500，2022-2023年进行）检验了运动的效果[方法论见附录B]。</div>
                    </div>

                    <div class="card bg-emerald-50 border border-emerald-200 rounded-lg p-4 shadow-sm">
                        <div class="text-sm text-gray-500 mb-1">技术规格说明</div>
                        <div class="keyword text-lg mb-1">The server (Intel Xeon processor, 32GB RAM) supports up to 1000 concurrent users (tested under normal load conditions).</div>
                        <div class="text-sm text-gray-600 mb-1">/ðə ˈsɜːrvər (ˈɪntəl ˈziːɑːn ˈprɑːsesər, ˈθɜːrti tuː ˈdʒɪɡəˌbaɪts ræm) səˈpɔːrts ʌp tuː wʌn ˈθaʊzənd kənˈkɜːrənt ˈjuːzərz (ˈtestəd ˈʌndər ˈnɔːrməl loʊd kənˈdɪʃənz)/</div>
                        <div class="text-gray-700">服务器（英特尔至强处理器，32GB内存）支持多达1000个并发用户（在正常负载条件下测试）。</div>
                    </div>
                </div>
            </div>
        </section>
    </div>
</body>
</html>
