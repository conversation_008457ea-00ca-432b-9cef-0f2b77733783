# 英语课程学习平台 - 设计系统文档

## 🎨 设计理念

### 核心原则
- **简洁优雅**：去除不必要的装饰，专注于内容本身
- **一致性**：统一的视觉语言和交互模式
- **可访问性**：良好的对比度和清晰的层次结构
- **响应式**：在所有设备上都有优秀的体验

### 设计目标
- 降低学习者的认知负担
- 提供清晰的信息层次
- 营造专业而友好的学习氛围
- 确保内容的可读性和易用性

## 🎯 视觉风格

### 整体风格
- **现代简约**：干净的线条，充足的留白
- **卡片化设计**：信息模块化，层次清晰
- **柔和圆角**：12px圆角，营造友好感
- **微妙阴影**：轻量级阴影增强层次感

### 设计语言
- 以内容为中心的布局
- 功能性优先的交互设计
- 渐进式信息展示
- 一致的视觉反馈

## 🎨 色彩系统

### 主色调
```css
/* 中性色 */
--slate-50: #f8fafc;   /* 背景色 */
--slate-100: #f1f5f9;  /* 浅灰背景 */
--slate-200: #e2e8f0;  /* 边框色 */
--slate-600: #475569;  /* 次要文字 */
--slate-800: #1e293b;  /* 主要文字 */

/* 白色 */
--white: #ffffff;      /* 卡片背景 */
```

### 主题色彩系统
每个课程都有独特的主题色，用于区分和品牌化：

#### 蓝色主题（基础课程）
```css
--blue-50: #eff6ff;    /* 浅色背景 */
--blue-600: #2563eb;   /* 主色 */
--blue-800: #1e40af;   /* 深色文字 */
```

#### 紫色主题（进阶课程）
```css
--purple-50: #faf5ff;  /* 浅色背景 */
--purple-600: #9333ea; /* 主色 */
--purple-800: #6b21a8; /* 深色文字 */
```

#### 绿色主题（实用课程）
```css
--green-50: #f0fdf4;   /* 浅色背景 */
--green-600: #16a34a;  /* 主色 */
--green-800: #166534;  /* 深色文字 */
```

#### 橙色主题（高级课程）
```css
--orange-50: #fff7ed;  /* 浅色背景 */
--orange-600: #ea580c; /* 主色 */
--orange-800: #c2410c; /* 深色文字 */
```

### 色彩应用原则
- **主题色**：用于强调和品牌识别
- **中性色**：用于文字、边框和背景
- **语义色**：用于状态反馈（成功、警告、错误）
- **对比度**：确保文字与背景有足够对比度

## 📝 字体系统

### 字体族
```css
font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
```

### 字体大小层级
```css
/* 标题层级 */
--text-4xl: 2.25rem;   /* 36px - 主标题 */
--text-3xl: 1.875rem;  /* 30px - 二级标题 */
--text-2xl: 1.5rem;    /* 24px - 三级标题 */
--text-xl: 1.25rem;    /* 20px - 四级标题 */
--text-lg: 1.125rem;   /* 18px - 大号文字 */

/* 正文层级 */
--text-base: 1rem;     /* 16px - 正文 */
--text-sm: 0.875rem;   /* 14px - 小号文字 */
--text-xs: 0.75rem;    /* 12px - 辅助文字 */
```

### 字重系统
```css
--font-normal: 400;    /* 正文 */
--font-medium: 500;    /* 强调 */
--font-semibold: 600;  /* 小标题 */
--font-bold: 700;      /* 标题 */
```

### 行高系统
```css
--leading-tight: 1.25;    /* 标题行高 */
--leading-normal: 1.5;    /* 正文行高 */
--leading-relaxed: 1.625; /* 舒适行高 */
```

## 📐 间距系统

### 间距单位
基于 4px 网格系统：
```css
--space-1: 0.25rem;  /* 4px */
--space-2: 0.5rem;   /* 8px */
--space-3: 0.75rem;  /* 12px */
--space-4: 1rem;     /* 16px */
--space-5: 1.25rem;  /* 20px */
--space-6: 1.5rem;   /* 24px */
--space-8: 2rem;     /* 32px */
--space-12: 3rem;    /* 48px */
--space-16: 4rem;    /* 64px */
--space-24: 6rem;    /* 96px */
```

### 应用规则
- **内边距**：p-4 (16px), p-5 (20px), p-6 (24px)
- **外边距**：mb-3 (12px), mb-4 (16px), mb-6 (24px)
- **网格间距**：gap-6 (24px), gap-8 (32px)
- **容器边距**：px-8 (32px), lg:px-16 (64px)

## 🎯 组件设计

### 卡片组件
```css
.solid-card {
  background: #ffffff;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
}
```

**特点：**
- 白色背景，轻微阴影
- 12px圆角，现代感
- 1px边框，清晰边界
- 悬停时阴影加深

### 按钮组件
```css
.button-primary {
  padding: 12px 16px;
  border-radius: 8px;
  font-weight: 500;
  transition: all 200ms;
}
```

**状态：**
- **默认**：主题色背景，白色文字
- **悬停**：背景色加深，轻微提升
- **激活**：背景色更深，无提升

### 输入组件
```css
.input-field {
  padding: 12px 16px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background: #ffffff;
}
```

## 📱 响应式设计

### 断点系统
```css
/* 移动端优先 */
@media (min-width: 640px)  { /* sm */ }
@media (min-width: 768px)  { /* md */ }
@media (min-width: 1024px) { /* lg */ }
@media (min-width: 1280px) { /* xl */ }
@media (min-width: 1536px) { /* 2xl */ }
```

### 网格系统
- **移动端**：1列布局
- **平板端**：2-3列布局
- **桌面端**：4列布局
- **大屏幕**：保持4列，增加间距

### 间距适配
```css
/* 容器内边距 */
px-8 lg:px-16 xl:px-20 2xl:px-32

/* 组件间距 */
gap-6 lg:gap-8 xl:gap-10
```

## 🎭 动画系统

### 过渡动画
```css
.transition-all {
  transition: all 200ms ease-in-out;
}

.transition-colors {
  transition: color 200ms, background-color 200ms;
}
```

### 悬停效果
```css
.hover-lift:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}
```

### 入场动画
```css
.fade-in {
  animation: fadeIn 600ms ease-out;
}

.slide-up {
  animation: slideUp 600ms ease-out;
}
```

## 🏗️ 布局系统

### 首页布局
```
Header (py-16)
├── 标题和描述
└── 居中对齐

Main (py-16)
├── 课程网格 (4列)
└── 响应式适配

Footer (mt-24 pb-16)
└── 版权信息
```

### 详情页布局
```
Container (min-h-screen)
├── 左侧边栏 (w-80)
│   ├── 返回按钮
│   ├── 课程信息
│   └── 章节列表
└── 右侧内容 (flex-1)
    ├── 章节标题
    └── 内容区域 (h-screen)
```

## 🎨 主题应用

### 课程主题色
每个课程根据其类型和难度使用不同主题色：

1. **理解基本句子结构** - 蓝色（基础、稳重）
2. **掌握动词时态** - 紫色（进阶、活力）
3. **学会连词与介词** - 绿色（实用、自然）
4. **掌握发音规则** - 橙色（技能、温暖）
5. **现代口语表达** - 紫色（现代、时尚）
6. **文化与实用技巧** - 蓝色（专业、可信）

### 主题色应用
- **课程封面**：使用主题色作为背景
- **等级标识**：使用主题色的浅色版本
- **章节序号**：使用主题色强调
- **激活状态**：使用主题色表示选中

## 🔧 技术实现

### CSS框架
- **Tailwind CSS**：原子化CSS框架
- **响应式设计**：移动端优先
- **暗色模式**：预留支持

### 组件架构
- **React组件**：模块化设计
- **Props传递**：主题色配置
- **状态管理**：本地状态为主

### 性能优化
- **CSS优化**：使用Tailwind的purge功能
- **图片优化**：响应式图片加载
- **动画优化**：使用transform和opacity

## 📋 设计检查清单

### 视觉一致性
- [ ] 颜色使用符合主题系统
- [ ] 字体大小遵循层级系统
- [ ] 间距使用标准单位
- [ ] 圆角和阴影保持一致

### 交互体验
- [ ] 悬停状态清晰可见
- [ ] 点击反馈及时准确
- [ ] 加载状态友好提示
- [ ] 错误处理用户友好

### 响应式适配
- [ ] 移动端布局合理
- [ ] 平板端体验良好
- [ ] 桌面端充分利用空间
- [ ] 大屏幕不过度拉伸

### 可访问性
- [ ] 颜色对比度符合标准
- [ ] 文字大小易于阅读
- [ ] 交互元素易于操作
- [ ] 键盘导航支持

## 🎯 课程详情页面优化

### 设计统一性
课程详情页面现已与首页保持完全一致的设计风格：

#### 主题色彩应用
- **动态主题色**：根据课程类型自动应用对应主题色
- **一致的色彩语言**：封面、按钮、标识使用统一色彩
- **视觉连贯性**：从首页到详情页的无缝过渡

#### 返回按钮设计
```jsx
<button className="flex items-center space-x-3 text-blue-600 hover:bg-slate-50 px-4 py-3 rounded-lg mb-6 transition-all duration-200 hover-lift border border-slate-200 hover:border-slate-300 w-full justify-center font-medium">
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
  </svg>
  <span>返回首页</span>
</button>
```

**特点：**
- 全宽按钮，视觉突出
- 主题色文字，保持一致性
- 悬停效果，良好反馈
- 图标+文字，清晰表意

#### 布局优化
- **左侧边栏**：固定宽度320px，包含课程信息和章节列表
- **右侧内容**：自适应宽度，章节内容区域
- **高度控制**：内容区域高度固定为浏览器视窗高度
- **滚动优化**：独立滚动区域，避免整页滚动

#### 章节内容区域
```css
style={{ height: 'calc(100vh - 280px)', minHeight: '600px' }}
```

**特点：**
- 动态高度计算，适应不同屏幕
- 最小高度保证，确保内容可读
- 独立滚动，不影响侧边栏
- 无边框设计，内容沉浸

### 组件增强

#### 课程信息卡片
- **正方形封面**：与首页卡片保持一致
- **等级标识**：使用主题色浅色版本
- **统计信息**：显示章节数和学习时长
- **主题色应用**：边框和强调色使用课程主题色

#### 章节卡片
- **主题色适配**：激活状态使用课程主题色
- **一致的交互**：与首页卡片相同的悬停效果
- **清晰的状态**：激活/非激活状态对比明显
- **信息层次**：标题、描述的层次清晰

### 响应式优化
- **移动端**：左侧边栏可折叠
- **平板端**：保持双栏布局
- **桌面端**：充分利用屏幕空间
- **大屏幕**：内容不过度拉伸

---

**当前版本**：v1.0
**最后更新**：2024年
**访问地址**：http://localhost:9001

这套设计系统确保了整个平台的视觉一致性和用户体验的连贯性！
