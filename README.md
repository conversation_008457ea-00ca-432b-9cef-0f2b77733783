# 英语课程学习平台

一个现代化的英语学习平台，采用React + Vite + TailwindCSS构建，具有响应式设计和优美的动画效果。

## 🚀 特性

- **现代设计**: 采用毛玻璃效果和新拟态设计风格
- **响应式布局**: 完美适配PC端和移动端
- **动画效果**: 流畅的页面切换和交互动画
- **模块化内容**: 通过JSON配置课程和章节内容
- **易于扩展**: 简单添加新课程和章节

## 📁 项目结构

```
EnglishCourse/
├── public/
│   ├── chapters/           # 章节HTML页面
│   └── images/            # 课程封面图片
├── src/
│   ├── components/        # React组件
│   │   ├── CourseCard.jsx # 课程卡片组件
│   │   └── ChapterCard.jsx # 章节卡片组件
│   ├── pages/            # 页面组件
│   │   ├── HomePage.jsx   # 首页
│   │   └── ChapterPage.jsx # 章节页面
│   ├── data/             # 数据文件
│   │   └── courses.json   # 课程配置
│   └── styles/           # 样式文件
├── package.json
└── vite.config.js
```

## 🛠️ 安装和运行

1. **安装依赖**
   ```bash
   npm install
   ```

2. **启动开发服务器**
   ```bash
   npm run dev
   ```

3. **构建生产版本**
   ```bash
   npm run build
   ```

## 📝 内容管理

### 添加新课程

编辑 `src/data/courses.json` 文件：

```json
{
  "courses": [
    {
      "id": 4,
      "title": "新课程标题",
      "description": "课程描述",
      "cover": "/images/new-course.jpg",
      "chapters": [
        {
          "id": "4.1",
          "title": "章节标题",
          "description": "章节描述",
          "htmlFile": "/chapters/new-chapter.html"
        }
      ]
    }
  ]
}
```

### 添加新章节

1. 在 `public/chapters/` 目录下创建HTML文件
2. 在课程配置中添加章节信息
3. HTML文件会自动嵌入到章节页面中

### 章节HTML模板

```html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>章节标题</title>
    <style>
        /* 样式代码 */
    </style>
</head>
<body>
    <div class="content">
        <!-- 章节内容 -->
    </div>
</body>
</html>
```

## 🎨 设计风格

- **毛玻璃效果**: 使用 `backdrop-filter: blur()` 创建透明感
- **新拟态设计**: 柔和的阴影和圆角
- **渐变背景**: 多层次的颜色渐变
- **动画交互**: 悬停和点击效果

## 📱 响应式设计

- **移动端**: 单列布局，触摸友好
- **平板端**: 双列布局，适中间距
- **桌面端**: 三列布局，充分利用空间

## 🔧 技术栈

- **前端框架**: React 18
- **构建工具**: Vite
- **样式框架**: TailwindCSS
- **动画库**: Framer Motion
- **路由**: React Router
- **开发语言**: JavaScript (ES6+)

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目！

## 📞 联系

如有问题，请通过GitHub Issues联系。
